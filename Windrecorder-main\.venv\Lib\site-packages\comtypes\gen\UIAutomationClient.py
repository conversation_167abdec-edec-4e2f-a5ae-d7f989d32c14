from enum import IntFlag

import comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 as __wrapper_module__
from comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 import (
    UIA_WindowIsModalPropertyId, UIA_ThumbControlTypeId,
    DockPosition_Top, RowOrColumnMajor_ColumnMajor,
    UIA_SelectionCanSelectMultiplePropertyId, DockPosition_Right,
    UIA_Window_WindowOpenedEventId, UIA_WindowCanMinimizePropertyId,
    IUIAutomation, IUIAutomationTogglePattern,
    UIA_AppBarControlTypeId, SynchronizedInputType_KeyUp,
    UIA_ToolTipControlTypeId, UIA_Drag_DragCompleteEventId,
    RowOrColumnMajor_RowMajor, AnnotationType_UnsyncedChange,
    SynchronizedInputType_LeftMouseUp, RowOrColumnMajor_Indeterminate,
    UIA_MarginTopAttributeId, UIA_IsValuePatternAvailablePropertyId,
    UIA_IsSynchronizedInputPatternAvailablePropertyId,
    UIA_SelectionActiveEndAttributeId, UIA_TreeControlTypeId,
    UIA_ScrollItemPatternId, UIA_MenuItemControlTypeId,
    UIA_SelectionPatternId, HeadingLevel4, IUIAutomationInvokePattern,
    IUIAutomationTextRange, UIA_StylesShapePropertyId,
    IUIAutomationMultipleViewPattern,
    UIA_IsRangeValuePatternAvailablePropertyId, StyleId_Heading9,
    UIA_AccessKeyPropertyId, AnnotationType_InsertionChange,
    TreeScope_None, ProviderOptions_ClientSideProvider,
    UIA_LandmarkTypePropertyId, IUIAutomationAnnotationPattern,
    IUIAutomationElementArray, UIA_SpinnerControlTypeId,
    StructureChangeType_ChildRemoved, Assertive,
    UIA_TableControlTypeId, AnnotationType_GrammarError,
    HeadingLevel_None, UIA_DockDockPositionPropertyId,
    IUIAutomationDockPattern, OrientationType_Horizontal,
    TextUnit_Paragraph, UIA_TextPatternId, AnnotationType_Comment,
    StyleId_Heading7, IUIAutomationEventHandler,
    UIA_Transform2CanZoomPropertyId,
    UIA_LegacyIAccessibleHelpPropertyId, UIA_GroupControlTypeId,
    TextEditChangeType_AutoCorrect, UIA_OverlineStyleAttributeId,
    DockPosition_Left, UIA_Selection2ItemCountPropertyId,
    UIA_IsGridItemPatternAvailablePropertyId, UIA_InvokePatternId,
    TreeScope_Subtree, IUIAutomationAndCondition, IUIAutomation3,
    UIA_ScrollVerticalViewSizePropertyId, UIA_Drag_DragStartEventId,
    UIA_RotationPropertyId, SynchronizedInputType_KeyDown,
    UIA_HelpTextPropertyId, UIA_SynchronizedInputPatternId,
    StyleId_Heading2, DockPosition_Bottom,
    AnnotationType_DataValidationError, UIA_WindowIsTopmostPropertyId,
    StyleId_BulletedList, NotificationKind_ActionAborted,
    CoalesceEventsOptions_Disabled, UIA_StyleNameAttributeId,
    UIA_OutlineStylesAttributeId, UIA_DragPatternId,
    UIA_TextFlowDirectionsAttributeId, UIA_AutomationIdPropertyId,
    IUIAutomation4, ZoomUnit_SmallDecrement,
    NavigateDirection_FirstChild, HRESULT, CUIAutomation8,
    UIA_Invoke_InvokedEventId, UIA_SayAsInterpretAsMetadataId,
    UIA_IsEnabledPropertyId,
    NotificationProcessing_ImportantMostRecent,
    UIA_ToolTipClosedEventId, UIA_AnnotationAuthorPropertyId,
    SupportedTextSelection_None, AnnotationType_DeletionChange,
    StyleId_NumberedList, UIA_ScrollHorizontalViewSizePropertyId,
    UIA_SemanticZoomControlTypeId, UIA_MenuOpenedEventId,
    UIA_HeaderItemControlTypeId, UIA_InputReachedOtherElementEventId,
    IUIAutomationElement, UIA_TableItemColumnHeaderItemsPropertyId,
    UIA_SelectionPattern2Id, StyleId_Heading8,
    UIA_TransformPattern2Id, UIA_MenuBarControlTypeId,
    DockPosition_None, HeadingLevel2, UIA_SystemAlertEventId,
    UIA_MultipleViewSupportedViewsPropertyId,
    UIA_StrikethroughStyleAttributeId, UIA_InputReachedTargetEventId,
    UIA_GridItemRowSpanPropertyId,
    WindowInteractionState_NotResponding, tagRECT, StyleId_Title,
    UIA_ProgressBarControlTypeId, UIA_ValuePatternId,
    UIA_CustomControlTypeId,
    UIA_IsMultipleViewPatternAvailablePropertyId,
    ConnectionRecoveryBehaviorOptions_Enabled, AnnotationType_Endnote,
    UIA_OptimizeForVisualContentPropertyId, Off,
    TreeTraversalOptions_LastToFirstOrder,
    IUIAutomationExpandCollapsePattern, UIA_DockPatternId,
    UIA_RangeValueSmallChangePropertyId,
    UIA_IndentationFirstLineAttributeId, UIA_PositionInSetPropertyId,
    UIA_HorizontalTextAlignmentAttributeId,
    UIA_Transform2ZoomMinimumPropertyId, Polite,
    UIA_DropTarget_DragEnterEventId, TextEditChangeType_AutoComplete,
    BSTR, UIA_Drag_DragCancelEventId, UIA_MenuModeStartEventId,
    UIA_IsScrollItemPatternAvailablePropertyId,
    IUIAutomationScrollPattern, UIA_GridItemRowPropertyId,
    UIA_TablePatternId, UIA_HyperlinkControlTypeId,
    AnnotationType_ExternalChange, StyleId_Subtitle,
    UIA_StatusBarControlTypeId, UIA_ButtonControlTypeId,
    UIA_LegacyIAccessibleChildIdPropertyId,
    UIA_GridItemContainingGridPropertyId,
    ProviderOptions_ProviderOwnsSetFocus,
    TextEditChangeType_CompositionFinalized,
    NotificationProcessing_All,
    UIA_IsSpreadsheetPatternAvailablePropertyId,
    UIA_StyleIdAttributeId, UIA_IsStylesPatternAvailablePropertyId,
    UIA_IsSelectionPatternAvailablePropertyId,
    UIA_IsDragPatternAvailablePropertyId, AnnotationType_Unknown,
    IUIAutomationTablePattern, AnnotationType_Author,
    IUIAutomationStylesPattern, UIA_AnnotationPatternId,
    UIA_IsSuperscriptAttributeId,
    SynchronizedInputType_RightMouseDown,
    UIA_DropTarget_DragLeaveEventId, IUIAutomationGridItemPattern,
    UIA_IsCustomNavigationPatternAvailablePropertyId,
    UIA_TreeItemControlTypeId, UIA_EditControlTypeId,
    UIA_TabControlTypeId, UIA_ValueValuePropertyId,
    UIA_MarginBottomAttributeId, IUIAutomationTextEditPattern,
    IUIAutomationTableItemPattern,
    UIA_IsDropTargetPatternAvailablePropertyId,
    ProviderOptions_UseClientCoordinates, UIA_IsItalicAttributeId,
    UIA_IsRequiredForFormPropertyId,
    SynchronizedInputType_RightMouseUp, CoalesceEventsOptions_Enabled,
    HeadingLevel6, UIA_IsLegacyIAccessiblePatternAvailablePropertyId,
    IUIAutomationElement5, StructureChangeType_ChildrenBulkRemoved,
    UIA_GridItemColumnPropertyId, ZoomUnit_SmallIncrement,
    UIA_UnderlineStyleAttributeId, UIA_ListControlTypeId,
    UIA_WindowPatternId, IUIAutomationValuePattern,
    UIA_ControlTypePropertyId, IUIAutomationCacheRequest,
    UIA_IsReadOnlyAttributeId,
    UIA_ScrollHorizontallyScrollablePropertyId, IUIAutomation2,
    UIA_IsSelectionItemPatternAvailablePropertyId, CUIAutomation,
    UIA_OrientationPropertyId, IUIAutomationEventHandlerGroup,
    UIA_IndentationLeadingAttributeId, ExpandCollapseState_Expanded,
    UIA_IsDialogPropertyId, UIA_TextChildPatternId, TextUnit_Word,
    IUIAutomationProxyFactoryEntry,
    UIA_ExpandCollapseExpandCollapseStatePropertyId,
    UIA_MultipleViewCurrentViewPropertyId, UIA_ScrollPatternId,
    IUIAutomationProxyFactoryMapping,
    UIA_TableRowOrColumnMajorPropertyId,
    ProviderOptions_NonClientAreaProvider,
    UIA_IsTextChildPatternAvailablePropertyId,
    UIA_Text_TextSelectionChangedEventId,
    UIA_ProviderDescriptionPropertyId, UIA_IsContentElementPropertyId,
    UIA_AnnotationDateTimePropertyId, _check_version,
    WindowVisualState_Minimized, IUIAutomationElement8,
    TextEditChangeType_Composition, UIA_RadioButtonControlTypeId,
    UIA_CustomNavigationPatternId, UIA_CulturePropertyId,
    IUIAutomationVirtualizedItemPattern,
    UIA_ForegroundColorAttributeId, _midlSAFEARRAY,
    UIA_IsTransformPattern2AvailablePropertyId,
    IUIAutomationRangeValuePattern,
    UIA_StylesExtendedPropertiesPropertyId, DockPosition_Fill,
    IUIAutomationWindowPattern, UIA_DocumentControlTypeId,
    UIA_ChangesEventId, StructureChangeType_ChildrenInvalidated,
    UIA_TogglePatternId, UIA_AfterParagraphSpacingAttributeId,
    UIA_CustomLandmarkTypeId, UIA_WindowWindowVisualStatePropertyId,
    UIA_TransformCanResizePropertyId,
    UIA_TextEdit_ConversionTargetChangedEventId,
    UIA_AnnotationObjectsPropertyId, NotificationKind_ItemAdded,
    Library, UIA_LegacyIAccessibleKeyboardShortcutPropertyId,
    HeadingLevel3, UIA_LegacyIAccessibleStatePropertyId,
    UIA_StylesStyleIdPropertyId, UIA_ToolTipOpenedEventId,
    IUIAutomationPropertyChangedEventHandler,
    UIA_AutomationFocusChangedEventId,
    UIA_IsInvokePatternAvailablePropertyId,
    UIA_RangeValueMinimumPropertyId, TreeScope_Parent,
    UIA_SpreadsheetPatternId, UIA_SeparatorControlTypeId,
    UIA_ValueIsReadOnlyPropertyId, UIA_GridRowCountPropertyId,
    UIA_IsControlElementPropertyId, UIA_TabsAttributeId,
    UIA_SelectionItemSelectionContainerPropertyId,
    NotificationProcessing_ImportantAll, UIA_MultipleViewPatternId,
    UIA_LocalizedControlTypePropertyId, UIA_ItemStatusPropertyId,
    UIA_AcceleratorKeyPropertyId, UIA_CenterPointPropertyId,
    ConnectionRecoveryBehaviorOptions_Disabled,
    UIA_IsKeyboardFocusablePropertyId, UIA_LevelPropertyId,
    UIA_IsObjectModelPatternAvailablePropertyId,
    SynchronizedInputType_LeftMouseDown, UIA_TextPattern2Id,
    IUIAutomationNotCondition, UIA_AriaRolePropertyId,
    UIA_RuntimeIdPropertyId, UIA_AnnotationTargetPropertyId,
    UIA_SizePropertyId, UIA_ScrollHorizontalScrollPercentPropertyId,
    IUIAutomationProxyFactory, StructureChangeType_ChildrenReordered,
    typelib_path, UIA_TableColumnHeadersPropertyId,
    PropertyConditionFlags_None, TextUnit_Document,
    UIA_IsGridPatternAvailablePropertyId,
    NavigateDirection_PreviousSibling,
    IUIAutomationObjectModelPattern, UIA_ListItemControlTypeId,
    UIA_AnnotationTypesPropertyId,
    IUIAutomationFocusChangedEventHandler,
    UIA_SelectionItem_ElementSelectedEventId, TextUnit_Character,
    NotificationKind_ActionCompleted, ZoomUnit_LargeIncrement,
    UIA_LiveRegionChangedEventId, UIA_HeaderControlTypeId,
    HeadingLevel5, TreeScope_Children, AnnotationType_FormatChange,
    UIA_IsOffscreenPropertyId, UIA_SearchLandmarkTypeId,
    IUIAutomationSelectionPattern,
    UIA_IsItemContainerPatternAvailablePropertyId,
    NotificationProcessing_ImportantCurrentThenMostRecent,
    NotificationProcessing_CurrentThenMostRecent,
    UIA_GridItemColumnSpanPropertyId,
    UIA_IsTogglePatternAvailablePropertyId,
    IUIAutomationTextRangeArray, UIA_DropTarget_DroppedEventId,
    AnnotationType_Sensitive, UIA_MenuControlTypeId,
    UIA_SpreadsheetItemAnnotationTypesPropertyId,
    IUIAutomationStructureChangedEventHandler, _lcid,
    UIA_AnimationStyleAttributeId, UIA_DataItemControlTypeId,
    UIA_IsTransformPatternAvailablePropertyId,
    UIA_DragIsGrabbedPropertyId, WSTRING, UIA_TextEditPatternId,
    IUIAutomationElement2, UIA_TableRowHeadersPropertyId,
    StyleId_Custom, HeadingLevel9, UIA_TransformCanMovePropertyId,
    UIA_ActiveTextPositionChangedEventId,
    ExpandCollapseState_LeafNode, ProviderOptions_ServerSideProvider,
    UIA_IsSpreadsheetItemPatternAvailablePropertyId,
    IUIAutomationSelectionPattern2, UIA_StylesFillColorPropertyId,
    UIA_LabeledByPropertyId, UIA_DescribedByPropertyId,
    UIA_ComboBoxControlTypeId, TextPatternRangeEndpoint_End,
    UIA_DataGridControlTypeId, UIA_LegacyIAccessibleNamePropertyId,
    AnnotationType_Highlighted, UIA_DropTargetPatternId,
    HeadingLevel7, UIA_MenuModeEndEventId,
    UIA_ExpandCollapsePatternId, UIA_CaretPositionAttributeId,
    UIA_IsSelectionPattern2AvailablePropertyId, TextUnit_Format,
    UIA_LinkAttributeId, IUIAutomationPropertyCondition,
    UIA_SpreadsheetItemAnnotationObjectsPropertyId,
    NotificationKind_Other, OrientationType_None,
    UIA_PaneControlTypeId, TreeTraversalOptions_PostOrder,
    UIA_UnderlineColorAttributeId, IUIAutomationScrollItemPattern,
    IUIAutomationSelectionItemPattern, UIA_FillColorPropertyId,
    IUIAutomationGridPattern, UIA_GridItemPatternId,
    UIA_DragGrabbedItemsPropertyId, IUIAutomationTreeWalker,
    TreeTraversalOptions_Default, UIA_SayAsInterpretAsAttributeId,
    UIA_IsWindowPatternAvailablePropertyId, UIA_FontNameAttributeId,
    IUIAutomationActiveTextPositionChangedEventHandler,
    UIA_ScrollVerticalScrollPercentPropertyId,
    UIA_IsAnnotationPatternAvailablePropertyId,
    UIA_NavigationLandmarkTypeId, UIA_FontSizeAttributeId,
    UIA_BoundingRectanglePropertyId, AutomationElementMode_None,
    ExpandCollapseState_PartiallyExpanded, IUIAutomationTextPattern2,
    StyleId_Heading6, UIA_LayoutInvalidatedEventId,
    UIA_TransformCanRotatePropertyId, UIA_CalendarControlTypeId,
    UIA_TabItemControlTypeId, UIA_FillTypePropertyId, TextUnit_Page,
    IAccessible, UIA_IsActiveAttributeId, UIA_ProcessIdPropertyId,
    UIA_WindowWindowInteractionStatePropertyId,
    UIA_TitleBarControlTypeId, IUIAutomationElement4,
    IUIAutomationNotificationEventHandler, IUIAutomationOrCondition,
    ScrollAmount_LargeIncrement, UIA_ClickablePointPropertyId,
    UIA_MenuClosedEventId, UIA_NativeWindowHandlePropertyId,
    IUIAutomation5, UIA_IsTablePatternAvailablePropertyId,
    IUIAutomationTextEditTextChangedEventHandler,
    UIA_IndentationTrailingAttributeId, UIA_NotificationEventId,
    AnnotationType_CircularReferenceError,
    ScrollAmount_LargeDecrement, OrientationType_Vertical,
    UIA_FlowsToPropertyId, IUIAutomationTextChildPattern,
    UIA_SplitButtonControlTypeId, IUIAutomationDragPattern,
    AnnotationType_SpellingError, IUnknown, IUIAutomationElement3,
    UIA_ItemTypePropertyId, NavigateDirection_Parent,
    UIA_TextEdit_TextChangedEventId,
    UIA_LegacyIAccessibleRolePropertyId, UIA_ItemContainerPatternId,
    AnnotationType_Footnote, IUIAutomationCustomNavigationPattern,
    UIA_AnnotationTypesAttributeId,
    UIA_IsTextPatternAvailablePropertyId, UIA_TableItemPatternId,
    UIA_SpreadsheetItemFormulaPropertyId, UIA_CultureAttributeId,
    IUIAutomationSpreadsheetPattern, UIA_FlowsFromPropertyId,
    ZoomUnit_NoAmount, AnnotationType_FormulaError, StyleId_Heading5,
    AutomationElementMode_Full, UIA_Selection_InvalidatedEventId,
    UIA_ScrollVerticallyScrollablePropertyId, StyleId_Heading4,
    UIA_IsVirtualizedItemPatternAvailablePropertyId,
    TreeScope_Element, UIA_IsDockPatternAvailablePropertyId,
    ProviderOptions_UseComThreading, TreeScope_Descendants,
    NavigateDirection_NextSibling, UIA_FontWeightAttributeId,
    NotificationKind_ItemRemoved, IUIAutomationDropTargetPattern,
    UIA_RangeValuePatternId, UIA_SelectionItemIsSelectedPropertyId,
    UIA_Window_WindowClosedEventId,
    UIA_IsTextPattern2AvailablePropertyId,
    UIA_MarginTrailingAttributeId, UIA_RangeValueValuePropertyId,
    UIA_NamePropertyId, UIA_CaretBidiModeAttributeId,
    UIA_ToggleToggleStatePropertyId,
    UIA_RangeValueLargeChangePropertyId, ToggleState_Indeterminate,
    IUIAutomation6, HeadingLevel1, IRawElementProviderSimple,
    IUIAutomationTextRange3, UIA_BackgroundColorAttributeId,
    UIA_GridColumnCountPropertyId,
    UIA_LegacyIAccessibleSelectionPropertyId,
    ProviderOptions_HasNativeIAccessible,
    UIA_DropTargetDropTargetEffectPropertyId,
    UIA_StrikethroughColorAttributeId,
    UIA_IsExpandCollapsePatternAvailablePropertyId,
    TreeScope_Ancestors, UIA_Selection2LastSelectedItemPropertyId,
    IUIAutomationSpreadsheetItemPattern, UIA_LineSpacingAttributeId,
    AnnotationType_AdvancedProofingIssue, UIA_ImageControlTypeId,
    UIA_SelectionIsSelectionRequiredPropertyId,
    UIA_ToolBarControlTypeId, UIA_FrameworkIdPropertyId,
    UIA_DragDropEffectPropertyId,
    StructureChangeType_ChildrenBulkAdded,
    UIA_AnnotationObjectsAttributeId, UIA_SliderControlTypeId,
    UIA_WindowControlTypeId, ScrollAmount_SmallIncrement,
    NavigateDirection_LastChild, UIA_TransformPatternId,
    UIA_Selection2FirstSelectedItemPropertyId,
    WindowInteractionState_Running,
    UIA_SelectionItem_ElementRemovedFromSelectionEventId,
    UIA_IsDataValidForFormPropertyId,
    PropertyConditionFlags_IgnoreCase, TextUnit_Line,
    UIA_LegacyIAccessiblePatternId,
    UIA_SelectionItem_ElementAddedToSelectionEventId,
    UIA_ScrollBarControlTypeId, UIA_Text_TextChangedEventId,
    UIA_LocalizedLandmarkTypePropertyId, UiaChangeInfo,
    UIA_AutomationPropertyChangedEventId,
    UIA_StylesStyleNamePropertyId, StyleId_Heading1,
    UIA_VisualEffectsPropertyId, WindowVisualState_Normal,
    UIA_FormLandmarkTypeId, UIA_SpreadsheetItemPatternId,
    UIA_StylesPatternId, UIA_OutlineThicknessPropertyId,
    UIA_FullDescriptionPropertyId, AnnotationType_EditingLockedChange,
    WindowInteractionState_ReadyForUserInteraction,
    ScrollAmount_SmallDecrement, ExtendedProperty,
    UIA_MainLandmarkTypeId, COMMETHOD,
    UIA_LegacyIAccessibleDescriptionPropertyId, tagPOINT,
    UIA_SelectionItemPatternId, UIA_HeadingLevelPropertyId,
    UIA_CheckBoxControlTypeId, AnnotationType_ConflictingChange,
    UIA_ControllerForPropertyId, IUIAutomationElement9,
    ScrollAmount_NoAmount, UIA_SelectionSelectionPropertyId, dispid,
    IUIAutomationElement7, PropertyConditionFlags_MatchSubstring,
    UIA_IsSubscriptAttributeId, AnnotationType_Header,
    UIA_SizeOfSetPropertyId,
    UIA_IsTableItemPatternAvailablePropertyId, StyleId_Emphasis,
    WindowInteractionState_BlockedByModalWindow,
    UIA_Transform2ZoomLevelPropertyId, UIA_ObjectModelPatternId,
    ExpandCollapseState_Collapsed, UIA_IsPeripheralPropertyId,
    ZoomUnit_LargeDecrement, NotificationProcessing_MostRecent,
    UIA_InputDiscardedEventId, StyleId_Normal, CoClass,
    UIA_BeforeParagraphSpacingAttributeId,
    AnnotationType_TrackChanges, UIA_IsHiddenAttributeId,
    UIA_WindowCanMaximizePropertyId, UIA_TextControlTypeId,
    UIA_StylesFillPatternStylePropertyId,
    IUIAutomationItemContainerPattern,
    UIA_Transform2ZoomMaximumPropertyId, HeadingLevel8,
    ToggleState_Off, UIA_LiveSettingPropertyId,
    ProviderOptions_RefuseNonClientSupport,
    UIA_OverlineColorAttributeId, UIA_CapStyleAttributeId,
    TextEditChangeType_None, UIA_VirtualizedItemPatternId,
    UIA_BulletStyleAttributeId, UIA_HasKeyboardFocusPropertyId,
    StructureChangeType_ChildAdded, WindowInteractionState_Closing,
    IUIAutomationTextRange2, IUIAutomationElement6,
    UIA_RangeValueMaximumPropertyId,
    UIA_TableItemRowHeaderItemsPropertyId, UIA_IsPasswordPropertyId,
    UIA_SummaryChangeId, UIA_DropTargetDropTargetEffectsPropertyId,
    IUIAutomationTransformPattern,
    UIA_IsTextEditPatternAvailablePropertyId,
    UIA_OutlineColorPropertyId, UIA_DragDropEffectsPropertyId,
    TextPatternRangeEndpoint_Start,
    IUIAutomationSynchronizedInputPattern, UIA_GridPatternId,
    SupportedTextSelection_Multiple, AnnotationType_MoveChange,
    ToggleState_On, IUIAutomationTextPattern,
    UIA_StructureChangedEventId, IDispatch,
    UIA_AnnotationAnnotationTypeIdPropertyId,
    UIA_AnnotationAnnotationTypeNamePropertyId,
    UIA_MarginLeadingAttributeId, UIA_AriaPropertiesPropertyId,
    UIA_IsScrollPatternAvailablePropertyId,
    UIA_LegacyIAccessibleDefaultActionPropertyId,
    UIA_HostedFragmentRootsInvalidatedEventId, VARIANT,
    AnnotationType_Mathematics, UIA_RangeValueIsReadOnlyPropertyId,
    IUIAutomationLegacyIAccessiblePattern, IUIAutomationCondition,
    SupportedTextSelection_Single, WindowVisualState_Maximized,
    UIA_ClassNamePropertyId, IUIAutomationChangesEventHandler,
    IUIAutomationBoolCondition, IUIAutomationTransformPattern2, GUID,
    UIA_LegacyIAccessibleValuePropertyId,
    ProviderOptions_OverrideProvider, StyleId_Quote,
    UIA_Selection2CurrentSelectedItemPropertyId,
    AnnotationType_Footer, UIA_StylesFillPatternColorPropertyId,
    StyleId_Heading3, UIA_AsyncContentLoadedEventId
)


class TreeScope(IntFlag):
    TreeScope_None = 0
    TreeScope_Element = 1
    TreeScope_Children = 2
    TreeScope_Descendants = 4
    TreeScope_Parent = 8
    TreeScope_Ancestors = 16
    TreeScope_Subtree = 7


class OrientationType(IntFlag):
    OrientationType_None = 0
    OrientationType_Horizontal = 1
    OrientationType_Vertical = 2


class ProviderOptions(IntFlag):
    ProviderOptions_ClientSideProvider = 1
    ProviderOptions_ServerSideProvider = 2
    ProviderOptions_NonClientAreaProvider = 4
    ProviderOptions_OverrideProvider = 8
    ProviderOptions_ProviderOwnsSetFocus = 16
    ProviderOptions_UseComThreading = 32
    ProviderOptions_RefuseNonClientSupport = 64
    ProviderOptions_HasNativeIAccessible = 128
    ProviderOptions_UseClientCoordinates = 256


class NavigateDirection(IntFlag):
    NavigateDirection_Parent = 0
    NavigateDirection_NextSibling = 1
    NavigateDirection_PreviousSibling = 2
    NavigateDirection_FirstChild = 3
    NavigateDirection_LastChild = 4


class TextPatternRangeEndpoint(IntFlag):
    TextPatternRangeEndpoint_Start = 0
    TextPatternRangeEndpoint_End = 1


class TextUnit(IntFlag):
    TextUnit_Character = 0
    TextUnit_Format = 1
    TextUnit_Word = 2
    TextUnit_Line = 3
    TextUnit_Paragraph = 4
    TextUnit_Page = 5
    TextUnit_Document = 6


class PropertyConditionFlags(IntFlag):
    PropertyConditionFlags_None = 0
    PropertyConditionFlags_IgnoreCase = 1
    PropertyConditionFlags_MatchSubstring = 2


class SupportedTextSelection(IntFlag):
    SupportedTextSelection_None = 0
    SupportedTextSelection_Single = 1
    SupportedTextSelection_Multiple = 2


class ConnectionRecoveryBehaviorOptions(IntFlag):
    ConnectionRecoveryBehaviorOptions_Disabled = 0
    ConnectionRecoveryBehaviorOptions_Enabled = 1


class TextEditChangeType(IntFlag):
    TextEditChangeType_None = 0
    TextEditChangeType_AutoCorrect = 1
    TextEditChangeType_Composition = 2
    TextEditChangeType_CompositionFinalized = 3
    TextEditChangeType_AutoComplete = 4


class StructureChangeType(IntFlag):
    StructureChangeType_ChildAdded = 0
    StructureChangeType_ChildRemoved = 1
    StructureChangeType_ChildrenInvalidated = 2
    StructureChangeType_ChildrenBulkAdded = 3
    StructureChangeType_ChildrenBulkRemoved = 4
    StructureChangeType_ChildrenReordered = 5


class WindowVisualState(IntFlag):
    WindowVisualState_Normal = 0
    WindowVisualState_Maximized = 1
    WindowVisualState_Minimized = 2


class WindowInteractionState(IntFlag):
    WindowInteractionState_Running = 0
    WindowInteractionState_Closing = 1
    WindowInteractionState_ReadyForUserInteraction = 2
    WindowInteractionState_BlockedByModalWindow = 3
    WindowInteractionState_NotResponding = 4


class CoalesceEventsOptions(IntFlag):
    CoalesceEventsOptions_Disabled = 0
    CoalesceEventsOptions_Enabled = 1


class RowOrColumnMajor(IntFlag):
    RowOrColumnMajor_RowMajor = 0
    RowOrColumnMajor_ColumnMajor = 1
    RowOrColumnMajor_Indeterminate = 2


class SynchronizedInputType(IntFlag):
    SynchronizedInputType_KeyUp = 1
    SynchronizedInputType_KeyDown = 2
    SynchronizedInputType_LeftMouseUp = 4
    SynchronizedInputType_LeftMouseDown = 8
    SynchronizedInputType_RightMouseUp = 16
    SynchronizedInputType_RightMouseDown = 32


class LiveSetting(IntFlag):
    Off = 0
    Polite = 1
    Assertive = 2


class TreeTraversalOptions(IntFlag):
    TreeTraversalOptions_Default = 0
    TreeTraversalOptions_PostOrder = 1
    TreeTraversalOptions_LastToFirstOrder = 2


class AutomationElementMode(IntFlag):
    AutomationElementMode_None = 0
    AutomationElementMode_Full = 1


class NotificationKind(IntFlag):
    NotificationKind_ItemAdded = 0
    NotificationKind_ItemRemoved = 1
    NotificationKind_ActionCompleted = 2
    NotificationKind_ActionAborted = 3
    NotificationKind_Other = 4


class NotificationProcessing(IntFlag):
    NotificationProcessing_ImportantAll = 0
    NotificationProcessing_ImportantMostRecent = 1
    NotificationProcessing_All = 2
    NotificationProcessing_MostRecent = 3
    NotificationProcessing_CurrentThenMostRecent = 4
    NotificationProcessing_ImportantCurrentThenMostRecent = 5


class ToggleState(IntFlag):
    ToggleState_Off = 0
    ToggleState_On = 1
    ToggleState_Indeterminate = 2


class DockPosition(IntFlag):
    DockPosition_Top = 0
    DockPosition_Left = 1
    DockPosition_Bottom = 2
    DockPosition_Right = 3
    DockPosition_Fill = 4
    DockPosition_None = 5


class ExpandCollapseState(IntFlag):
    ExpandCollapseState_Collapsed = 0
    ExpandCollapseState_Expanded = 1
    ExpandCollapseState_PartiallyExpanded = 2
    ExpandCollapseState_LeafNode = 3


class ZoomUnit(IntFlag):
    ZoomUnit_NoAmount = 0
    ZoomUnit_LargeDecrement = 1
    ZoomUnit_SmallDecrement = 2
    ZoomUnit_LargeIncrement = 3
    ZoomUnit_SmallIncrement = 4


class ScrollAmount(IntFlag):
    ScrollAmount_LargeDecrement = 0
    ScrollAmount_SmallDecrement = 1
    ScrollAmount_NoAmount = 2
    ScrollAmount_LargeIncrement = 3
    ScrollAmount_SmallIncrement = 4


__all__ = [
    'UIA_WindowIsModalPropertyId', 'UIA_ThumbControlTypeId',
    'DockPosition_Top', 'RowOrColumnMajor_ColumnMajor',
    'UIA_SelectionCanSelectMultiplePropertyId', 'DockPosition_Right',
    'UIA_Window_WindowOpenedEventId',
    'UIA_WindowCanMinimizePropertyId', 'IUIAutomation',
    'IUIAutomationTogglePattern', 'UIA_AppBarControlTypeId',
    'SynchronizedInputType_KeyUp', 'UIA_ToolTipControlTypeId',
    'UIA_Drag_DragCompleteEventId', 'RowOrColumnMajor_RowMajor',
    'AnnotationType_UnsyncedChange',
    'SynchronizedInputType_LeftMouseUp',
    'RowOrColumnMajor_Indeterminate', 'UIA_MarginTopAttributeId',
    'UIA_IsValuePatternAvailablePropertyId', 'SynchronizedInputType',
    'UIA_IsSynchronizedInputPatternAvailablePropertyId',
    'UIA_SelectionActiveEndAttributeId', 'UIA_TreeControlTypeId',
    'UIA_ScrollItemPatternId', 'UIA_MenuItemControlTypeId',
    'UIA_SelectionPatternId', 'HeadingLevel4',
    'IUIAutomationInvokePattern', 'IUIAutomationTextRange',
    'LiveSetting', 'UIA_StylesShapePropertyId',
    'IUIAutomationMultipleViewPattern',
    'UIA_IsRangeValuePatternAvailablePropertyId', 'StyleId_Heading9',
    'ConnectionRecoveryBehaviorOptions', 'UIA_AccessKeyPropertyId',
    'AnnotationType_InsertionChange', 'TreeScope_None',
    'ProviderOptions_ClientSideProvider',
    'UIA_LandmarkTypePropertyId', 'IUIAutomationAnnotationPattern',
    'IUIAutomationElementArray', 'UIA_SpinnerControlTypeId',
    'StructureChangeType_ChildRemoved', 'Assertive',
    'UIA_TableControlTypeId', 'AnnotationType_GrammarError',
    'HeadingLevel_None', 'UIA_DockDockPositionPropertyId',
    'IUIAutomationDockPattern', 'OrientationType_Horizontal',
    'TextUnit_Paragraph', 'UIA_TextPatternId',
    'AnnotationType_Comment', 'StyleId_Heading7',
    'IUIAutomationEventHandler', 'UIA_Transform2CanZoomPropertyId',
    'UIA_LegacyIAccessibleHelpPropertyId', 'UIA_GroupControlTypeId',
    'TextEditChangeType_AutoCorrect', 'UIA_OverlineStyleAttributeId',
    'DockPosition_Left', 'UIA_Selection2ItemCountPropertyId',
    'UIA_IsGridItemPatternAvailablePropertyId', 'UIA_InvokePatternId',
    'TreeScope_Subtree', 'IUIAutomationAndCondition',
    'IUIAutomation3', 'UIA_ScrollVerticalViewSizePropertyId',
    'UIA_Drag_DragStartEventId', 'UIA_RotationPropertyId',
    'SynchronizedInputType_KeyDown', 'UIA_HelpTextPropertyId',
    'UIA_SynchronizedInputPatternId', 'StyleId_Heading2',
    'DockPosition_Bottom', 'AnnotationType_DataValidationError',
    'UIA_WindowIsTopmostPropertyId', 'StyleId_BulletedList',
    'NotificationKind_ActionAborted',
    'CoalesceEventsOptions_Disabled', 'UIA_StyleNameAttributeId',
    'UIA_OutlineStylesAttributeId', 'UIA_DragPatternId',
    'UIA_TextFlowDirectionsAttributeId', 'UIA_AutomationIdPropertyId',
    'IUIAutomation4', 'ZoomUnit_SmallDecrement',
    'NavigateDirection_FirstChild', 'CUIAutomation8',
    'AutomationElementMode', 'UIA_Invoke_InvokedEventId',
    'UIA_SayAsInterpretAsMetadataId', 'UIA_IsEnabledPropertyId',
    'NotificationProcessing_ImportantMostRecent',
    'UIA_ToolTipClosedEventId', 'UIA_AnnotationAuthorPropertyId',
    'SupportedTextSelection_None', 'AnnotationType_DeletionChange',
    'StyleId_NumberedList', 'UIA_ScrollHorizontalViewSizePropertyId',
    'UIA_SemanticZoomControlTypeId', 'UIA_MenuOpenedEventId',
    'UIA_HeaderItemControlTypeId',
    'UIA_InputReachedOtherElementEventId', 'IUIAutomationElement',
    'UIA_TableItemColumnHeaderItemsPropertyId',
    'UIA_SelectionPattern2Id', 'StyleId_Heading8',
    'UIA_TransformPattern2Id', 'UIA_MenuBarControlTypeId',
    'DockPosition_None', 'TextPatternRangeEndpoint', 'HeadingLevel2',
    'UIA_SystemAlertEventId',
    'UIA_MultipleViewSupportedViewsPropertyId',
    'UIA_StrikethroughStyleAttributeId',
    'UIA_InputReachedTargetEventId', 'UIA_GridItemRowSpanPropertyId',
    'WindowInteractionState_NotResponding', 'StyleId_Title',
    'UIA_ProgressBarControlTypeId', 'UIA_ValuePatternId',
    'UIA_CustomControlTypeId',
    'UIA_IsMultipleViewPatternAvailablePropertyId',
    'ConnectionRecoveryBehaviorOptions_Enabled',
    'AnnotationType_Endnote',
    'UIA_OptimizeForVisualContentPropertyId', 'Off',
    'TreeTraversalOptions_LastToFirstOrder',
    'IUIAutomationExpandCollapsePattern', 'UIA_DockPatternId',
    'UIA_RangeValueSmallChangePropertyId',
    'UIA_IndentationFirstLineAttributeId',
    'UIA_PositionInSetPropertyId',
    'UIA_HorizontalTextAlignmentAttributeId',
    'UIA_Transform2ZoomMinimumPropertyId', 'Polite',
    'UIA_DropTarget_DragEnterEventId',
    'TextEditChangeType_AutoComplete', 'UIA_Drag_DragCancelEventId',
    'UIA_MenuModeStartEventId',
    'UIA_IsScrollItemPatternAvailablePropertyId',
    'IUIAutomationScrollPattern', 'UIA_GridItemRowPropertyId',
    'UIA_TablePatternId', 'UIA_HyperlinkControlTypeId',
    'AnnotationType_ExternalChange', 'StyleId_Subtitle',
    'UIA_StatusBarControlTypeId', 'UIA_ButtonControlTypeId',
    'UIA_LegacyIAccessibleChildIdPropertyId',
    'UIA_GridItemContainingGridPropertyId',
    'ProviderOptions_ProviderOwnsSetFocus', 'TextUnit',
    'TextEditChangeType_CompositionFinalized',
    'NotificationProcessing_All',
    'UIA_IsSpreadsheetPatternAvailablePropertyId',
    'UIA_StyleIdAttributeId',
    'UIA_IsStylesPatternAvailablePropertyId', 'CoalesceEventsOptions',
    'UIA_IsSelectionPatternAvailablePropertyId',
    'UIA_IsDragPatternAvailablePropertyId', 'AnnotationType_Unknown',
    'IUIAutomationTablePattern', 'AnnotationType_Author',
    'IUIAutomationStylesPattern', 'UIA_AnnotationPatternId',
    'UIA_IsSuperscriptAttributeId', 'WindowInteractionState',
    'SynchronizedInputType_RightMouseDown',
    'UIA_DropTarget_DragLeaveEventId', 'IUIAutomationGridItemPattern',
    'UIA_IsCustomNavigationPatternAvailablePropertyId',
    'UIA_TreeItemControlTypeId', 'UIA_EditControlTypeId',
    'UIA_TabControlTypeId', 'UIA_ValueValuePropertyId',
    'UIA_MarginBottomAttributeId', 'IUIAutomationTextEditPattern',
    'IUIAutomationTableItemPattern',
    'UIA_IsDropTargetPatternAvailablePropertyId',
    'ProviderOptions_UseClientCoordinates', 'UIA_IsItalicAttributeId',
    'UIA_IsRequiredForFormPropertyId',
    'SynchronizedInputType_RightMouseUp',
    'CoalesceEventsOptions_Enabled', 'HeadingLevel6',
    'UIA_IsLegacyIAccessiblePatternAvailablePropertyId',
    'ScrollAmount', 'IUIAutomationElement5',
    'StructureChangeType_ChildrenBulkRemoved',
    'UIA_GridItemColumnPropertyId', 'ZoomUnit_SmallIncrement',
    'UIA_UnderlineStyleAttributeId', 'UIA_ListControlTypeId',
    'UIA_WindowPatternId', 'IUIAutomationValuePattern',
    'UIA_ControlTypePropertyId', 'IUIAutomationCacheRequest',
    'UIA_IsReadOnlyAttributeId',
    'UIA_ScrollHorizontallyScrollablePropertyId', 'IUIAutomation2',
    'UIA_IsSelectionItemPatternAvailablePropertyId', 'CUIAutomation',
    'UIA_OrientationPropertyId', 'IUIAutomationEventHandlerGroup',
    'UIA_IndentationLeadingAttributeId',
    'ExpandCollapseState_Expanded', 'UIA_IsDialogPropertyId',
    'UIA_TextChildPatternId', 'TextUnit_Word',
    'IUIAutomationProxyFactoryEntry',
    'UIA_ExpandCollapseExpandCollapseStatePropertyId',
    'UIA_MultipleViewCurrentViewPropertyId', 'UIA_ScrollPatternId',
    'IUIAutomationProxyFactoryMapping',
    'UIA_TableRowOrColumnMajorPropertyId',
    'ProviderOptions_NonClientAreaProvider',
    'UIA_IsTextChildPatternAvailablePropertyId',
    'UIA_Text_TextSelectionChangedEventId',
    'UIA_ProviderDescriptionPropertyId',
    'UIA_IsContentElementPropertyId',
    'UIA_AnnotationDateTimePropertyId', 'WindowVisualState_Minimized',
    'IUIAutomationElement8', 'TextEditChangeType_Composition',
    'UIA_RadioButtonControlTypeId', 'UIA_CustomNavigationPatternId',
    'UIA_CulturePropertyId', 'IUIAutomationVirtualizedItemPattern',
    'UIA_ForegroundColorAttributeId',
    'UIA_IsTransformPattern2AvailablePropertyId',
    'IUIAutomationRangeValuePattern',
    'UIA_StylesExtendedPropertiesPropertyId', 'DockPosition_Fill',
    'IUIAutomationWindowPattern', 'UIA_DocumentControlTypeId',
    'UIA_ChangesEventId', 'StructureChangeType_ChildrenInvalidated',
    'UIA_TogglePatternId', 'UIA_AfterParagraphSpacingAttributeId',
    'UIA_CustomLandmarkTypeId',
    'UIA_WindowWindowVisualStatePropertyId',
    'UIA_TransformCanResizePropertyId',
    'UIA_TextEdit_ConversionTargetChangedEventId',
    'UIA_AnnotationObjectsPropertyId', 'NotificationKind_ItemAdded',
    'Library', 'UIA_LegacyIAccessibleKeyboardShortcutPropertyId',
    'HeadingLevel3', 'UIA_LegacyIAccessibleStatePropertyId',
    'UIA_StylesStyleIdPropertyId', 'UIA_ToolTipOpenedEventId',
    'IUIAutomationPropertyChangedEventHandler',
    'UIA_AutomationFocusChangedEventId',
    'UIA_IsInvokePatternAvailablePropertyId',
    'UIA_RangeValueMinimumPropertyId', 'TreeScope_Parent',
    'UIA_SpreadsheetPatternId', 'UIA_SeparatorControlTypeId',
    'UIA_ValueIsReadOnlyPropertyId', 'UIA_GridRowCountPropertyId',
    'UIA_IsControlElementPropertyId', 'UIA_TabsAttributeId',
    'UIA_SelectionItemSelectionContainerPropertyId',
    'NotificationProcessing_ImportantAll',
    'UIA_MultipleViewPatternId', 'UIA_LocalizedControlTypePropertyId',
    'UIA_ItemStatusPropertyId', 'UIA_AcceleratorKeyPropertyId',
    'UIA_CenterPointPropertyId',
    'ConnectionRecoveryBehaviorOptions_Disabled',
    'UIA_IsKeyboardFocusablePropertyId', 'UIA_LevelPropertyId',
    'UIA_IsObjectModelPatternAvailablePropertyId',
    'SynchronizedInputType_LeftMouseDown', 'UIA_TextPattern2Id',
    'IUIAutomationNotCondition', 'UIA_AriaRolePropertyId',
    'UIA_RuntimeIdPropertyId', 'UIA_AnnotationTargetPropertyId',
    'UIA_SizePropertyId',
    'UIA_ScrollHorizontalScrollPercentPropertyId',
    'IUIAutomationProxyFactory',
    'StructureChangeType_ChildrenReordered', 'typelib_path',
    'UIA_TableColumnHeadersPropertyId', 'NotificationProcessing',
    'PropertyConditionFlags_None', 'TextUnit_Document',
    'UIA_IsGridPatternAvailablePropertyId',
    'NavigateDirection_PreviousSibling',
    'IUIAutomationObjectModelPattern', 'UIA_ListItemControlTypeId',
    'UIA_AnnotationTypesPropertyId',
    'IUIAutomationFocusChangedEventHandler',
    'UIA_SelectionItem_ElementSelectedEventId', 'TextUnit_Character',
    'NotificationKind_ActionCompleted', 'ZoomUnit_LargeIncrement',
    'UIA_LiveRegionChangedEventId', 'UIA_HeaderControlTypeId',
    'HeadingLevel5', 'TreeScope_Children',
    'AnnotationType_FormatChange', 'UIA_IsOffscreenPropertyId',
    'UIA_SearchLandmarkTypeId', 'IUIAutomationSelectionPattern',
    'UIA_IsItemContainerPatternAvailablePropertyId',
    'NotificationProcessing_ImportantCurrentThenMostRecent',
    'NotificationProcessing_CurrentThenMostRecent',
    'UIA_GridItemColumnSpanPropertyId',
    'UIA_IsTogglePatternAvailablePropertyId',
    'IUIAutomationTextRangeArray', 'UIA_DropTarget_DroppedEventId',
    'AnnotationType_Sensitive', 'UIA_MenuControlTypeId',
    'UIA_SpreadsheetItemAnnotationTypesPropertyId',
    'IUIAutomationStructureChangedEventHandler', 'NotificationKind',
    'UIA_AnimationStyleAttributeId', 'UIA_DataItemControlTypeId',
    'UIA_IsTransformPatternAvailablePropertyId',
    'UIA_DragIsGrabbedPropertyId', 'UIA_TextEditPatternId',
    'IUIAutomationElement2', 'UIA_TableRowHeadersPropertyId',
    'StyleId_Custom', 'HeadingLevel9',
    'UIA_TransformCanMovePropertyId',
    'UIA_ActiveTextPositionChangedEventId',
    'ExpandCollapseState_LeafNode',
    'ProviderOptions_ServerSideProvider',
    'UIA_IsSpreadsheetItemPatternAvailablePropertyId',
    'IUIAutomationSelectionPattern2', 'UIA_StylesFillColorPropertyId',
    'UIA_LabeledByPropertyId', 'UIA_DescribedByPropertyId',
    'UIA_ComboBoxControlTypeId', 'TextPatternRangeEndpoint_End',
    'UIA_DataGridControlTypeId',
    'UIA_LegacyIAccessibleNamePropertyId',
    'AnnotationType_Highlighted', 'UIA_DropTargetPatternId',
    'HeadingLevel7', 'UIA_MenuModeEndEventId',
    'UIA_ExpandCollapsePatternId', 'UIA_CaretPositionAttributeId',
    'UIA_IsSelectionPattern2AvailablePropertyId', 'TextUnit_Format',
    'UIA_LinkAttributeId', 'IUIAutomationPropertyCondition',
    'UIA_SpreadsheetItemAnnotationObjectsPropertyId',
    'NotificationKind_Other', 'OrientationType_None',
    'UIA_PaneControlTypeId', 'TreeTraversalOptions_PostOrder',
    'UIA_UnderlineColorAttributeId', 'IUIAutomationScrollItemPattern',
    'IUIAutomationSelectionItemPattern', 'UIA_FillColorPropertyId',
    'IUIAutomationGridPattern', 'TextEditChangeType',
    'UIA_GridItemPatternId', 'UIA_DragGrabbedItemsPropertyId',
    'IUIAutomationTreeWalker', 'TreeTraversalOptions_Default',
    'UIA_SayAsInterpretAsAttributeId',
    'UIA_IsWindowPatternAvailablePropertyId',
    'UIA_FontNameAttributeId',
    'IUIAutomationActiveTextPositionChangedEventHandler',
    'UIA_ScrollVerticalScrollPercentPropertyId',
    'UIA_IsAnnotationPatternAvailablePropertyId',
    'UIA_NavigationLandmarkTypeId', 'UIA_FontSizeAttributeId',
    'UIA_BoundingRectanglePropertyId', 'AutomationElementMode_None',
    'ExpandCollapseState_PartiallyExpanded', 'ExpandCollapseState',
    'IUIAutomationTextPattern2', 'StyleId_Heading6',
    'UIA_LayoutInvalidatedEventId',
    'UIA_TransformCanRotatePropertyId', 'UIA_CalendarControlTypeId',
    'UIA_TabItemControlTypeId', 'UIA_FillTypePropertyId',
    'TextUnit_Page', 'IAccessible', 'UIA_IsActiveAttributeId',
    'UIA_ProcessIdPropertyId',
    'UIA_WindowWindowInteractionStatePropertyId',
    'UIA_TitleBarControlTypeId', 'IUIAutomationElement4',
    'IUIAutomationNotificationEventHandler',
    'IUIAutomationOrCondition', 'ScrollAmount_LargeIncrement',
    'UIA_ClickablePointPropertyId', 'UIA_MenuClosedEventId',
    'UIA_NativeWindowHandlePropertyId', 'IUIAutomation5',
    'UIA_IsTablePatternAvailablePropertyId',
    'IUIAutomationTextEditTextChangedEventHandler',
    'UIA_IndentationTrailingAttributeId', 'UIA_NotificationEventId',
    'AnnotationType_CircularReferenceError',
    'ScrollAmount_LargeDecrement', 'ProviderOptions',
    'OrientationType_Vertical', 'UIA_FlowsToPropertyId',
    'IUIAutomationTextChildPattern', 'UIA_SplitButtonControlTypeId',
    'IUIAutomationDragPattern', 'AnnotationType_SpellingError',
    'IUIAutomationElement3', 'UIA_ItemTypePropertyId',
    'NavigateDirection_Parent', 'UIA_TextEdit_TextChangedEventId',
    'UIA_LegacyIAccessibleRolePropertyId',
    'UIA_ItemContainerPatternId', 'AnnotationType_Footnote',
    'IUIAutomationCustomNavigationPattern',
    'UIA_AnnotationTypesAttributeId',
    'UIA_IsTextPatternAvailablePropertyId', 'UIA_TableItemPatternId',
    'UIA_SpreadsheetItemFormulaPropertyId', 'UIA_CultureAttributeId',
    'IUIAutomationSpreadsheetPattern', 'UIA_FlowsFromPropertyId',
    'ZoomUnit_NoAmount', 'AnnotationType_FormulaError',
    'StyleId_Heading5', 'AutomationElementMode_Full',
    'UIA_Selection_InvalidatedEventId',
    'UIA_ScrollVerticallyScrollablePropertyId', 'StyleId_Heading4',
    'UIA_IsVirtualizedItemPatternAvailablePropertyId',
    'TreeScope_Element', 'UIA_IsDockPatternAvailablePropertyId',
    'SupportedTextSelection', 'ProviderOptions_UseComThreading',
    'TreeScope_Descendants', 'NavigateDirection_NextSibling',
    'UIA_FontWeightAttributeId', 'NotificationKind_ItemRemoved',
    'IUIAutomationDropTargetPattern', 'UIA_RangeValuePatternId',
    'UIA_SelectionItemIsSelectedPropertyId',
    'UIA_Window_WindowClosedEventId',
    'UIA_IsTextPattern2AvailablePropertyId',
    'UIA_MarginTrailingAttributeId', 'UIA_RangeValueValuePropertyId',
    'UIA_NamePropertyId', 'UIA_CaretBidiModeAttributeId',
    'UIA_ToggleToggleStatePropertyId',
    'UIA_RangeValueLargeChangePropertyId',
    'ToggleState_Indeterminate', 'IUIAutomation6', 'HeadingLevel1',
    'IRawElementProviderSimple', 'IUIAutomationTextRange3',
    'UIA_BackgroundColorAttributeId', 'UIA_GridColumnCountPropertyId',
    'UIA_LegacyIAccessibleSelectionPropertyId',
    'ProviderOptions_HasNativeIAccessible',
    'UIA_DropTargetDropTargetEffectPropertyId',
    'UIA_StrikethroughColorAttributeId',
    'UIA_IsExpandCollapsePatternAvailablePropertyId',
    'TreeScope_Ancestors', 'UIA_Selection2LastSelectedItemPropertyId',
    'IUIAutomationSpreadsheetItemPattern',
    'UIA_LineSpacingAttributeId',
    'AnnotationType_AdvancedProofingIssue', 'TreeScope',
    'UIA_ImageControlTypeId',
    'UIA_SelectionIsSelectionRequiredPropertyId',
    'UIA_ToolBarControlTypeId', 'UIA_FrameworkIdPropertyId',
    'UIA_DragDropEffectPropertyId',
    'StructureChangeType_ChildrenBulkAdded',
    'UIA_AnnotationObjectsAttributeId', 'UIA_SliderControlTypeId',
    'UIA_WindowControlTypeId', 'ScrollAmount_SmallIncrement',
    'NavigateDirection_LastChild', 'UIA_TransformPatternId',
    'UIA_Selection2FirstSelectedItemPropertyId',
    'WindowInteractionState_Running',
    'UIA_SelectionItem_ElementRemovedFromSelectionEventId',
    'UIA_IsDataValidForFormPropertyId',
    'PropertyConditionFlags_IgnoreCase', 'TextUnit_Line',
    'UIA_LegacyIAccessiblePatternId',
    'UIA_SelectionItem_ElementAddedToSelectionEventId',
    'UIA_ScrollBarControlTypeId', 'TreeTraversalOptions', 'ZoomUnit',
    'UIA_Text_TextChangedEventId',
    'UIA_LocalizedLandmarkTypePropertyId', 'UiaChangeInfo',
    'UIA_AutomationPropertyChangedEventId',
    'UIA_StylesStyleNamePropertyId', 'StyleId_Heading1',
    'UIA_VisualEffectsPropertyId', 'WindowVisualState_Normal',
    'UIA_FormLandmarkTypeId', 'UIA_SpreadsheetItemPatternId',
    'UIA_StylesPatternId', 'UIA_OutlineThicknessPropertyId',
    'UIA_FullDescriptionPropertyId',
    'AnnotationType_EditingLockedChange',
    'WindowInteractionState_ReadyForUserInteraction',
    'ScrollAmount_SmallDecrement', 'ExtendedProperty',
    'UIA_MainLandmarkTypeId', 'PropertyConditionFlags',
    'UIA_LegacyIAccessibleDescriptionPropertyId',
    'UIA_SelectionItemPatternId', 'UIA_HeadingLevelPropertyId',
    'UIA_CheckBoxControlTypeId', 'AnnotationType_ConflictingChange',
    'UIA_ControllerForPropertyId', 'IUIAutomationElement9',
    'ScrollAmount_NoAmount', 'UIA_SelectionSelectionPropertyId',
    'IUIAutomationElement7', 'PropertyConditionFlags_MatchSubstring',
    'UIA_IsSubscriptAttributeId', 'AnnotationType_Header',
    'UIA_SizeOfSetPropertyId',
    'UIA_IsTableItemPatternAvailablePropertyId', 'StyleId_Emphasis',
    'WindowInteractionState_BlockedByModalWindow', 'DockPosition',
    'UIA_Transform2ZoomLevelPropertyId', 'UIA_ObjectModelPatternId',
    'ToggleState', 'ExpandCollapseState_Collapsed',
    'UIA_IsPeripheralPropertyId', 'ZoomUnit_LargeDecrement',
    'NotificationProcessing_MostRecent', 'UIA_InputDiscardedEventId',
    'StyleId_Normal', 'UIA_BeforeParagraphSpacingAttributeId',
    'AnnotationType_TrackChanges', 'UIA_IsHiddenAttributeId',
    'UIA_WindowCanMaximizePropertyId', 'UIA_TextControlTypeId',
    'UIA_StylesFillPatternStylePropertyId',
    'IUIAutomationItemContainerPattern',
    'UIA_Transform2ZoomMaximumPropertyId', 'HeadingLevel8',
    'ToggleState_Off', 'UIA_LiveSettingPropertyId',
    'ProviderOptions_RefuseNonClientSupport',
    'UIA_OverlineColorAttributeId', 'UIA_CapStyleAttributeId',
    'TextEditChangeType_None', 'UIA_VirtualizedItemPatternId',
    'UIA_BulletStyleAttributeId', 'UIA_HasKeyboardFocusPropertyId',
    'StructureChangeType_ChildAdded',
    'WindowInteractionState_Closing', 'IUIAutomationTextRange2',
    'RowOrColumnMajor', 'IUIAutomationElement6',
    'UIA_RangeValueMaximumPropertyId',
    'UIA_TableItemRowHeaderItemsPropertyId',
    'UIA_IsPasswordPropertyId', 'UIA_SummaryChangeId',
    'UIA_DropTargetDropTargetEffectsPropertyId',
    'IUIAutomationTransformPattern',
    'UIA_IsTextEditPatternAvailablePropertyId',
    'UIA_OutlineColorPropertyId', 'UIA_DragDropEffectsPropertyId',
    'WindowVisualState', 'TextPatternRangeEndpoint_Start',
    'IUIAutomationSynchronizedInputPattern', 'UIA_GridPatternId',
    'SupportedTextSelection_Multiple', 'AnnotationType_MoveChange',
    'ToggleState_On', 'IUIAutomationTextPattern',
    'UIA_StructureChangedEventId',
    'UIA_AnnotationAnnotationTypeIdPropertyId',
    'UIA_AnnotationAnnotationTypeNamePropertyId',
    'UIA_MarginLeadingAttributeId', 'NavigateDirection',
    'UIA_AriaPropertiesPropertyId',
    'UIA_IsScrollPatternAvailablePropertyId',
    'UIA_LegacyIAccessibleDefaultActionPropertyId',
    'UIA_HostedFragmentRootsInvalidatedEventId',
    'AnnotationType_Mathematics', 'OrientationType',
    'UIA_RangeValueIsReadOnlyPropertyId',
    'IUIAutomationLegacyIAccessiblePattern', 'IUIAutomationCondition',
    'SupportedTextSelection_Single', 'WindowVisualState_Maximized',
    'StructureChangeType', 'UIA_ClassNamePropertyId',
    'IUIAutomationChangesEventHandler', 'IUIAutomationBoolCondition',
    'IUIAutomationTransformPattern2',
    'UIA_LegacyIAccessibleValuePropertyId',
    'ProviderOptions_OverrideProvider', 'StyleId_Quote',
    'UIA_Selection2CurrentSelectedItemPropertyId',
    'AnnotationType_Footer', 'UIA_StylesFillPatternColorPropertyId',
    'StyleId_Heading3', 'UIA_AsyncContentLoadedEventId'
]

