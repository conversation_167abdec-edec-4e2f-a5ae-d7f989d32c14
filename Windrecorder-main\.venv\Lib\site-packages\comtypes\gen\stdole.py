from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    HRESU<PERSON>, StdPicture, Gray, DIS<PERSON><PERSON><PERSON>ER<PERSON>, Picture,
    OLE_ENABLEDEFAULTBOOL, OLE_YSIZE_HIMETRIC, _lcid, Monochrome,
    OLE_YPOS_PIXELS, VgaColor, DISPMETHOD, EXCEPINFO, FONTNAME,
    Library, OLE_COLOR, OLE_XSIZE_PIXELS, IPictureDisp, IFontDisp,
    IPicture, D<PERSON><PERSON>ARAMS, FontEvents, Color, Default,
    OLE_XPOS_HIMETRIC, OLE_OPTEXCLUSIVE, IUnknown,
    OLE_XSIZE_CONTAINER, OLE_HANDLE, StdFont, F<PERSON>TSTR<PERSON><PERSON>THROUGH, Font,
    F<PERSON>TITALIC, IFontEventsDisp, <PERSON><PERSON><PERSON>IZ<PERSON>, <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>_Y<PERSON>ZE_PIXELS, <PERSON><PERSON><PERSON><PERSON>ER<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>BOLD,
    typelib_path, Checked, dispid, OLE_XPOS_CONTAINER, IFont,
    VARIANT_BOOL, OLE_YPOS_HIMETRIC, BSTR, GUID, OLE_CANCELBOOL,
    _check_version, IEnumVARIANT, Unchecked, OLE_YPOS_CONTAINER,
    OLE_XSIZE_HIMETRIC, CoClass, OLE_XPOS_PIXELS, OLE_YSIZE_CONTAINER
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'StdFont', 'FONTSTRIKETHROUGH', 'Font', 'StdPicture',
    'FONTITALIC', 'IFontEventsDisp', 'Picture', 'Gray',
    'OLE_TRISTATE', 'OLE_ENABLEDEFAULTBOOL', 'OLE_YSIZE_HIMETRIC',
    'FONTSIZE', 'Monochrome', 'OLE_YPOS_PIXELS', 'OLE_YSIZE_PIXELS',
    'VgaColor', 'FONTUNDERSCORE', 'FONTBOLD', 'typelib_path',
    'FONTNAME', 'Checked', 'Library', 'OLE_COLOR',
    'OLE_XPOS_CONTAINER', 'IFont', 'OLE_YPOS_HIMETRIC',
    'OLE_XSIZE_PIXELS', 'IPictureDisp', 'IFontDisp', 'OLE_CANCELBOOL',
    'IPicture', 'FontEvents', 'Color', 'Default', 'OLE_XPOS_HIMETRIC',
    'OLE_OPTEXCLUSIVE', 'Unchecked', 'OLE_YPOS_CONTAINER',
    'LoadPictureConstants', 'OLE_XSIZE_HIMETRIC',
    'OLE_XSIZE_CONTAINER', 'OLE_XPOS_PIXELS', 'OLE_YSIZE_CONTAINER',
    'OLE_HANDLE'
]

