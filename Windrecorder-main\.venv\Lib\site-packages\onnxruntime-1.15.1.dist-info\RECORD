../../Scripts/onnxruntime_test.exe,sha256=amJZdJdd-MZbxzIGj_NnY4x8WovOnjwaXTy_zzgFXS4,103325
onnxruntime/LICENSE,sha256=wlDWJ48LR6ZDn7dZKwi1ilXrn1NapJodtjIRw_mCtnQ,1094
onnxruntime/Privacy.md,sha256=v7dxKwdfPwfj6-5dwqKW0d4y2_ca0oZj9z0VOMtsOwg,2490
onnxruntime/ThirdPartyNotices.txt,sha256=5JKbIGOHGGRdPMp-y8rpLKWLBFnxjFL51k1QjO0G4fA,318315
onnxruntime/__init__.py,sha256=4aIfaQLYiKzOeCoKEHDXNi_UrfoQ2nC3P2_iNgZlI0k,4246
onnxruntime/backend/__init__.py,sha256=5I1Ylsawf9w6MNmK4RiN1wA-EEQqlKKwYTNZB-m_k6M,334
onnxruntime/backend/backend.py,sha256=0jIj7NPKBFw-c0g6zNoWmmeimD6GaVetc0KHJhIO1nc,8141
onnxruntime/backend/backend_rep.py,sha256=8Hid8lLPmcBtXsEUfpXsamX0pN5XATIIun-U7A6oNmk,1821
onnxruntime/capi/__init__.py,sha256=uRp4pMtfoayBhZgEsiFqFCD13Y6LUo82FdZsQX8X8LI,251
onnxruntime/capi/_ld_preload.py,sha256=li6cbZ64hDfUndat4mprUWzowLa3RQdw0q2E56sXFwE,413
onnxruntime/capi/_pybind_state.py,sha256=P8zE305Trv-939aEa5tfb9gdjeQAUer66JJnyzVwXqw,1544
onnxruntime/capi/onnxruntime_collect_build_info.py,sha256=N7ViCgTVKYLPiHXhf16ZkGK2FVNB3PzfWFLU4ykP28w,4068
onnxruntime/capi/onnxruntime_inference_collection.py,sha256=x2Fd_LzvV8VJnjTyLsQx9DUvT6-UzwmCiTqfoyBTxt4,39683
onnxruntime/capi/onnxruntime_providers_shared.dll,sha256=qx1tXlFzj4LkVbChICRkFxHvuGPTcypuuHYNgpgTGok,21952
onnxruntime/capi/onnxruntime_pybind11_state.pyd,sha256=CmnF3zTQd-iXSxd25dc0UCL34hXDfnPjT3piFbt1dds,19426744
onnxruntime/capi/onnxruntime_validation.py,sha256=ZF14DZc9hI5wVQjYzn4bWe0AauNLEX0qapeIH2MPino,6382
onnxruntime/capi/version_info.py,sha256=g2pKZXlThlkMbp7JytLW6rOC3M9Scbx44P9WBLULNaQ,33
onnxruntime/capi/training/__init__.py,sha256=V63zeaS2MljWyqwkvAq6Elo8phOZdPGZNa_1fbZrIig,326
onnxruntime/datasets/__init__.py,sha256=0D1rdhXK940JccUq3Sj4BBMqjDpAPOcxlGcwJR4X3wc,471
onnxruntime/datasets/logreg_iris.onnx,sha256=giR4TJjXNBLZ_ZmrzVejhWi9WQmA0PvlkWRkUxxS6Pw,670
onnxruntime/datasets/mul_1.onnx,sha256=cfQxxOkyHsb76xWNAu0kBFmn3MmGc_p5pPQ5zkLvrxA,130
onnxruntime/datasets/sigmoid.onnx,sha256=U0Crpnp-NHUWKteUN4r1XxcY9V-aXXS0r2Dsx_emJLY,103
onnxruntime/quantization/__init__.py,sha256=eeIgS5jf18UjGelvD4Bf57Z6-Qxvg6J54V-PEtlcww0,686
onnxruntime/quantization/calibrate.py,sha256=hIhZRSGKrwSPZ4aV45QGVCQdebz9TKH5mQObemCqJhE,38011
onnxruntime/quantization/onnx_model.py,sha256=0h7jgqANlOrlKFzXdek9rYuMh6Fdo-m_epeeyByqcsM,18686
onnxruntime/quantization/onnx_quantizer.py,sha256=K66CJpD7CitF7J-vJ7zmtRwQDHeWH-N50iRmrjmNRPo,46664
onnxruntime/quantization/preprocess.py,sha256=gaoyDn-VYzWDvox65Wtu3ZnnYfUtgllucByj2O2kq6E,5045
onnxruntime/quantization/qdq_loss_debug.py,sha256=sFweAtHoO0mclnSIN4tuUsRtv1msgY3gXzkrJRMm7tE,15307
onnxruntime/quantization/qdq_quantizer.py,sha256=TbyW2zj_XLppuduaeK90Q3MVLMscKSk43js4C-un0Xs,19061
onnxruntime/quantization/quant_utils.py,sha256=DZRb5H_RYow7txO5N9WovEERy801wh4288nU5yXtBRw,20102
onnxruntime/quantization/quantize.py,sha256=HT2rMx-0EEfk1tAtZNs__DBftB6CSpwQJnYv9ihZrHA,28359
onnxruntime/quantization/registry.py,sha256=-nAxLSzcb8pxBaJcwKfwsvdFt2DOihcMSqkNGWa5H6o,3671
onnxruntime/quantization/shape_inference.py,sha256=wg9a5h95j7xzpP7VSreIKTUCoedR_FCbz8j25yY2bps,6149
onnxruntime/quantization/CalTableFlatBuffers/KeyValue.py,sha256=e-jJFhw9fb775fDCLnWdbRSdoJ6vGD0c7qTnkIG-vNs,2250
onnxruntime/quantization/CalTableFlatBuffers/TrtTable.py,sha256=QQ9_f60Wya8U-KQOMu0gXImfhiPN6jNkfjpoCdAFic4,2665
onnxruntime/quantization/CalTableFlatBuffers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/quantization/operators/__init__.py,sha256=IfKXrFWtRSye1mkgD9lpwxio0fw9cVr_1CdV1cvefig,85
onnxruntime/quantization/operators/activation.py,sha256=JMkSthxHxIJe4wDnzhxi9nXmSdIG2Q98E7ahxXp3llM,4463
onnxruntime/quantization/operators/argmax.py,sha256=pfE9_eSTZ2otTkUcWwlLi7HJKtN10kE5c2Lz0SeVADQ,589
onnxruntime/quantization/operators/attention.py,sha256=eH7-Z3MfP6xRZCdhDAyNxWG2s2nZILxIEFVAHtqj7EQ,2637
onnxruntime/quantization/operators/base_operator.py,sha256=vrAVfKJXZvF7ZherKw4JUGonNyNuoU2TWnwBy-EQ3QE,1118
onnxruntime/quantization/operators/binary_op.py,sha256=pEQHRAS75EMp7LG6jzWV7gDQt_vzEPLJEI00eIOuoiA,2544
onnxruntime/quantization/operators/concat.py,sha256=F8hZfd6dcnU-J2BxMHJj2FL1AIxabHIuOyFybSh20Xk,2149
onnxruntime/quantization/operators/conv.py,sha256=nXNW4t6PDlyl7yDlesGjkHWO7ZJyammfXVJILXXDZd4,9695
onnxruntime/quantization/operators/direct_q8.py,sha256=jNL6DZGKcc1GjvBTlO5m3uO5hsMKZzwE_9_KIpdp4EI,3350
onnxruntime/quantization/operators/embed_layernorm.py,sha256=2LsZk5Um0ELaRESWjScgYyQioJelRZK6oQbzAclSgXI,4058
onnxruntime/quantization/operators/gather.py,sha256=oYPW3XdwWo7kqPYbEvCqPC6njAC2_zJN7c46z1xp6QE,2166
onnxruntime/quantization/operators/gavgpool.py,sha256=wYyjEf3h-_QChWKnsZ2N-haBG1RSvqRitZ-Yvfwo9Dk,2445
onnxruntime/quantization/operators/gemm.py,sha256=dN47axddU9tl39NviPccIRPGu2qmjHEMkVpIN-P--uA,5905
onnxruntime/quantization/operators/instnorm.py,sha256=hQ4cAMAVL5FXVWrMyTey9fXEhBiCja8HtQcYWZgWHAk,1114
onnxruntime/quantization/operators/lstm.py,sha256=UBaWDHQQ1_FVLerZ_vC8wJeHaVXUvuKtMKgt4shljVE,5050
onnxruntime/quantization/operators/matmul.py,sha256=PujLylxLAgJ_g9pA_m3H-wB1Fnfy2Fg1qEUIgJONWiU,7762
onnxruntime/quantization/operators/maxpool.py,sha256=QyDmHyBo0QKf6kNFbp2a9v6ThrBO-OL3tW0PFdN6bkI,961
onnxruntime/quantization/operators/pad.py,sha256=wenZNSN64H3-X4kgBlZW-1lTg-iGVuq863v2bwRCQag,4277
onnxruntime/quantization/operators/pooling.py,sha256=L0IT7G6-2XSx9-wUz5BX59Mc43FfJEg79NwW3yqEDhI,2285
onnxruntime/quantization/operators/qdq_base_operator.py,sha256=Fco9JZxrXQoVgjKvmHFuzT0mogWo9-wHiDa51CjTioo,823
onnxruntime/quantization/operators/resize.py,sha256=BMeym-7GHOSnGpZisa9BkdQkVmCXwKANA5NpnKRnaLI,962
onnxruntime/quantization/operators/softmax.py,sha256=aUy4yDGdCQ-vptejnNowfae3H8R5P40HszeiXBCtBGk,3386
onnxruntime/quantization/operators/split.py,sha256=ZY8aEpiF2xD0r5DTmm3wVlcpsepd-FOSYMZ86XCwUeI,2244
onnxruntime/quantization/operators/where.py,sha256=wd6PQ7LlbrJTqamFMch_Fipnbt4IewMJSAPozMTrwKI,3127
onnxruntime/tools/__init__.py,sha256=7up7iKcklVy6UcpIIIIlBaK690O32vaOxyaaTWvwyxU,528
onnxruntime/tools/check_onnx_model_mobile_usability.py,sha256=h-xTaXu_uSVptpmx69FiYcwACzuvI-4sTqLKkKXMo08,2871
onnxruntime/tools/convert_onnx_models_to_ort.py,sha256=TnqL0j4AdgCXdR1Iu-hHBNI02vMt3vMFnBtSGG4hIWs,16064
onnxruntime/tools/file_utils.py,sha256=ONHY-VlxAJ7mlrTNZYkRD4I00RqsSHMZb1rUUxceQss,1569
onnxruntime/tools/logger.py,sha256=ikKm7kP-W4Hjl2UuDx-WUViFrWU7qak-WzdoIgNyAMc,286
onnxruntime/tools/make_dynamic_shape_fixed.py,sha256=GkbUE5kH1pOua5EJVH4trXs7mJIPIQ8T2YTeKQxr6ak,2608
onnxruntime/tools/offline_tuning.py,sha256=Gd120-LGX04OJZ8nvErr_8h-5XGdDOEmuJPCWVQC76E,6380
onnxruntime/tools/onnx_model_utils.py,sha256=rAt-27nQgDJ_cQPvg9Pc3Sub6a9dYoIgn8ja6isKTSo,14355
onnxruntime/tools/onnx_randomizer.py,sha256=9L96dzIf59cQ2oQsmR2EEsdrR4hHwEGrpZkajEgUPAY,3361
onnxruntime/tools/onnxruntime_test.py,sha256=cyb-WS4KNKY6iaeCwDpzipMM5Sw2YLgPckJqqtRuINk,5657
onnxruntime/tools/optimize_onnx_model.py,sha256=S6-B-YorPT48PnkUZZLxPQnGVhhuO0mRiNbG26JAfxg,1949
onnxruntime/tools/pytorch_export_contrib_ops.py,sha256=xxlw5jPDy72tWEPPYn8Qhof4H-edK7RwpT0ZXtWYfC4,4091
onnxruntime/tools/pytorch_export_helpers.py,sha256=MRegHn3z3VhVbZQ4O-kTGedIE-pufyxhq1A1GVIdCjY,5971
onnxruntime/tools/reduced_build_config_parser.py,sha256=O9XtpCRKoFiPcXuwfyGH1zcvpVU0cbOq9JxFh0Jm-Fs,10137
onnxruntime/tools/symbolic_shape_infer.py,sha256=-XVLniCkGUbEZRH84MfSTkavu7D3m14SMzhE4D_A0k8,129481
onnxruntime/tools/update_onnx_opset.py,sha256=fplb1ypV-pFhu8Xsi5u_bDfI7EsC4zamJkTziccgQ2c,1182
onnxruntime/tools/mobile_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/mobile_helpers/check_model_can_use_ort_mobile_pkg.py,sha256=hKH_tiR90ctxNrutkA1_OCZs17l4xgircarz8Or_CN4,12691
onnxruntime/tools/mobile_helpers/coreml_supported_ops.md,sha256=PDRe4fer6SAA9jIoMQrPX7I7CHBU1dbiH29FEQEdYHk,1319
onnxruntime/tools/mobile_helpers/mobile_package.required_operators.config,sha256=nDi5sBRRAFxhelU7H6SJUEHuxiUfFRE8MIjw7sVJCXs,3069
onnxruntime/tools/mobile_helpers/nnapi_supported_ops.md,sha256=j43sa6p93T_pswIMlzHRfXH3VHY4IrZFY9a0QYzfPbE,2226
onnxruntime/tools/mobile_helpers/usability_checker.py,sha256=XddT0FQjfSdgp6XMoImbPdWbtKmEGoUnZWswDvgsl-Q,25768
onnxruntime/tools/ort_format_model/__init__.py,sha256=gQqh9tWzGxeUllyIEF2FmIfee9ulji3mlJQNW7QrpJ0,1378
onnxruntime/tools/ort_format_model/operator_type_usage_processors.py,sha256=Oyjk4bhv6CYsAni7_crv-NcR08NlCsWzQznTECke8Xk,27366
onnxruntime/tools/ort_format_model/ort_model_processor.py,sha256=sT2if_kb7cwwfLp3m1zXPTNqy5pxn2NnitMrXjSftos,4484
onnxruntime/tools/ort_format_model/types.py,sha256=gpjqcHLTox5fxkxP6XA_Tvl2_NmUc-n-q_QPxgGTXZg,4154
onnxruntime/tools/ort_format_model/utils.py,sha256=Ix5mFXZCnMEHf8Tg7Mwg2GFdy0d1l-zocT2fsE8_8sU,2604
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgType.py,sha256=KNRBlqUVQKtG2E7c2TvSUb29R3UXcR9cPUpdtmnR9QI,149
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgTypeAndIndex.py,sha256=Tr3VGnO2r4ZGT-OP_96qqUzTap1DS_0lysfzgODa1nQ,1611
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Attribute.py,sha256=wfv752tTginm2d0tr2QecHSZAGoCTLwwTifXIXJC33A,9310
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/AttributeType.py,sha256=1_69-oW8C6M_Wq2_1TXwXL_ryHXUpxIC42CU_QPYFss,348
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedKernelCreateInfos.py,sha256=cXdas8VWnTkcmOLfMcOVnMO3CPv6ETOkz4SizEiGAt4,3754
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedNodeIndexAndKernelDefHash.py,sha256=ez5IgvSx0ds8ktg6u4_YtJ1KAjRbT7Wv203bHeEDZyk,1924
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSessionState.py,sha256=R4u-ioNdtYqRD1ANa_VA1O2etj48ABTptyBnItp25WU,2939
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSubGraphSessionState.py,sha256=89oCxn7cIaQJXMeght_1y7CN0LY244sVCuTjrnNS8LM,2112
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Dimension.py,sha256=i8aQjgIQLWaoiyso0qx7Zp7aJeWBPROqti6coq3xSWU,1792
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValue.py,sha256=fqWUU7xUeEPZRCbjx1-T2XzSXCKhnSUWys1mngK5tyM,1988
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValueType.py,sha256=MvwDY8Du8E08-7-lpr2YrB5eRtSbnruGacgQu8zZwBo,176
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/EdgeEnd.py,sha256=TH60nYg8iKcHZKO8-YpA2UMOLkNIFr79JdKoWXzbxTY,1076
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Graph.py,sha256=TgNQezbzb1LgAKl6UQwBzeHOV8vDHESEfehzdvCgxEA,9000
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/InferenceSession.py,sha256=Oh1NVttahMXc8_PdFTjOGGrU1LM6KuPwckJBxkjdReY,2477
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrArgsEntry.py,sha256=eN88n-fwIl0MpkIUXyHhS_M1qwbaGArSc2EY15890mQ,2532
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrResolver.py,sha256=yfzU2LLgnJsHhdgQDi1iLqdeaFlr9eiVmdnVDwfZx4g,2244
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/MapType.py,sha256=5NHXISPEFJ3nWKEImkHs2izwbL8hQW3Ba5damX7Xrj8,1728
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Model.py,sha256=GtXy--BasuQ_5pSrrMqF2K0oCQegL48wcZb9F1MclN4,6145
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Node.py,sha256=zwGG5ihskvWevt2MlhS8YOgNPTOFBdu7cFSPGPs5VU8,8635
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeEdge.py,sha256=6BEVnqcv7huI0W7o1BWQJqaWm7tUf27rL7HnxW1iYDU,3339
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeType.py,sha256=quSM-cnMeSAHZRPJdBXSCjC8Wnj30wc_1mKl9obfW4c,153
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodesToOptimizeIndices.py,sha256=9GVC9CpTSZ6UmPQ1tQOUEmJa1I65eAMQo93mw6wzsfA,4785
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OpIdKernelTypeStrArgsEntry.py,sha256=czJ9B6XND3bwwBcBkQkVyvsUmKUVwrOZBk-07uTCmig,2664
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OperatorSetId.py,sha256=bZayVbeZdKnKi46Dkg1N4M70oHYaRdl2V87WHZPWIQE,1621
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecord.py,sha256=vDhHlZq_DcrLo800B5hLXvEbXLEtcS5waJoLwDgwP9E,3194
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecordContainerEntry.py,sha256=y4D8JefGWTYtwoDmh_LhCi7Qlo60OMTjGifzhnl4obI,2954
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizations.py,sha256=I-HIh6O1l1Lw_zWtFr_JpBk8Ef_phGSoBpfkDp5wSZo,2253
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SequenceType.py,sha256=42QJ7DbSUilbbysiYHGmEtYbYoU77X8W0Lyy6qvyeiM,1437
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Shape.py,sha256=xwh9ZSvNNb0fOV_g9deY0JD6hEXPDgk1uAppsMLeGH8,1889
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SparseTensor.py,sha256=zJ35it-hOQ_bT8QBYdncXbAS8ewQsXh3BJ_XEVdVt6M,3133
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringStringEntry.py,sha256=AB9-zZAM9bJjrWNbe8QIOR37i92jPeBGlgd1uRp0vD0,1673
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Tensor.py,sha256=KYzRQPUQ3INAPl7SoTZhALOVWDyQGdXg-Gm5qjfUvHE,5144
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorDataType.py,sha256=Q45onOhkRAMjauV4eytYveheZsnItgPOjKUKUI-dgzA,408
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorTypeAndShape.py,sha256=IGgnA7Kgz00idyxMVDAy-m-CAxvcl4NtGIai7L9TEVw,1828
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfo.py,sha256=BozLwO5lX5f___cutnpJDD2oJWOnJqDDaeJPJaqD_WQ,2039
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfoValue.py,sha256=vLFbH5F9iaEEF5d0OSnt1bvlAzQ9dddPc7pDu5uhvLA,200
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ValueInfo.py,sha256=oT0gX2UAHgZqIn7oyetzlJIvQmFgeSeNuibt0TLr3dQ,2118
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__init__.py,sha256=au-zd_Hu8RMFk1WMspyWvkkh09aN0gk-KnwF79ef5oo,253
onnxruntime/tools/qdq_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/qdq_helpers/optimize_qdq_model.py,sha256=9kpU0Dukc0ZEHnjwO7NqNFUFVRtOx8xgFPVWZpXkEcQ,1279
onnxruntime/transformers/__init__.py,sha256=fvi2cqSNrKFRuEnpSzac01zdg-O8py6bYOtnv988gDo,552
onnxruntime/transformers/affinity_helper.py,sha256=KOKBvzoBr-wOk0QHMGKzY1uy1iI7E6eHpYwBdTHM-Y4,1442
onnxruntime/transformers/benchmark.py,sha256=2RuzJwr1dIbqhYNidUxHTNa04gdOZLXFVlfxkWkBiKE,33334
onnxruntime/transformers/benchmark_helper.py,sha256=3FthZnfsXmb8VsQgJwvrJiHw6RBbvDsMsox8iyE2Cus,20969
onnxruntime/transformers/bert_perf_test.py,sha256=LuGJhil3bMT5W2dHd5B0dlXQj4QaIYkBF1cy0vzyLkg,19728
onnxruntime/transformers/bert_test_data.py,sha256=6t5bq5KrTkGnx6MyNy95J3HTZd2Lqxh7mhc1svcFH64,19039
onnxruntime/transformers/compare_bert_results.py,sha256=SxRP2gvGOy2w6_DoixftFdMeQwApRiTJrDTkqpDe9AY,7639
onnxruntime/transformers/constants.py,sha256=enLKnm_Wlt7bu9OVa52lxwDBHJgbwhAXvSniP6EAMO8,740
onnxruntime/transformers/convert_generation.py,sha256=2VhCTsrp9Mo-cfwaUalIVruPGJy8fzvpsiPiFU1HFVw,108650
onnxruntime/transformers/convert_tf_models_to_pytorch.py,sha256=JrMGzUi_4cMfYoIYrxa1n0jnMDG-WYj-xmUXZmH8aJ0,6705
onnxruntime/transformers/convert_to_packing_mode.py,sha256=Vpow2auDtyUhKHiIk39rPISvQ5s3F5NasNn9IGaZBSo,10057
onnxruntime/transformers/float16.py,sha256=JXaX1Wh0KZyQMxyqHcwcxYqcN6CqiO7Mrtr_muJpwxE,22806
onnxruntime/transformers/fusion_attention.py,sha256=twc3emyVmLGqv0hFOkrSIKYJAgEPd7ZYc_AFvFltB_w,50487
onnxruntime/transformers/fusion_attention_unet.py,sha256=29oRxCAayOvytuobKmmI_XqWNDOITphwLYW-8A0cMrc,18666
onnxruntime/transformers/fusion_attention_vae.py,sha256=C2JcT-ik-Fapc_lO21MBnNANpDHv1QQlWkaf1A2RQgI,12600
onnxruntime/transformers/fusion_bart_attention.py,sha256=WSm8vOZhS4rXWM82It-dfRCd7Al03MzRrJFpJyUAFVE,18807
onnxruntime/transformers/fusion_base.py,sha256=ld9YwmEMxw8syuEmoCOZuBNFXcSAwIzUgu-qokceTkg,2820
onnxruntime/transformers/fusion_bias_add.py,sha256=7JRHl-p1M8GxNfa9cgHsES7dwburpaTWqYh921_8QjQ,2066
onnxruntime/transformers/fusion_biasgelu.py,sha256=vGamxthOu6jXsxCRVdTFaP25-_tnjz9TVq91pIRV_Is,2300
onnxruntime/transformers/fusion_biassplitgelu.py,sha256=6G73bmAGM5y02Rm_Lupbn341O0Y5Sr-2Re_628Ez2Qo,4516
onnxruntime/transformers/fusion_embedlayer.py,sha256=Kau9xeIRlbR21j6DIab5fzOulJxNxpwchxwzUz_Cbso,35646
onnxruntime/transformers/fusion_fastgelu.py,sha256=pi2U93F4xWMThs6Yz9K1d6AZQ1kyWSZhjeGqs7WWAVI,13324
onnxruntime/transformers/fusion_gelu.py,sha256=GrTB0LoVz_YRyTW-JoL4Fh_fz_IA01JweEP0Tj_Lwgs,10180
onnxruntime/transformers/fusion_gelu_approximation.py,sha256=MCNAXL0nIHweYrBe4euYUZnmzxvi3X2Cj1HKi6o4Nlc,1076
onnxruntime/transformers/fusion_gemmfastgelu.py,sha256=bNQO5aVD2-H0Kqdr8meFGRmOi3A0AvVVTnCcrBF6WB8,4262
onnxruntime/transformers/fusion_gpt_attention.py,sha256=dsF4lhKqHNIP9KtV0OSG4TJOW420dEa_r1_s2EGm3UI,22327
onnxruntime/transformers/fusion_gpt_attention_megatron.py,sha256=A25O3V06F5e1c8sJLxh84wP3VAk2SfFZCVSQukOmuTQ,13887
onnxruntime/transformers/fusion_gpt_attention_no_past.py,sha256=NI9ySlwkHMHJEUOwyLZtiMMcpbifr6UFGhnBFwc3KFo,11023
onnxruntime/transformers/fusion_group_norm.py,sha256=ASkDmrvsCVsTJlDi9pKj-PmBjxbFy3eAxSUDOWJgBY0,7693
onnxruntime/transformers/fusion_layernorm.py,sha256=fQO0uU3yPE6KSvuvMYAKsnMu-qLAB4WPTVXoH85Bank,12194
onnxruntime/transformers/fusion_nhwc_conv.py,sha256=VK4-I7ne5O_BkuTYR7TVP4FZaUUI2G6SXuVJWVG_GGI,3592
onnxruntime/transformers/fusion_options.py,sha256=vFJONo287ZNRwpHVMYg92Hiyeaaks6tZ4h8xyq7PEIY,10607
onnxruntime/transformers/fusion_qordered_attention.py,sha256=VutuLlHX0oDnDhcbzWhVSq-VXlyKNaOXu2hW8gdn21c,17163
onnxruntime/transformers/fusion_qordered_gelu.py,sha256=aRBTRACUuXMctEfyL1GICG5hFRqiuybJQ0B7Psgz5dQ,4393
onnxruntime/transformers/fusion_qordered_layernorm.py,sha256=5GndWDsK2_3wURps3R4tVuGD0IuDFAcWzX3FXiysoGM,4915
onnxruntime/transformers/fusion_qordered_matmul.py,sha256=j85chtrY9YrGD1ERNIHqCBAxZW51I2Sk_EFU4jg8qdM,8566
onnxruntime/transformers/fusion_reshape.py,sha256=RNfehwtwQYWvrBjXKkjso_L9nxfGUC1WRyGFwwWMrY4,6463
onnxruntime/transformers/fusion_shape.py,sha256=k6wcbIWY4FMxWqLuf0b768z5n2vwslys2yJ30ceek9A,3845
onnxruntime/transformers/fusion_skiplayernorm.py,sha256=s379IDb_HjNnoWsHFSLP7lFb-EWtP8ic2-rVa3YxX4c,8269
onnxruntime/transformers/fusion_transpose.py,sha256=4h9x8j7ERcT2TUhr6XgYICrtvaeL5JBDovYCoiB6tAw,6980
onnxruntime/transformers/fusion_utils.py,sha256=7jdnakQTTJP1OiS6uzrX1lX1rPcXvKUu0NfZd6NmFSA,12229
onnxruntime/transformers/huggingface_models.py,sha256=C0B3Lh52edigeZd_JZEBBl2x_hruGF0u5VmsNOke_J4,9130
onnxruntime/transformers/io_binding_helper.py,sha256=KEy0QRl_3HmFU8sjSCbP2vsDNegSzHQ1CJejPp0as2k,7726
onnxruntime/transformers/machine_info.py,sha256=qJXW4KGXgNONrtgBextAurr1K9QDFG3Pcyllxl7Xel4,7336
onnxruntime/transformers/onnx_exporter.py,sha256=MyTxtBwesCK2jCVs0ql6m-AANbuPgZEKdFRBAZmC90A,25364
onnxruntime/transformers/onnx_model.py,sha256=k_SGDsziPBDNHa0wJrZeYI27EwgX7pk4BH9-Opj2WxA,49231
onnxruntime/transformers/onnx_model_bart.py,sha256=G5KQEf_ipKOD41ctZvqySSheBlHtbVF9gbpSGkAyfH4,5397
onnxruntime/transformers/onnx_model_bert.py,sha256=wtxvd-nyW5nxKP4KTjApOkGNO_xodMMERAQWoWjKD2A,20938
onnxruntime/transformers/onnx_model_bert_keras.py,sha256=vK8lv_3clLuLJ6AYEUCdv6ukoPdG_4jtLyhk7_lv0oA,19132
onnxruntime/transformers/onnx_model_bert_tf.py,sha256=YCtvEy8sNBTESPOVp0gaTgES6R3PIIAnoSkSPPn4nJs,25561
onnxruntime/transformers/onnx_model_clip.py,sha256=21bomWo0u0OWxhgP198OC8gzjnNoy2scvKz5Aw0R04g,1067
onnxruntime/transformers/onnx_model_gpt2.py,sha256=HMVPDGBlU1QPp1dB5aKCDAnM1nPds8HfrzEiYEC-Yo4,3747
onnxruntime/transformers/onnx_model_t5.py,sha256=7rbWKZDIGr0GV7a-qxrD-LmwKVoKEUl57u7pCK6L-3M,31346
onnxruntime/transformers/onnx_model_tnlr.py,sha256=HNb6dBsWU8Fo-Trx_R9pgK0pKnDRZGJ3i54n_aNd16E,8682
onnxruntime/transformers/onnx_model_unet.py,sha256=JvRHSsljqiZ0HEkGE9wh477RIM1grCBQiS20hufS5D0,7100
onnxruntime/transformers/onnx_model_vae.py,sha256=_L2UlVv4CHNImsvQKmyW0QIMK5Hpsg8P8ZCyM8f9rEQ,1515
onnxruntime/transformers/optimizer.py,sha256=hVR5nF-WcABMJaOyroXkfzyC1QNhIYOc3Bg1a20WvkU,19209
onnxruntime/transformers/profiler.py,sha256=MAe1Oqamoh7ZQoZ08audv8XceEJURW-X72R9-HdB_-E,25009
onnxruntime/transformers/quantize_helper.py,sha256=8u_pW1DHv7mh5KYOObG4ZP_qQKuFRGcxkotdvbkNy9Y,2825
onnxruntime/transformers/shape_infer_helper.py,sha256=Six_K74VGMyJRf6npveQkn-_cLjcFIRdRvmuSIaC9W0,4590
onnxruntime/transformers/shape_optimizer.py,sha256=BC3-JRbRrCohR0GJWkfk2Kdkkhmcg4_bBPKJ9y66R9Q,15555
onnxruntime/transformers/torch_onnx_export_helper.py,sha256=5ml2odDKi6sA1iaHqdNMA8tC6MnslaCF35RZjPx6ib4,2507
onnxruntime/transformers/models/bart/export.py,sha256=PNlhkbvrxTxSSLXpzqoa02Lektzf8rdZpcVFBxw-qcI,4285
onnxruntime/transformers/models/bert/__init__.py,sha256=Hrvad2_TcQ3bpd4b7QJsJV4SSilowg8Loy3e5FxNiEI,252
onnxruntime/transformers/models/bert/eval_squad.py,sha256=pr7M4yG0eJIFuPubrKta1vWg3MMlzKS2eoyUe4iLS0w,10783
onnxruntime/transformers/models/gpt2/__init__.py,sha256=Hrvad2_TcQ3bpd4b7QJsJV4SSilowg8Loy3e5FxNiEI,252
onnxruntime/transformers/models/gpt2/benchmark_gpt2.py,sha256=JBR2lEewyswFErYN0C1R2gj9zdh1t0eDQK3rO97Nh0o,16036
onnxruntime/transformers/models/gpt2/convert_to_onnx.py,sha256=6zxnf4usjOoC6tNjcg27V4QSxJVKTRmAPsirIHG_a40,20751
onnxruntime/transformers/models/gpt2/gpt2_helper.py,sha256=FJbaiUkdBrXPLiF7hta_PUvEgoLFMsfezgpU5Cj8k6Y,41547
onnxruntime/transformers/models/gpt2/gpt2_parity.py,sha256=9sVBRQwL3ELgN19eV3WXWeQMHY2P46ufm48vnF5WcRE,18322
onnxruntime/transformers/models/gpt2/gpt2_tester.py,sha256=Qs1dOPLzYFGujpHGOobxsU4q-75INKqSHpJH5imDYcY,20178
onnxruntime/transformers/models/gpt2/parity_check_helper.py,sha256=7tO0sHE8oLQ0smA83jfJ_JHlCrNyT289_tEAbEiJJUQ,5906
onnxruntime/transformers/models/longformer/__init__.py,sha256=Hrvad2_TcQ3bpd4b7QJsJV4SSilowg8Loy3e5FxNiEI,252
onnxruntime/transformers/models/longformer/benchmark_longformer.py,sha256=iPyJoRG_pjoA5I0Qx47x8nY34_2yoNv8PXeSLBcPGp8,30370
onnxruntime/transformers/models/longformer/convert_to_onnx.py,sha256=9dCRv9i9EAqCCPZSyof33cF7S5wkk2RmHYFQVL0QbXE,15342
onnxruntime/transformers/models/longformer/generate_test_data.py,sha256=a05XRHyGTm6tnH5qhc-XPLrYQBZm_1VcDgua6gTqA3Q,9225
onnxruntime/transformers/models/longformer/longformer_helper.py,sha256=FH7Uykc57rLNr1l0pr85OVgr9PZE_4x29xdE-t1riC4,3180
onnxruntime/transformers/models/stable_diffusion/__init__.py,sha256=Hrvad2_TcQ3bpd4b7QJsJV4SSilowg8Loy3e5FxNiEI,252
onnxruntime/transformers/models/stable_diffusion/benchmark.py,sha256=Fa4hc_TsaXuL2W-RRHxPMjUfemHNLPgjKXti4ID88M4,21893
onnxruntime/transformers/models/stable_diffusion/optimize_pipeline.py,sha256=S-6sgND_NKJPMEzPj7nkSokBmchfZL2kRf9-z7aNDpA,11932
onnxruntime/transformers/models/t5/__init__.py,sha256=Hrvad2_TcQ3bpd4b7QJsJV4SSilowg8Loy3e5FxNiEI,252
onnxruntime/transformers/models/t5/convert_to_onnx.py,sha256=e1t47mh0N16c0glXXlBhuWA21gko398YHjGjaE3caQM,8832
onnxruntime/transformers/models/t5/past_helper.py,sha256=ounFkzTPTM0N9gjZ70jhh-grskckMQwCu2KsDupljpM,6987
onnxruntime/transformers/models/t5/t5_decoder.py,sha256=jw21pKh49-kxxT3SqaAhVzH5hCTRJuWs7pBpmbGQrPk,17207
onnxruntime/transformers/models/t5/t5_encoder.py,sha256=qxjvzbfPYIKtIoYBMRCW4vmjd9beSWqFg4dexSYUNyk,6407
onnxruntime/transformers/models/t5/t5_encoder_decoder_init.py,sha256=_AGmW4ujewlPG8tV8kGQBsekFwl5psEj4mDxWFrcM9Q,12324
onnxruntime/transformers/models/t5/t5_helper.py,sha256=EpYgznALmy32_Iz4A-WDGql1-kOY2M358c2L9rcGvaY,11027
onnxruntime/transformers/models/whisper/__init__.py,sha256=2c213CqzXrc0N6Cqf__Te5d_SH_stfLdNdeNrugB7SQ,321
onnxruntime/transformers/models/whisper/convert_to_onnx.py,sha256=lUnX4gxlWBJTHdU5oCOG4KpnGM7nqAMKaY9QIa7ahqY,11670
onnxruntime/transformers/models/whisper/whisper_chain.py,sha256=6QTlpTbu68fp8A-0CIRVhz9bccmQbCVxnikHmwXoB0s,4441
onnxruntime/transformers/models/whisper/whisper_decoder.py,sha256=SKIfYMP0mgp6xKdCTS2H5rW_avmaOEAEXFO8U7Rb77A,16818
onnxruntime/transformers/models/whisper/whisper_encoder.py,sha256=PLKYGpFvvw0_Z7MaGv75Ccu5PHC_9yXVoWdLC7t80lw,5931
onnxruntime/transformers/models/whisper/whisper_encoder_decoder_init.py,sha256=ZSoj2mtvoYeQy3sh-4wgjPs9WcWbt_iOuMt9iTRsXb8,13107
onnxruntime/transformers/models/whisper/whisper_helper.py,sha256=hQldNVOLQw3UjbAczdWXMjlIleye1dFNpwhQp2q6_wA,10529
onnxruntime-1.15.1.dist-info/METADATA,sha256=33GA0WOua1lZ5bpKW9w4w4Vfg17ykh4zRlhqhpT0qQ0,4126
onnxruntime-1.15.1.dist-info/WHEEL,sha256=9wvhO-5NhjjD8YmmxAvXTPQXMDOZ50W5vklzeoqFtkM,102
onnxruntime-1.15.1.dist-info/entry_points.txt,sha256=7qLS4FbGXwPZjfdpVAGpnmk9I6m6H5CxEnwcCx1Imjs,77
onnxruntime-1.15.1.dist-info/top_level.txt,sha256=zk_fJEekrTm9DLxX2LwGegokVqP6blqPhFoMIuh0Nv8,12
onnxruntime-1.15.1.dist-info/INSTALLER,sha256=9Fj27hpVKWMXZsBOPfrH05WeL2C7QXSjAHAgayzBJ8A,12
onnxruntime-1.15.1.dist-info/RECORD,,
