/*! For license information please see 2.6d7de458.chunk.js.LICENSE.txt.txt */
(this.webpackJsonpstreamlit_tags=this.webpackJsonpstreamlit_tags||[]).push([[2],[function(e,t,n){e.exports=n(22)},function(e,t,n){"use strict";e.exports=n(16)},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(5);function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,i=!1,a=void 0;try{for(var o,u=e[Symbol.iterator]();!(r=(o=u.next()).done)&&(n.push(o.value),!t||n.length!==t);r=!0);}catch(s){i=!0,a=s}finally{try{r||null==u.return||u.return()}finally{if(i)throw a}}return n}}(e,t)||Object(r.a)(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}},function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,"a",(function(){return r}))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return"string"===typeof e&&r.test(e)};var r=/-webkit-|-moz-|-ms-/;e.exports=t.default},function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var r=n(6);function i(e,t){if(e){if("string"===typeof e)return Object(r.a)(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Object(r.a)(e,t):void 0}}},function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,"a",(function(){return r}))},function(e,t,n){"use strict";n.d(t,"b",(function(){return yc})),n.d(t,"a",(function(){return dc}));var r={};n.r(r),n.d(r,"memcpy",(function(){return We})),n.d(r,"joinUint8Arrays",(function(){return He})),n.d(r,"toArrayBufferView",(function(){return Ye})),n.d(r,"toInt8Array",(function(){return $e})),n.d(r,"toInt16Array",(function(){return Ke})),n.d(r,"toInt32Array",(function(){return Qe})),n.d(r,"toBigInt64Array",(function(){return Ge})),n.d(r,"toUint8Array",(function(){return qe})),n.d(r,"toUint16Array",(function(){return Xe})),n.d(r,"toUint32Array",(function(){return Ze})),n.d(r,"toBigUint64Array",(function(){return Je})),n.d(r,"toFloat32Array",(function(){return et})),n.d(r,"toFloat64Array",(function(){return tt})),n.d(r,"toUint8ClampedArray",(function(){return nt})),n.d(r,"toArrayBufferViewIterator",(function(){return it})),n.d(r,"toInt8ArrayIterator",(function(){return at})),n.d(r,"toInt16ArrayIterator",(function(){return ot})),n.d(r,"toInt32ArrayIterator",(function(){return ut})),n.d(r,"toUint8ArrayIterator",(function(){return st})),n.d(r,"toUint16ArrayIterator",(function(){return lt})),n.d(r,"toUint32ArrayIterator",(function(){return ct})),n.d(r,"toFloat32ArrayIterator",(function(){return ft})),n.d(r,"toFloat64ArrayIterator",(function(){return dt})),n.d(r,"toUint8ClampedArrayIterator",(function(){return ht})),n.d(r,"toArrayBufferViewAsyncIterator",(function(){return pt})),n.d(r,"toInt8ArrayAsyncIterator",(function(){return vt})),n.d(r,"toInt16ArrayAsyncIterator",(function(){return bt})),n.d(r,"toInt32ArrayAsyncIterator",(function(){return mt})),n.d(r,"toUint8ArrayAsyncIterator",(function(){return gt})),n.d(r,"toUint16ArrayAsyncIterator",(function(){return kt})),n.d(r,"toUint32ArrayAsyncIterator",(function(){return wt})),n.d(r,"toFloat32ArrayAsyncIterator",(function(){return _t})),n.d(r,"toFloat64ArrayAsyncIterator",(function(){return xt})),n.d(r,"toUint8ClampedArrayAsyncIterator",(function(){return St})),n.d(r,"rebaseValueOffsets",(function(){return Tt})),n.d(r,"compareArrayLike",(function(){return It}));var i={};n.r(i),n.d(i,"getBool",(function(){return on})),n.d(i,"getBit",(function(){return un})),n.d(i,"setBool",(function(){return sn})),n.d(i,"truncateBitmap",(function(){return ln})),n.d(i,"packBools",(function(){return cn})),n.d(i,"iterateBits",(function(){return fn})),n.d(i,"popcnt_bit_range",(function(){return dn})),n.d(i,"popcnt_array",(function(){return hn})),n.d(i,"popcnt_uint32",(function(){return pn}));var a={};n.r(a),n.d(a,"uint16ToFloat64",(function(){return Nr})),n.d(a,"float64ToUint16",(function(){return Pr}));var o={};n.r(o),n.d(o,"isArrowBigNumSymbol",(function(){return Yr})),n.d(o,"bignumToString",(function(){return Wr})),n.d(o,"bignumToBigInt",(function(){return Hr})),n.d(o,"BN",(function(){return Jr}));var u={};n.r(u),n.d(u,"clampIndex",(function(){return Pi})),n.d(u,"clampRange",(function(){return Ui})),n.d(u,"createElementComparator",(function(){return ji}));var s={};n.r(s),n.d(s,"BaseInt64",(function(){return no})),n.d(s,"Uint64",(function(){return ro})),n.d(s,"Int64",(function(){return io})),n.d(s,"Int128",(function(){return ao}));var l=n(10),c=n.n(l),f=n(1),d=n.n(f),h=new WeakMap,p=new WeakMap;function y(e){var t=h.get(e);return console.assert(null!=t,"'this' is expected an Event object, but got",e),t}function v(e){null==e.passiveListener?e.event.cancelable&&(e.canceled=!0,"function"===typeof e.event.preventDefault&&e.event.preventDefault()):"undefined"!==typeof console&&"function"===typeof console.error&&console.error("Unable to preventDefault inside passive event listener invocation.",e.passiveListener)}function b(e,t){h.set(this,{eventTarget:e,event:t,eventPhase:2,currentTarget:e,canceled:!1,stopped:!1,immediateStopped:!1,passiveListener:null,timeStamp:t.timeStamp||Date.now()}),Object.defineProperty(this,"isTrusted",{value:!1,enumerable:!0});for(var n=Object.keys(t),r=0;r<n.length;++r){var i=n[r];i in this||Object.defineProperty(this,i,m(i))}}function m(e){return{get:function(){return y(this).event[e]},set:function(t){y(this).event[e]=t},configurable:!0,enumerable:!0}}function g(e){return{value:function(){var t=y(this).event;return t[e].apply(t,arguments)},configurable:!0,enumerable:!0}}function k(e){if(null==e||e===Object.prototype)return b;var t=p.get(e);return null==t&&(t=function(e,t){var n=Object.keys(t);if(0===n.length)return e;function r(t,n){e.call(this,t,n)}r.prototype=Object.create(e.prototype,{constructor:{value:r,configurable:!0,writable:!0}});for(var i=0;i<n.length;++i){var a=n[i];if(!(a in e.prototype)){var o="function"===typeof Object.getOwnPropertyDescriptor(t,a).value;Object.defineProperty(r.prototype,a,o?g(a):m(a))}}return r}(k(Object.getPrototypeOf(e)),e),p.set(e,t)),t}function w(e){return y(e).immediateStopped}function _(e,t){y(e).passiveListener=t}b.prototype={get type(){return y(this).event.type},get target(){return y(this).eventTarget},get currentTarget(){return y(this).currentTarget},composedPath:function(){var e=y(this).currentTarget;return null==e?[]:[e]},get NONE(){return 0},get CAPTURING_PHASE(){return 1},get AT_TARGET(){return 2},get BUBBLING_PHASE(){return 3},get eventPhase(){return y(this).eventPhase},stopPropagation:function(){var e=y(this);e.stopped=!0,"function"===typeof e.event.stopPropagation&&e.event.stopPropagation()},stopImmediatePropagation:function(){var e=y(this);e.stopped=!0,e.immediateStopped=!0,"function"===typeof e.event.stopImmediatePropagation&&e.event.stopImmediatePropagation()},get bubbles(){return Boolean(y(this).event.bubbles)},get cancelable(){return Boolean(y(this).event.cancelable)},preventDefault:function(){v(y(this))},get defaultPrevented(){return y(this).canceled},get composed(){return Boolean(y(this).event.composed)},get timeStamp(){return y(this).timeStamp},get srcElement(){return y(this).eventTarget},get cancelBubble(){return y(this).stopped},set cancelBubble(e){if(e){var t=y(this);t.stopped=!0,"boolean"===typeof t.event.cancelBubble&&(t.event.cancelBubble=!0)}},get returnValue(){return!y(this).canceled},set returnValue(e){e||v(y(this))},initEvent:function(){}},Object.defineProperty(b.prototype,"constructor",{value:b,configurable:!0,writable:!0}),"undefined"!==typeof window&&"undefined"!==typeof window.Event&&(Object.setPrototypeOf(b.prototype,window.Event.prototype),p.set(window.Event.prototype,b));var x=new WeakMap;function S(e){return null!==e&&"object"===typeof e}function T(e){var t=x.get(e);if(null==t)throw new TypeError("'this' is expected an EventTarget object, but got another value.");return t}function I(e,t){Object.defineProperty(e,"on".concat(t),function(e){return{get:function(){for(var t=T(this).get(e);null!=t;){if(3===t.listenerType)return t.listener;t=t.next}return null},set:function(t){"function"===typeof t||S(t)||(t=null);for(var n=T(this),r=null,i=n.get(e);null!=i;)3===i.listenerType?null!==r?r.next=i.next:null!==i.next?n.set(e,i.next):n.delete(e):r=i,i=i.next;if(null!==t){var a={listener:t,listenerType:3,passive:!1,once:!1,next:null};null===r?n.set(e,a):r.next=a}},configurable:!0,enumerable:!0}}(t))}function E(e){function t(){A.call(this)}t.prototype=Object.create(A.prototype,{constructor:{value:t,configurable:!0,writable:!0}});for(var n=0;n<e.length;++n)I(t.prototype,e[n]);return t}function A(){if(!(this instanceof A)){if(1===arguments.length&&Array.isArray(arguments[0]))return E(arguments[0]);if(arguments.length>0){for(var e=new Array(arguments.length),t=0;t<arguments.length;++t)e[t]=arguments[t];return E(e)}throw new TypeError("Cannot call a class as a function")}x.set(this,new Map)}A.prototype={addEventListener:function(e,t,n){if(null!=t){if("function"!==typeof t&&!S(t))throw new TypeError("'listener' should be a function or an object.");var r=T(this),i=S(n),a=(i?Boolean(n.capture):Boolean(n))?1:2,o={listener:t,listenerType:a,passive:i&&Boolean(n.passive),once:i&&Boolean(n.once),next:null},u=r.get(e);if(void 0!==u){for(var s=null;null!=u;){if(u.listener===t&&u.listenerType===a)return;s=u,u=u.next}s.next=o}else r.set(e,o)}},removeEventListener:function(e,t,n){if(null!=t)for(var r=T(this),i=(S(n)?Boolean(n.capture):Boolean(n))?1:2,a=null,o=r.get(e);null!=o;){if(o.listener===t&&o.listenerType===i)return void(null!==a?a.next=o.next:null!==o.next?r.set(e,o.next):r.delete(e));a=o,o=o.next}},dispatchEvent:function(e){if(null==e||"string"!==typeof e.type)throw new TypeError('"event.type" should be a string.');var t=T(this),n=e.type,r=t.get(n);if(null==r)return!0;for(var i=function(e,t){return new(k(Object.getPrototypeOf(t)))(e,t)}(this,e),a=null;null!=r;){if(r.once?null!==a?a.next=r.next:null!==r.next?t.set(n,r.next):t.delete(n):a=r,_(i,r.passive?r.listener:null),"function"===typeof r.listener)try{r.listener.call(this,i)}catch(o){"undefined"!==typeof console&&"function"===typeof console.error&&console.error(o)}else 3!==r.listenerType&&"function"===typeof r.listener.handleEvent&&r.listener.handleEvent(i);if(w(i))break;r=r.next}return _(i,null),function(e,t){y(e).eventPhase=t}(i,0),function(e,t){y(e).currentTarget=t}(i,null),!i.defaultPrevented}},Object.defineProperty(A.prototype,"constructor",{value:A,configurable:!0,writable:!0}),"undefined"!==typeof window&&"undefined"!==typeof window.EventTarget&&Object.setPrototypeOf(A.prototype,window.EventTarget.prototype);var F=n(5);function O(e){if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(e=Object(F.a)(e))){var t=0,n=function(){};return{s:n,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,i,a=!0,o=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return a=e.done,e},e:function(e){o=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw i}}}}function B(e,t,n,r,i,a,o){try{var u=e[a](o),s=u.value}catch(l){return void n(l)}u.done?t(s):Promise.resolve(s).then(r,i)}function D(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){B(a,r,i,o,u,"next",e)}function u(e){B(a,r,i,o,u,"throw",e)}o(void 0)}))}}function C(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function L(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function M(e,t,n){return t&&L(e.prototype,t),n&&L(e,n),e}var N=n(0),P=n(2);function U(e){this.wrapped=e}function R(e){return new U(e)}function z(e){var t,n;function r(t,n){try{var a=e[t](n),o=a.value,u=o instanceof U;Promise.resolve(u?o.wrapped:o).then((function(e){u?r("return"===t?"return":"next",e):i(a.done?"return":"normal",e)}),(function(e){r("throw",e)}))}catch(s){i("throw",s)}}function i(e,i){switch(e){case"return":t.resolve({value:i,done:!0});break;case"throw":t.reject(i);break;default:t.resolve({value:i,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,i){return new Promise((function(a,o){var u={key:e,arg:i,resolve:a,reject:o,next:null};n?n=n.next=u:(t=n=u,r(e,i))}))},"function"!==typeof e.return&&(this.return=void 0)}function j(e){return function(){return new z(e.apply(this,arguments))}}function V(e){var t;if("undefined"!==typeof Symbol){if(Symbol.asyncIterator&&null!=(t=e[Symbol.asyncIterator]))return t.call(e);if(Symbol.iterator&&null!=(t=e[Symbol.iterator]))return t.call(e)}throw new TypeError("Object is not async iterable")}function W(e,t){var n={},r=!1;function i(n,i){return r=!0,i=new Promise((function(t){t(e[n](i))})),{done:!1,value:t(i)}}return"function"===typeof Symbol&&Symbol.iterator&&(n[Symbol.iterator]=function(){return this}),n.next=function(e){return r?(r=!1,e):i("next",e)},"function"===typeof e.throw&&(n.throw=function(e){if(r)throw r=!1,e;return i("throw",e)}),"function"===typeof e.return&&(n.return=function(e){return r?(r=!1,e):i("return",e)}),n}"function"===typeof Symbol&&Symbol.asyncIterator&&(z.prototype[Symbol.asyncIterator]=function(){return this}),z.prototype.next=function(e){return this._invoke("next",e)},z.prototype.throw=function(e){return this._invoke("throw",e)},z.prototype.return=function(e){return this._invoke("return",e)};var H={};function Y(e,t,n){return t<=e&&e<=n}function $(e){if(void 0===e)return{};if(e===Object(e))return e;throw TypeError("Could not convert argument to dictionary")}H.Offset,H.Table,H.SIZEOF_SHORT=2,H.SIZEOF_INT=4,H.FILE_IDENTIFIER_LENGTH=4,H.Encoding={UTF8_BYTES:1,UTF16_STRING:2},H.int32=new Int32Array(2),H.float32=new Float32Array(H.int32.buffer),H.float64=new Float64Array(H.int32.buffer),H.isLittleEndian=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0],H.Long=function(e,t){this.low=0|e,this.high=0|t},H.Long.create=function(e,t){return 0==e&&0==t?H.Long.ZERO:new H.Long(e,t)},H.Long.prototype.toFloat64=function(){return(this.low>>>0)+4294967296*this.high},H.Long.prototype.equals=function(e){return this.low==e.low&&this.high==e.high},H.Long.ZERO=new H.Long(0,0),H.Builder=function(e){if(e)t=e;else var t=1024;this.bb=H.ByteBuffer.allocate(t),this.space=t,this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1},H.Builder.prototype.clear=function(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1},H.Builder.prototype.forceDefaults=function(e){this.force_defaults=e},H.Builder.prototype.dataBuffer=function(){return this.bb},H.Builder.prototype.asUint8Array=function(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())},H.Builder.prototype.prep=function(e,t){e>this.minalign&&(this.minalign=e);for(var n=1+~(this.bb.capacity()-this.space+t)&e-1;this.space<n+e+t;){var r=this.bb.capacity();this.bb=H.Builder.growByteBuffer(this.bb),this.space+=this.bb.capacity()-r}this.pad(n)},H.Builder.prototype.pad=function(e){for(var t=0;t<e;t++)this.bb.writeInt8(--this.space,0)},H.Builder.prototype.writeInt8=function(e){this.bb.writeInt8(this.space-=1,e)},H.Builder.prototype.writeInt16=function(e){this.bb.writeInt16(this.space-=2,e)},H.Builder.prototype.writeInt32=function(e){this.bb.writeInt32(this.space-=4,e)},H.Builder.prototype.writeInt64=function(e){this.bb.writeInt64(this.space-=8,e)},H.Builder.prototype.writeFloat32=function(e){this.bb.writeFloat32(this.space-=4,e)},H.Builder.prototype.writeFloat64=function(e){this.bb.writeFloat64(this.space-=8,e)},H.Builder.prototype.addInt8=function(e){this.prep(1,0),this.writeInt8(e)},H.Builder.prototype.addInt16=function(e){this.prep(2,0),this.writeInt16(e)},H.Builder.prototype.addInt32=function(e){this.prep(4,0),this.writeInt32(e)},H.Builder.prototype.addInt64=function(e){this.prep(8,0),this.writeInt64(e)},H.Builder.prototype.addFloat32=function(e){this.prep(4,0),this.writeFloat32(e)},H.Builder.prototype.addFloat64=function(e){this.prep(8,0),this.writeFloat64(e)},H.Builder.prototype.addFieldInt8=function(e,t,n){(this.force_defaults||t!=n)&&(this.addInt8(t),this.slot(e))},H.Builder.prototype.addFieldInt16=function(e,t,n){(this.force_defaults||t!=n)&&(this.addInt16(t),this.slot(e))},H.Builder.prototype.addFieldInt32=function(e,t,n){(this.force_defaults||t!=n)&&(this.addInt32(t),this.slot(e))},H.Builder.prototype.addFieldInt64=function(e,t,n){!this.force_defaults&&t.equals(n)||(this.addInt64(t),this.slot(e))},H.Builder.prototype.addFieldFloat32=function(e,t,n){(this.force_defaults||t!=n)&&(this.addFloat32(t),this.slot(e))},H.Builder.prototype.addFieldFloat64=function(e,t,n){(this.force_defaults||t!=n)&&(this.addFloat64(t),this.slot(e))},H.Builder.prototype.addFieldOffset=function(e,t,n){(this.force_defaults||t!=n)&&(this.addOffset(t),this.slot(e))},H.Builder.prototype.addFieldStruct=function(e,t,n){t!=n&&(this.nested(t),this.slot(e))},H.Builder.prototype.nested=function(e){if(e!=this.offset())throw new Error("FlatBuffers: struct must be serialized inline.")},H.Builder.prototype.notNested=function(){if(this.isNested)throw new Error("FlatBuffers: object serialization must not be nested.")},H.Builder.prototype.slot=function(e){this.vtable[e]=this.offset()},H.Builder.prototype.offset=function(){return this.bb.capacity()-this.space},H.Builder.growByteBuffer=function(e){var t=e.capacity();if(3221225472&t)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");var n=t<<1,r=H.ByteBuffer.allocate(n);return r.setPosition(n-t),r.bytes().set(e.bytes(),n-t),r},H.Builder.prototype.addOffset=function(e){this.prep(H.SIZEOF_INT,0),this.writeInt32(this.offset()-e+H.SIZEOF_INT)},H.Builder.prototype.startObject=function(e){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=e;for(var t=0;t<e;t++)this.vtable[t]=0;this.isNested=!0,this.object_start=this.offset()},H.Builder.prototype.endObject=function(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);for(var e=this.offset(),t=this.vtable_in_use-1;t>=0&&0==this.vtable[t];t--);for(var n=t+1;t>=0;t--)this.addInt16(0!=this.vtable[t]?e-this.vtable[t]:0);this.addInt16(e-this.object_start);var r=(n+2)*H.SIZEOF_SHORT;this.addInt16(r);var i=0,a=this.space;e:for(t=0;t<this.vtables.length;t++){var o=this.bb.capacity()-this.vtables[t];if(r==this.bb.readInt16(o)){for(var u=H.SIZEOF_SHORT;u<r;u+=H.SIZEOF_SHORT)if(this.bb.readInt16(a+u)!=this.bb.readInt16(o+u))continue e;i=this.vtables[t];break}}return i?(this.space=this.bb.capacity()-e,this.bb.writeInt32(this.space,i-e)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-e,this.offset()-e)),this.isNested=!1,e},H.Builder.prototype.finish=function(e,t){if(t){var n=t;if(this.prep(this.minalign,H.SIZEOF_INT+H.FILE_IDENTIFIER_LENGTH),n.length!=H.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+H.FILE_IDENTIFIER_LENGTH);for(var r=H.FILE_IDENTIFIER_LENGTH-1;r>=0;r--)this.writeInt8(n.charCodeAt(r))}this.prep(this.minalign,H.SIZEOF_INT),this.addOffset(e),this.bb.setPosition(this.space)},H.Builder.prototype.requiredField=function(e,t){var n=this.bb.capacity()-e,r=n-this.bb.readInt32(n);if(!(0!=this.bb.readInt16(r+t)))throw new Error("FlatBuffers: field "+t+" must be set")},H.Builder.prototype.startVector=function(e,t,n){this.notNested(),this.vector_num_elems=t,this.prep(H.SIZEOF_INT,e*t),this.prep(n,e*t)},H.Builder.prototype.endVector=function(){return this.writeInt32(this.vector_num_elems),this.offset()},H.Builder.prototype.createString=function(e){if(e instanceof Uint8Array)var t=e;else{t=[];for(var n=0;n<e.length;){var r,i=e.charCodeAt(n++);if(i<55296||i>=56320)r=i;else r=(i<<10)+e.charCodeAt(n++)+-56613888;r<128?t.push(r):(r<2048?t.push(r>>6&31|192):(r<65536?t.push(r>>12&15|224):t.push(r>>18&7|240,r>>12&63|128),t.push(r>>6&63|128)),t.push(63&r|128))}}this.addInt8(0),this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length);n=0;for(var a=this.space,o=this.bb.bytes();n<t.length;n++)o[a++]=t[n];return this.endVector()},H.Builder.prototype.createLong=function(e,t){return H.Long.create(e,t)},H.ByteBuffer=function(e){this.bytes_=e,this.position_=0},H.ByteBuffer.allocate=function(e){return new H.ByteBuffer(new Uint8Array(e))},H.ByteBuffer.prototype.clear=function(){this.position_=0},H.ByteBuffer.prototype.bytes=function(){return this.bytes_},H.ByteBuffer.prototype.position=function(){return this.position_},H.ByteBuffer.prototype.setPosition=function(e){this.position_=e},H.ByteBuffer.prototype.capacity=function(){return this.bytes_.length},H.ByteBuffer.prototype.readInt8=function(e){return this.readUint8(e)<<24>>24},H.ByteBuffer.prototype.readUint8=function(e){return this.bytes_[e]},H.ByteBuffer.prototype.readInt16=function(e){return this.readUint16(e)<<16>>16},H.ByteBuffer.prototype.readUint16=function(e){return this.bytes_[e]|this.bytes_[e+1]<<8},H.ByteBuffer.prototype.readInt32=function(e){return this.bytes_[e]|this.bytes_[e+1]<<8|this.bytes_[e+2]<<16|this.bytes_[e+3]<<24},H.ByteBuffer.prototype.readUint32=function(e){return this.readInt32(e)>>>0},H.ByteBuffer.prototype.readInt64=function(e){return new H.Long(this.readInt32(e),this.readInt32(e+4))},H.ByteBuffer.prototype.readUint64=function(e){return new H.Long(this.readUint32(e),this.readUint32(e+4))},H.ByteBuffer.prototype.readFloat32=function(e){return H.int32[0]=this.readInt32(e),H.float32[0]},H.ByteBuffer.prototype.readFloat64=function(e){return H.int32[H.isLittleEndian?0:1]=this.readInt32(e),H.int32[H.isLittleEndian?1:0]=this.readInt32(e+4),H.float64[0]},H.ByteBuffer.prototype.writeInt8=function(e,t){this.bytes_[e]=t},H.ByteBuffer.prototype.writeUint8=function(e,t){this.bytes_[e]=t},H.ByteBuffer.prototype.writeInt16=function(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8},H.ByteBuffer.prototype.writeUint16=function(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8},H.ByteBuffer.prototype.writeInt32=function(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8,this.bytes_[e+2]=t>>16,this.bytes_[e+3]=t>>24},H.ByteBuffer.prototype.writeUint32=function(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8,this.bytes_[e+2]=t>>16,this.bytes_[e+3]=t>>24},H.ByteBuffer.prototype.writeInt64=function(e,t){this.writeInt32(e,t.low),this.writeInt32(e+4,t.high)},H.ByteBuffer.prototype.writeUint64=function(e,t){this.writeUint32(e,t.low),this.writeUint32(e+4,t.high)},H.ByteBuffer.prototype.writeFloat32=function(e,t){H.float32[0]=t,this.writeInt32(e,H.int32[0])},H.ByteBuffer.prototype.writeFloat64=function(e,t){H.float64[0]=t,this.writeInt32(e,H.int32[H.isLittleEndian?0:1]),this.writeInt32(e+4,H.int32[H.isLittleEndian?1:0])},H.ByteBuffer.prototype.getBufferIdentifier=function(){if(this.bytes_.length<this.position_+H.SIZEOF_INT+H.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");for(var e="",t=0;t<H.FILE_IDENTIFIER_LENGTH;t++)e+=String.fromCharCode(this.readInt8(this.position_+H.SIZEOF_INT+t));return e},H.ByteBuffer.prototype.__offset=function(e,t){var n=e-this.readInt32(e);return t<this.readInt16(n)?this.readInt16(n+t):0},H.ByteBuffer.prototype.__union=function(e,t){return e.bb_pos=t+this.readInt32(t),e.bb=this,e},H.ByteBuffer.prototype.__string=function(e,t){e+=this.readInt32(e);var n=this.readInt32(e),r="",i=0;if(e+=H.SIZEOF_INT,t===H.Encoding.UTF8_BYTES)return this.bytes_.subarray(e,e+n);for(;i<n;){var a,o=this.readUint8(e+i++);if(o<192)a=o;else{var u=this.readUint8(e+i++);if(o<224)a=(31&o)<<6|63&u;else{var s=this.readUint8(e+i++);if(o<240)a=(15&o)<<12|(63&u)<<6|63&s;else a=(7&o)<<18|(63&u)<<12|(63&s)<<6|63&this.readUint8(e+i++)}}a<65536?r+=String.fromCharCode(a):(a-=65536,r+=String.fromCharCode(55296+(a>>10),56320+(1023&a)))}return r},H.ByteBuffer.prototype.__indirect=function(e){return e+this.readInt32(e)},H.ByteBuffer.prototype.__vector=function(e){return e+this.readInt32(e)+H.SIZEOF_INT},H.ByteBuffer.prototype.__vector_len=function(e){return this.readInt32(e+this.readInt32(e))},H.ByteBuffer.prototype.__has_identifier=function(e){if(e.length!=H.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+H.FILE_IDENTIFIER_LENGTH);for(var t=0;t<H.FILE_IDENTIFIER_LENGTH;t++)if(e.charCodeAt(t)!=this.readInt8(this.position_+H.SIZEOF_INT+t))return!1;return!0},H.ByteBuffer.prototype.createLong=function(e,t){return H.Long.create(e,t)};function K(e){this.tokens=[].slice.call(e)}K.prototype={endOfStream:function(){return!this.tokens.length},read:function(){return this.tokens.length?this.tokens.shift():-1},prepend:function(e){if(Array.isArray(e))for(var t=e;t.length;)this.tokens.unshift(t.pop());else this.tokens.unshift(e)},push:function(e){if(Array.isArray(e))for(var t=e;t.length;)this.tokens.push(t.shift());else this.tokens.push(e)}};function Q(e,t){if(e)throw TypeError("Decoder error");return t||65533}function G(e,t){if(!(this instanceof G))return new G(e,t);if("utf-8"!==(e=void 0!==e?String(e).toLowerCase():"utf-8"))throw new Error("Encoding not supported. Only utf-8 is supported");t=$(t),this._streaming=!1,this._BOMseen=!1,this._decoder=null,this._fatal=Boolean(t.fatal),this._ignoreBOM=Boolean(t.ignoreBOM),Object.defineProperty(this,"encoding",{value:"utf-8"}),Object.defineProperty(this,"fatal",{value:this._fatal}),Object.defineProperty(this,"ignoreBOM",{value:this._ignoreBOM})}function q(e,t){if(!(this instanceof q))return new q(e,t);if("utf-8"!==(e=void 0!==e?String(e).toLowerCase():"utf-8"))throw new Error("Encoding not supported. Only utf-8 is supported");t=$(t),this._streaming=!1,this._encoder=null,this._options={fatal:Boolean(t.fatal)},Object.defineProperty(this,"encoding",{value:"utf-8"})}function X(e){var t=e.fatal,n=0,r=0,i=0,a=128,o=191;this.handler=function(e,u){if(-1===u&&0!==i)return i=0,Q(t);if(-1===u)return-1;if(0===i){if(Y(u,0,127))return u;if(Y(u,194,223))i=1,n=u-192;else if(Y(u,224,239))224===u&&(a=160),237===u&&(o=159),i=2,n=u-224;else{if(!Y(u,240,244))return Q(t);240===u&&(a=144),244===u&&(o=143),i=3,n=u-240}return n<<=6*i,null}if(!Y(u,a,o))return n=i=r=0,a=128,o=191,e.prepend(u),Q(t);if(a=128,o=191,n+=u-128<<6*(i-(r+=1)),r!==i)return null;var s=n;return n=i=r=0,s}}function Z(e){e.fatal;this.handler=function(e,t){if(-1===t)return-1;if(Y(t,0,127))return t;var n,r;Y(t,128,2047)?(n=1,r=192):Y(t,2048,65535)?(n=2,r=224):Y(t,65536,1114111)&&(n=3,r=240);for(var i=[(t>>6*n)+r];n>0;){var a=t>>6*(n-1);i.push(128|63&a),n-=1}return i}}G.prototype={decode:function(e,t){var n;n="object"===typeof e&&e instanceof ArrayBuffer?new Uint8Array(e):"object"===typeof e&&"buffer"in e&&e.buffer instanceof ArrayBuffer?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):new Uint8Array(0),t=$(t),this._streaming||(this._decoder=new X({fatal:this._fatal}),this._BOMseen=!1),this._streaming=Boolean(t.stream);for(var r,i=new K(n),a=[];!i.endOfStream()&&-1!==(r=this._decoder.handler(i,i.read()));)null!==r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r));if(!this._streaming){do{if(-1===(r=this._decoder.handler(i,i.read())))break;null!==r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r))}while(!i.endOfStream());this._decoder=null}return a.length&&(-1===["utf-8"].indexOf(this.encoding)||this._ignoreBOM||this._BOMseen||(65279===a[0]?(this._BOMseen=!0,a.shift()):this._BOMseen=!0)),function(e){for(var t="",n=0;n<e.length;++n){var r=e[n];r<=65535?t+=String.fromCharCode(r):(r-=65536,t+=String.fromCharCode(55296+(r>>10),56320+(1023&r)))}return t}(a)}},q.prototype={encode:function(e,t){e=e?String(e):"",t=$(t),this._streaming||(this._encoder=new Z(this._options)),this._streaming=Boolean(t.stream);for(var n,r=[],i=new K(function(e){for(var t=String(e),n=t.length,r=0,i=[];r<n;){var a=t.charCodeAt(r);if(a<55296||a>57343)i.push(a);else if(56320<=a&&a<=57343)i.push(65533);else if(55296<=a&&a<=56319)if(r===n-1)i.push(65533);else{var o=e.charCodeAt(r+1);if(56320<=o&&o<=57343){var u=1023&a,s=1023&o;i.push(65536+(u<<10)+s),r+=1}else i.push(65533)}r+=1}return i}(e));!i.endOfStream()&&-1!==(n=this._encoder.handler(i,i.read()));)Array.isArray(n)?r.push.apply(r,n):r.push(n);if(!this._streaming){for(;-1!==(n=this._encoder.handler(i,i.read()));)Array.isArray(n)?r.push.apply(r,n):r.push(n);this._encoder=null}return new Uint8Array(r)}};var J="function"===typeof Buffer?Buffer:null,ee="function"===typeof TextDecoder&&"function"===typeof TextEncoder,te=function(e){if(ee||!J){var t=new e("utf-8");return function(e){return t.decode(e)}}return function(e){var t=qe(e),n=t.buffer,r=t.byteOffset,i=t.length;return J.from(n,r,i).toString()}}("undefined"!==typeof TextDecoder?TextDecoder:G),ne=function(e){if(ee||!J){var t=new e;return function(e){return t.encode(e)}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return qe(J.from(e,"utf8"))}}("undefined"!==typeof TextEncoder?TextEncoder:q);function re(e,t){return(re=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ie(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&re(e,t)}function ae(e){return(ae=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function oe(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function ue(e){return(ue="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function se(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function le(e,t){return!t||"object"!==ue(t)&&"function"!==typeof t?se(e):t}function ce(e){return function(){var t,n=ae(e);if(oe()){var r=ae(this).constructor;t=Reflect.construct(n,arguments,r)}else t=n.apply(this,arguments);return le(this,t)}}var fe=Object.freeze({done:!0,value:void 0}),de=function(){function e(t){C(this,e),this._json=t}return M(e,[{key:"schema",get:function(){return this._json.schema}},{key:"batches",get:function(){return this._json.batches||[]}},{key:"dictionaries",get:function(){return this._json.dictionaries||[]}}]),e}(),he=function(){function e(){C(this,e)}return M(e,[{key:"tee",value:function(){return this._getDOMStream().tee()}},{key:"pipe",value:function(e,t){return this._getNodeStream().pipe(e,t)}},{key:"pipeTo",value:function(e,t){return this._getDOMStream().pipeTo(e,t)}},{key:"pipeThrough",value:function(e,t){return this._getDOMStream().pipeThrough(e,t)}},{key:"_getDOMStream",value:function(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}},{key:"_getNodeStream",value:function(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}]),e}(),pe=function(e){ie(n,e);var t=ce(n);function n(){var e;return C(this,n),(e=t.call(this))._values=[],e.resolvers=[],e._closedPromise=new Promise((function(t){return e._closedPromiseResolve=t})),e}return M(n,[{key:"closed",get:function(){return this._closedPromise}},{key:"cancel",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.return(t);case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"write",value:function(e){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(e):this.resolvers.shift().resolve({done:!1,value:e}))}},{key:"abort",value:function(e){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:e}:this.resolvers.shift().reject({done:!0,value:e}))}},{key:"close",value:function(){if(this._closedPromiseResolve){for(var e=this.resolvers;e.length>0;)e.shift().resolve(fe);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}},{key:Symbol.asyncIterator,value:function(){return this}},{key:"toDOMStream",value:function(e){return At.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,e)}},{key:"toNodeStream",value:function(e){return At.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,e)}},{key:"throw",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.abort(t);case 2:return e.abrupt("return",fe);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.close();case 2:return e.abrupt("return",fe);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"read",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next(t,"read");case 2:return e.abrupt("return",e.sent.value);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"peek",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next(t,"peek");case 2:return e.abrupt("return",e.sent.value);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"next",value:function(){var e=this;return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise((function(t,n){e.resolvers.push({resolve:t,reject:n})})):Promise.resolve(fe)}},{key:"_ensureOpen",value:function(){if(this._closedPromiseResolve)return!0;throw new Error("".concat(this," is closed"))}}]),n}(he),ye=function(){var e=function(){throw new Error("BigInt is not available in this environment")};function t(){throw e()}return t.asIntN=function(){throw e()},t.asUintN=function(){throw e()},"undefined"!==typeof BigInt?[BigInt,!0]:[t,!1]}(),ve=Object(P.a)(ye,2),be=ve[0],me=ve[1],ge=function(){var e=function(){throw new Error("BigInt64Array is not available in this environment")};return"undefined"!==typeof BigInt64Array?[BigInt64Array,!0]:[function(){function t(){throw C(this,t),e()}return M(t,null,[{key:"BYTES_PER_ELEMENT",get:function(){return 8}},{key:"of",value:function(){throw e()}},{key:"from",value:function(){throw e()}}]),t}(),!1]}(),ke=Object(P.a)(ge,2),we=ke[0],_e=(ke[1],function(){var e=function(){throw new Error("BigUint64Array is not available in this environment")};return"undefined"!==typeof BigUint64Array?[BigUint64Array,!0]:[function(){function t(){throw C(this,t),e()}return M(t,null,[{key:"BYTES_PER_ELEMENT",get:function(){return 8}},{key:"of",value:function(){throw e()}},{key:"from",value:function(){throw e()}}]),t}(),!1]}()),xe=Object(P.a)(_e,2),Se=xe[0],Te=(xe[1],function(e){return"number"===typeof e}),Ie=function(e){return"boolean"===typeof e},Ee=function(e){return"function"===typeof e},Ae=function(e){return null!=e&&Object(e)===e},Fe=function(e){return Ae(e)&&Ee(e.then)},Oe=function(e){return Ae(e)&&Ee(e[Symbol.iterator])},Be=function(e){return Ae(e)&&Ee(e[Symbol.asyncIterator])},De=function(e){return Ae(e)&&Ae(e.schema)},Ce=function(e){return Ae(e)&&"done"in e&&"value"in e},Le=function(e){return Ae(e)&&Ee(e.stat)&&Te(e.fd)},Me=function(e){return Ae(e)&&Pe(e.body)},Ne=function(e){return Ae(e)&&Ee(e.abort)&&Ee(e.getWriter)&&!(e instanceof he)},Pe=function(e){return Ae(e)&&Ee(e.cancel)&&Ee(e.getReader)&&!(e instanceof he)},Ue=function(e){return Ae(e)&&Ee(e.end)&&Ee(e.write)&&Ie(e.writable)&&!(e instanceof he)},Re=function(e){return Ae(e)&&Ee(e.read)&&Ee(e.pipe)&&Ie(e.readable)&&!(e instanceof he)},ze=N.mark(it),je=H.ByteBuffer,Ve="undefined"!==typeof SharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;function We(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.byteLength,i=e.byteLength,a=new Uint8Array(e.buffer,e.byteOffset,i),o=new Uint8Array(t.buffer,t.byteOffset,Math.min(r,i));return a.set(o,n),e}function He(e,t){for(var n,r,i,a=function(e){for(var t,n,r,i,a,o,u=e[0]?[e[0]]:[],s=0,l=0,c=e.length;++s<c;)if(a=u[l],o=e[s],!a||!o||a.buffer!==o.buffer||o.byteOffset<a.byteOffset)o&&(u[++l]=o);else{var f=a;t=f.byteOffset,r=f.byteLength;var d=o;n=d.byteOffset,i=d.byteLength,t+r<n||n+i<t?o&&(u[++l]=o):u[l]=new Uint8Array(a.buffer,t,n-t+i)}return u}(e),o=a.reduce((function(e,t){return e+t.byteLength}),0),u=0,s=-1,l=Math.min(t||1/0,o),c=a.length;++s<c;){if(l<=u+(r=(n=a[s]).subarray(0,Math.min(n.length,l-u))).length){r.length<n.length?a[s]=n.subarray(r.length):r.length===n.length&&s++,i?We(i,r,u):i=r;break}We(i||(i=new Uint8Array(l)),r,u),u+=r.length}return[i||new Uint8Array(0),a.slice(s),o-(i?i.byteLength:0)]}function Ye(e,t){var n=Ce(t)?t.value:t;return n instanceof e?e===Uint8Array?new e(n.buffer,n.byteOffset,n.byteLength):n:n?("string"===typeof n&&(n=ne(n)),n instanceof ArrayBuffer||n instanceof Ve?new e(n):n instanceof je?Ye(e,n.bytes()):ArrayBuffer.isView(n)?n.byteLength<=0?new e(0):new e(n.buffer,n.byteOffset,n.byteLength/e.BYTES_PER_ELEMENT):e.from(n)):new e(0)}var $e=function(e){return Ye(Int8Array,e)},Ke=function(e){return Ye(Int16Array,e)},Qe=function(e){return Ye(Int32Array,e)},Ge=function(e){return Ye(we,e)},qe=function(e){return Ye(Uint8Array,e)},Xe=function(e){return Ye(Uint16Array,e)},Ze=function(e){return Ye(Uint32Array,e)},Je=function(e){return Ye(Se,e)},et=function(e){return Ye(Float32Array,e)},tt=function(e){return Ye(Float64Array,e)},nt=function(e){return Ye(Uint8ClampedArray,e)},rt=function(e){return e.next(),e};function it(e,t){var n,r;return N.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return n=N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t;case 2:case"end":return e.stop()}}),e)})),r="string"===typeof t||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof Ve?n(t):Oe(t)?t:n(t),i.delegateYield(rt(N.mark((function t(n){var r;return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=null;case 1:return t.t0=n,t.next=4,Ye(e,r);case 4:t.t1=t.sent,r=t.t0.next.call(t.t0,t.t1);case 6:if(!r.done){t.next=1;break}case 7:case"end":return t.stop()}}),t)}))(r[Symbol.iterator]())),"t0",3);case 3:case"end":return i.stop()}}),ze)}var at=function(e){return it(Int8Array,e)},ot=function(e){return it(Int16Array,e)},ut=function(e){return it(Int32Array,e)},st=function(e){return it(Uint8Array,e)},lt=function(e){return it(Uint16Array,e)},ct=function(e){return it(Uint32Array,e)},ft=function(e){return it(Float32Array,e)},dt=function(e){return it(Float64Array,e)},ht=function(e){return it(Uint8ClampedArray,e)};function pt(e,t){return yt.apply(this,arguments)}function yt(){return(yt=j(N.mark((function e(t,n){var r,i,a;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!Fe(n)){e.next=13;break}return e.t0=W,e.t1=V,e.t2=pt,e.t3=t,e.next=7,R(n);case 7:return e.t4=e.sent,e.t5=(0,e.t2)(e.t3,e.t4),e.t6=(0,e.t1)(e.t5),e.t7=R,e.delegateYield((0,e.t0)(e.t6,e.t7),"t8",12);case 12:return e.abrupt("return",e.t8);case 13:return r=function(){var e=j(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t;case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),i=function(){var e=j(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.delegateYield(W(V(rt(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=null;case 1:return e.t0=t,e.next=4,n&&n.value;case 4:e.t1=e.sent,n=e.t0.next.call(e.t0,e.t1);case 6:if(!n.done){e.next=1;break}case 7:case"end":return e.stop()}}),e)}))(t[Symbol.iterator]()))),R),"t0",1);case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),a="string"===typeof n||ArrayBuffer.isView(n)||n instanceof ArrayBuffer||n instanceof Ve?r(n):Oe(n)?i(n):Be(n)?n:r(n),e.delegateYield(W(V(rt(function(){var e=j(N.mark((function e(n){var r;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=null;case 1:return e.t0=R,e.t1=n,e.next=5,Ye(t,r);case 5:return e.t2=e.sent,e.t3=e.t1.next.call(e.t1,e.t2),e.next=9,(0,e.t0)(e.t3);case 9:r=e.sent;case 10:if(!r.done){e.next=1;break}case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()(a[Symbol.asyncIterator]()))),R),"t9",17);case 17:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var vt=function(e){return pt(Int8Array,e)},bt=function(e){return pt(Int16Array,e)},mt=function(e){return pt(Int32Array,e)},gt=function(e){return pt(Uint8Array,e)},kt=function(e){return pt(Uint16Array,e)},wt=function(e){return pt(Uint32Array,e)},_t=function(e){return pt(Float32Array,e)},xt=function(e){return pt(Float64Array,e)},St=function(e){return pt(Uint8ClampedArray,e)};function Tt(e,t,n){if(0!==e){n=n.slice(0,t+1);for(var r=-1;++r<=t;)n[r]+=e}return n}function It(e,t){var n=0,r=e.length;if(r!==t.length)return!1;if(r>0)do{if(e[n]!==t[n])return!1}while(++n<r);return!0}var Et=N.mark(Ot),At={fromIterable:function(e){return Ft(Ot(e))},fromAsyncIterable:function(e){return Ft(function(e){return Bt.apply(this,arguments)}(e))},fromDOMStream:function(e){return Ft(function(e){return Dt.apply(this,arguments)}(e))},fromNodeStream:function(e){return Ft(function(e){return Pt.apply(this,arguments)}(e))},toDOMStream:function(e,t){throw new Error('"toDOMStream" not available in this environment')},toNodeStream:function(e,t){throw new Error('"toNodeStream" not available in this environment')}},Ft=function(e){return e.next(),e};function Ot(e){var t,n,r,i,a,o,u,s,l,c,f,d;return N.wrap((function(h){for(;;)switch(h.prev=h.next){case 0:return s=function(){if("peek"===a)return He(r,o)[0];var e=He(r,o),t=Object(P.a)(e,3);return i=t[0],r=t[1],u=t[2],i},n=!1,r=[],u=0,h.next=6,null;case 6:l=h.sent,a=l.cmd,o=l.size,c=st(e)[Symbol.iterator](),h.prev=10;case 11:if(f=isNaN(o-u)?c.next(void 0):c.next(o-u),t=f.done,i=f.value,!t&&i.byteLength>0&&(r.push(i),u+=i.byteLength),!(t||o<=u)){h.next=22;break}case 16:return h.next=18,s();case 18:d=h.sent,a=d.cmd,o=d.size;case 21:if(o<u){h.next=16;break}case 22:if(!t){h.next=11;break}case 23:h.next=28;break;case 25:h.prev=25,h.t0=h.catch(10),(n=!0)&&"function"===typeof c.throw&&c.throw(h.t0);case 28:return h.prev=28,!1===n&&"function"===typeof c.return&&c.return(),h.finish(28);case 31:case"end":return h.stop()}}),Et,null,[[10,25,28,31]])}function Bt(){return(Bt=j(N.mark((function e(t){var n,r,i,a,o,u,s,l,c,f,d,h;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return l=function(){if("peek"===o)return He(i,u)[0];var e=He(i,u),t=Object(P.a)(e,3);return a=t[0],i=t[1],s=t[2],a},r=!1,i=[],s=0,e.next=6,null;case 6:c=e.sent,o=c.cmd,u=c.size,f=gt(t)[Symbol.asyncIterator](),e.prev=10;case 11:if(!isNaN(u-s)){e.next=17;break}return e.next=14,R(f.next(void 0));case 14:e.t0=e.sent,e.next=20;break;case 17:return e.next=19,R(f.next(u-s));case 19:e.t0=e.sent;case 20:if(d=e.t0,n=d.done,a=d.value,!n&&a.byteLength>0&&(i.push(a),s+=a.byteLength),!(n||u<=s)){e.next=31;break}case 25:return e.next=27,l();case 27:h=e.sent,o=h.cmd,u=h.size;case 30:if(u<s){e.next=25;break}case 31:if(!n){e.next=11;break}case 32:e.next=40;break;case 34:if(e.prev=34,e.t1=e.catch(10),e.t2=(r=!0)&&"function"===typeof f.throw,!e.t2){e.next=40;break}return e.next=40,R(f.throw(e.t1));case 40:if(e.prev=40,e.t3=!1===r&&"function"===typeof f.return,!e.t3){e.next=45;break}return e.next=45,R(f.return());case 45:return e.finish(40);case 46:case"end":return e.stop()}}),e,null,[[10,34,40,46]])})))).apply(this,arguments)}function Dt(){return(Dt=j(N.mark((function e(t){var n,r,i,a,o,u,s,l,c,f,d,h;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return l=function(){if("peek"===o)return He(i,u)[0];var e=He(i,u),t=Object(P.a)(e,3);return a=t[0],i=t[1],s=t[2],a},n=!1,r=!1,i=[],s=0,e.next=6,null;case 6:c=e.sent,o=c.cmd,u=c.size,f=new Ct(t),e.prev=10;case 11:if(!isNaN(u-s)){e.next=17;break}return e.next=14,R(f.read(void 0));case 14:e.t0=e.sent,e.next=20;break;case 17:return e.next=19,R(f.read(u-s));case 19:e.t0=e.sent;case 20:if(d=e.t0,n=d.done,a=d.value,!n&&a.byteLength>0&&(i.push(qe(a)),s+=a.byteLength),!(n||u<=s)){e.next=31;break}case 25:return e.next=27,l();case 27:h=e.sent,o=h.cmd,u=h.size;case 30:if(u<s){e.next=25;break}case 31:if(!n){e.next=11;break}case 32:e.next=40;break;case 34:if(e.prev=34,e.t1=e.catch(10),e.t2=r=!0,!e.t2){e.next=40;break}return e.next=40,R(f.cancel(e.t1));case 40:if(e.prev=40,!1!==r){e.next=46;break}return e.next=44,R(f.cancel());case 44:e.next=47;break;case 46:t.locked&&f.releaseLock();case 47:return e.finish(40);case 48:case"end":return e.stop()}}),e,null,[[10,34,40,48]])})))).apply(this,arguments)}var Ct=function(){function e(t){C(this,e),this.source=t,this.byobReader=null,this.defaultReader=null;try{this.supportsBYOB=!!(this.reader=this.getBYOBReader())}catch(n){this.supportsBYOB=!(this.reader=this.getDefaultReader())}}return M(e,[{key:"closed",get:function(){return this.reader?this.reader.closed.catch((function(){})):Promise.resolve()}},{key:"releaseLock",value:function(){this.reader&&this.reader.releaseLock(),this.reader=this.byobReader=this.defaultReader=null}},{key:"cancel",value:function(){var e=D(N.mark((function e(t){var n,r;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this.reader,r=this.source,e.t0=n,!e.t0){e.next=5;break}return e.next=5,n.cancel(t).catch((function(){}));case 5:r&&r.locked&&this.releaseLock();case 6:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"read",value:function(){var e=D(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==t){e.next=2;break}return e.abrupt("return",{done:null==this.reader,value:new Uint8Array(0)});case 2:if(this.supportsBYOB&&"number"===typeof t){e.next=8;break}return e.next=5,this.getDefaultReader().read();case 5:e.t0=e.sent,e.next=11;break;case 8:return e.next=10,this.readFromBYOBReader(t);case 10:e.t0=e.sent;case 11:return!(n=e.t0).done&&(n.value=qe(n)),e.abrupt("return",n);case 14:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getDefaultReader",value:function(){return this.byobReader&&this.releaseLock(),this.defaultReader||(this.defaultReader=this.source.getReader(),this.defaultReader.closed.catch((function(){}))),this.reader=this.defaultReader}},{key:"getBYOBReader",value:function(){return this.defaultReader&&this.releaseLock(),this.byobReader||(this.byobReader=this.source.getReader({mode:"byob"}),this.byobReader.closed.catch((function(){}))),this.reader=this.byobReader}},{key:"readFromBYOBReader",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Lt(this.getBYOBReader(),new ArrayBuffer(t),0,t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}();function Lt(e,t,n,r){return Mt.apply(this,arguments)}function Mt(){return(Mt=D(N.mark((function e(t,n,r,i){var a,o,u;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(r>=i)){e.next=2;break}return e.abrupt("return",{done:!1,value:new Uint8Array(n,0,i)});case 2:return e.next=4,t.read(new Uint8Array(n,r,i-r));case 4:if(a=e.sent,o=a.done,u=a.value,!((r+=u.byteLength)<i)||o){e.next=11;break}return e.next=10,Lt(t,u.buffer,r,i);case 10:return e.abrupt("return",e.sent);case 11:return e.abrupt("return",{done:o,value:new Uint8Array(u.buffer,0,r)});case 12:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var Nt=function(e,t){var n,r=function(e){return n([t,e])};return[t,r,new Promise((function(i){return(n=i)&&e.once(t,r)}))]};function Pt(){return(Pt=j(N.mark((function e(t){var n,r,i,a,o,u,s,l,c,f,d,h,p,y,v;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return v=function(e,n){return c=l=null,new Promise(function(){var r=D(N.mark((function r(i,a){var o,u,s,l,c,f;return N.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:o=O(e);try{for(o.s();!(u=o.n()).done;)s=Object(P.a)(u.value,2),l=s[0],c=s[1],t.off(l,c)}catch(n){o.e(n)}finally{o.f()}try{(f=t.destroy)&&f.call(t,n),n=void 0}catch(d){n=d||n}finally{null!=n?a(n):i()}case 3:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}())},f=function(){if("peek"===o)return He(l,u)[0];var e=He(l,u),t=Object(P.a)(e,3);return c=t[0],l=t[1],s=t[2],c},n=[],r="error",i=!1,a=null,s=0,l=[],e.next=9,null;case 9:if(d=e.sent,o=d.cmd,u=d.size,!t.isTTY){e.next=16;break}return e.next=15,new Uint8Array(0);case 15:return e.abrupt("return",e.sent);case 16:e.prev=16,n[0]=Nt(t,"end"),n[1]=Nt(t,"error");case 19:return n[2]=Nt(t,"readable"),e.next=22,R(Promise.race(n.map((function(e){return e[2]}))));case 22:if(h=e.sent,p=Object(P.a)(h,2),r=p[0],a=p[1],"error"!==r){e.next=28;break}return e.abrupt("break",37);case 28:if((i="end"===r)||(isFinite(u-s)?(c=qe(t.read(u-s))).byteLength<u-s&&(c=qe(t.read(void 0))):c=qe(t.read(void 0)),c.byteLength>0&&(l.push(c),s+=c.byteLength)),!(i||u<=s)){e.next=36;break}case 30:return e.next=32,f();case 32:y=e.sent,o=y.cmd,u=y.size;case 35:if(u<s){e.next=30;break}case 36:if(!i){e.next=19;break}case 37:return e.prev=37,e.next=40,R(v(n,"error"===r?a:null));case 40:return e.finish(37);case 41:case"end":return e.stop()}}),e,null,[[16,,37,41]])})))).apply(this,arguments)}var Ut=n(3);function Rt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function zt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Rt(Object(n),!0).forEach((function(t){Object(Ut.a)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Rt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function jt(e,t,n){return(jt="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=ae(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(n):i.value}})(e,t,n||e)}var Vt,Wt,Ht,Yt,$t,Kt,Qt=function e(){C(this,e)};$t=Vt||(Vt={}),Yt=$t.apache||($t.apache={}),Ht=Yt.arrow||(Yt.arrow={}),function(e){e[e.V1=0]="V1",e[e.V2=1]="V2",e[e.V3=2]="V3",e[e.V4=3]="V4"}((Wt=Ht.flatbuf||(Ht.flatbuf={})).MetadataVersion||(Wt.MetadataVersion={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.Sparse=0]="Sparse",e[e.Dense=1]="Dense"}(e.UnionMode||(e.UnionMode={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.HALF=0]="HALF",e[e.SINGLE=1]="SINGLE",e[e.DOUBLE=2]="DOUBLE"}(e.Precision||(e.Precision={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.DAY=0]="DAY",e[e.MILLISECOND=1]="MILLISECOND"}(e.DateUnit||(e.DateUnit={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.SECOND=0]="SECOND",e[e.MILLISECOND=1]="MILLISECOND",e[e.MICROSECOND=2]="MICROSECOND",e[e.NANOSECOND=3]="NANOSECOND"}(e.TimeUnit||(e.TimeUnit={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.YEAR_MONTH=0]="YEAR_MONTH",e[e.DAY_TIME=1]="DAY_TIME"}(e.IntervalUnit||(e.IntervalUnit={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.NONE=0]="NONE",e[e.Null=1]="Null",e[e.Int=2]="Int",e[e.FloatingPoint=3]="FloatingPoint",e[e.Binary=4]="Binary",e[e.Utf8=5]="Utf8",e[e.Bool=6]="Bool",e[e.Decimal=7]="Decimal",e[e.Date=8]="Date",e[e.Time=9]="Time",e[e.Timestamp=10]="Timestamp",e[e.Interval=11]="Interval",e[e.List=12]="List",e[e.Struct_=13]="Struct_",e[e.Union=14]="Union",e[e.FixedSizeBinary=15]="FixedSizeBinary",e[e.FixedSizeList=16]="FixedSizeList",e[e.Map=17]="Map",e[e.Duration=18]="Duration",e[e.LargeBinary=19]="LargeBinary",e[e.LargeUtf8=20]="LargeUtf8",e[e.LargeList=21]="LargeList"}(e.Type||(e.Type={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.Little=0]="Little",e[e.Big=1]="Big"}(e.Endianness||(e.Endianness={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsNull",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startNull",value:function(e){e.startObject(0)}},{key:"endNull",value:function(e){return e.endObject()}},{key:"createNull",value:function(t){return e.startNull(t),e.endNull(t)}}]),e}();e.Null=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsStruct_",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startStruct_",value:function(e){e.startObject(0)}},{key:"endStruct_",value:function(e){return e.endObject()}},{key:"createStruct_",value:function(t){return e.startStruct_(t),e.endStruct_(t)}}]),e}();e.Struct_=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsList",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startList",value:function(e){e.startObject(0)}},{key:"endList",value:function(e){return e.endObject()}},{key:"createList",value:function(t){return e.startList(t),e.endList(t)}}]),e}();e.List=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsLargeList",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startLargeList",value:function(e){e.startObject(0)}},{key:"endLargeList",value:function(e){return e.endObject()}},{key:"createLargeList",value:function(t){return e.startLargeList(t),e.endLargeList(t)}}]),e}();e.LargeList=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"listSize",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}}],[{key:"getRootAsFixedSizeList",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startFixedSizeList",value:function(e){e.startObject(1)}},{key:"addListSize",value:function(e,t){e.addFieldInt32(0,t,0)}},{key:"endFixedSizeList",value:function(e){return e.endObject()}},{key:"createFixedSizeList",value:function(t,n){return e.startFixedSizeList(t),e.addListSize(t,n),e.endFixedSizeList(t)}}]),e}();e.FixedSizeList=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"keysSorted",value:function(){var e=this.bb.__offset(this.bb_pos,4);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}}],[{key:"getRootAsMap",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startMap",value:function(e){e.startObject(1)}},{key:"addKeysSorted",value:function(e,t){e.addFieldInt8(0,+t,0)}},{key:"endMap",value:function(e){return e.endObject()}},{key:"createMap",value:function(t,n){return e.startMap(t),e.addKeysSorted(t,n),e.endMap(t)}}]),e}();e.Map=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"mode",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.UnionMode.Sparse}},{key:"typeIds",value:function(e){var t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb.__vector(this.bb_pos+t)+4*e):0}},{key:"typeIdsLength",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"typeIdsArray",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+e),this.bb.__vector_len(this.bb_pos+e)):null}}],[{key:"getRootAsUnion",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startUnion",value:function(e){e.startObject(2)}},{key:"addMode",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.UnionMode.Sparse)}},{key:"addTypeIds",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"createTypeIdsVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addInt32(t[n]);return e.endVector()}},{key:"startTypeIdsVector",value:function(e,t){e.startVector(4,t,4)}},{key:"endUnion",value:function(e){return e.endObject()}},{key:"createUnion",value:function(e,n,r){return t.startUnion(e),t.addMode(e,n),t.addTypeIds(e,r),t.endUnion(e)}}]),t}();t.Union=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"bitWidth",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}},{key:"isSigned",value:function(){var e=this.bb.__offset(this.bb_pos,6);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}}],[{key:"getRootAsInt",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startInt",value:function(e){e.startObject(2)}},{key:"addBitWidth",value:function(e,t){e.addFieldInt32(0,t,0)}},{key:"addIsSigned",value:function(e,t){e.addFieldInt8(1,+t,0)}},{key:"endInt",value:function(e){return e.endObject()}},{key:"createInt",value:function(t,n,r){return e.startInt(t),e.addBitWidth(t,n),e.addIsSigned(t,r),e.endInt(t)}}]),e}();e.Int=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"precision",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.Precision.HALF}}],[{key:"getRootAsFloatingPoint",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startFloatingPoint",value:function(e){e.startObject(1)}},{key:"addPrecision",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.Precision.HALF)}},{key:"endFloatingPoint",value:function(e){return e.endObject()}},{key:"createFloatingPoint",value:function(e,n){return t.startFloatingPoint(e),t.addPrecision(e,n),t.endFloatingPoint(e)}}]),t}();t.FloatingPoint=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsUtf8",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startUtf8",value:function(e){e.startObject(0)}},{key:"endUtf8",value:function(e){return e.endObject()}},{key:"createUtf8",value:function(t){return e.startUtf8(t),e.endUtf8(t)}}]),e}();e.Utf8=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsBinary",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startBinary",value:function(e){e.startObject(0)}},{key:"endBinary",value:function(e){return e.endObject()}},{key:"createBinary",value:function(t){return e.startBinary(t),e.endBinary(t)}}]),e}();e.Binary=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsLargeUtf8",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startLargeUtf8",value:function(e){e.startObject(0)}},{key:"endLargeUtf8",value:function(e){return e.endObject()}},{key:"createLargeUtf8",value:function(t){return e.startLargeUtf8(t),e.endLargeUtf8(t)}}]),e}();e.LargeUtf8=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsLargeBinary",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startLargeBinary",value:function(e){e.startObject(0)}},{key:"endLargeBinary",value:function(e){return e.endObject()}},{key:"createLargeBinary",value:function(t){return e.startLargeBinary(t),e.endLargeBinary(t)}}]),e}();e.LargeBinary=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"byteWidth",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}}],[{key:"getRootAsFixedSizeBinary",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startFixedSizeBinary",value:function(e){e.startObject(1)}},{key:"addByteWidth",value:function(e,t){e.addFieldInt32(0,t,0)}},{key:"endFixedSizeBinary",value:function(e){return e.endObject()}},{key:"createFixedSizeBinary",value:function(t,n){return e.startFixedSizeBinary(t),e.addByteWidth(t,n),e.endFixedSizeBinary(t)}}]),e}();e.FixedSizeBinary=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsBool",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startBool",value:function(e){e.startObject(0)}},{key:"endBool",value:function(e){return e.endObject()}},{key:"createBool",value:function(t){return e.startBool(t),e.endBool(t)}}]),e}();e.Bool=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"precision",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}},{key:"scale",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb_pos+e):0}}],[{key:"getRootAsDecimal",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startDecimal",value:function(e){e.startObject(2)}},{key:"addPrecision",value:function(e,t){e.addFieldInt32(0,t,0)}},{key:"addScale",value:function(e,t){e.addFieldInt32(1,t,0)}},{key:"endDecimal",value:function(e){return e.endObject()}},{key:"createDecimal",value:function(t,n,r){return e.startDecimal(t),e.addPrecision(t,n),e.addScale(t,r),e.endDecimal(t)}}]),e}();e.Decimal=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.DateUnit.MILLISECOND}}],[{key:"getRootAsDate",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startDate",value:function(e){e.startObject(1)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.DateUnit.MILLISECOND)}},{key:"endDate",value:function(e){return e.endObject()}},{key:"createDate",value:function(e,n){return t.startDate(e),t.addUnit(e,n),t.endDate(e)}}]),t}();t.Date=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.TimeUnit.MILLISECOND}},{key:"bitWidth",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb_pos+e):32}}],[{key:"getRootAsTime",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startTime",value:function(e){e.startObject(2)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.TimeUnit.MILLISECOND)}},{key:"addBitWidth",value:function(e,t){e.addFieldInt32(1,t,32)}},{key:"endTime",value:function(e){return e.endObject()}},{key:"createTime",value:function(e,n,r){return t.startTime(e),t.addUnit(e,n),t.addBitWidth(e,r),t.endTime(e)}}]),t}();t.Time=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.TimeUnit.SECOND}},{key:"timezone",value:function(e){var t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__string(this.bb_pos+t,e):null}}],[{key:"getRootAsTimestamp",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startTimestamp",value:function(e){e.startObject(2)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.TimeUnit.SECOND)}},{key:"addTimezone",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"endTimestamp",value:function(e){return e.endObject()}},{key:"createTimestamp",value:function(e,n,r){return t.startTimestamp(e),t.addUnit(e,n),t.addTimezone(e,r),t.endTimestamp(e)}}]),t}();t.Timestamp=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.IntervalUnit.YEAR_MONTH}}],[{key:"getRootAsInterval",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startInterval",value:function(e){e.startObject(1)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.IntervalUnit.YEAR_MONTH)}},{key:"endInterval",value:function(e){return e.endObject()}},{key:"createInterval",value:function(e,n){return t.startInterval(e),t.addUnit(e,n),t.endInterval(e)}}]),t}();t.Interval=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.TimeUnit.MILLISECOND}}],[{key:"getRootAsDuration",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startDuration",value:function(e){e.startObject(1)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.TimeUnit.MILLISECOND)}},{key:"endDuration",value:function(e){return e.endObject()}},{key:"createDuration",value:function(e,n){return t.startDuration(e),t.addUnit(e,n),t.endDuration(e)}}]),t}();t.Duration=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"key",value:function(e){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__string(this.bb_pos+t,e):null}},{key:"value",value:function(e){var t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__string(this.bb_pos+t,e):null}}],[{key:"getRootAsKeyValue",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startKeyValue",value:function(e){e.startObject(2)}},{key:"addKey",value:function(e,t){e.addFieldOffset(0,t,0)}},{key:"addValue",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"endKeyValue",value:function(e){return e.endObject()}},{key:"createKeyValue",value:function(t,n,r){return e.startKeyValue(t),e.addKey(t,n),e.addValue(t,r),e.endKeyValue(t)}}]),e}();e.KeyValue=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"id",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}},{key:"indexType",value:function(t){var n=this.bb.__offset(this.bb_pos,6);return n?(t||new e.apache.arrow.flatbuf.Int).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}},{key:"isOrdered",value:function(){var e=this.bb.__offset(this.bb_pos,8);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}}],[{key:"getRootAsDictionaryEncoding",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startDictionaryEncoding",value:function(e){e.startObject(3)}},{key:"addId",value:function(e,t){e.addFieldInt64(0,t,e.createLong(0,0))}},{key:"addIndexType",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"addIsOrdered",value:function(e,t){e.addFieldInt8(2,+t,0)}},{key:"endDictionaryEncoding",value:function(e){return e.endObject()}},{key:"createDictionaryEncoding",value:function(e,n,r,i){return t.startDictionaryEncoding(e),t.addId(e,n),t.addIndexType(e,r),t.addIsOrdered(e,i),t.endDictionaryEncoding(e)}}]),t}();t.DictionaryEncoding=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"name",value:function(e){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__string(this.bb_pos+t,e):null}},{key:"nullable",value:function(){var e=this.bb.__offset(this.bb_pos,6);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}},{key:"typeType",value:function(){var t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):e.apache.arrow.flatbuf.Type.NONE}},{key:"type",value:function(e){var t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__union(e,this.bb_pos+t):null}},{key:"dictionary",value:function(t){var n=this.bb.__offset(this.bb_pos,12);return n?(t||new e.apache.arrow.flatbuf.DictionaryEncoding).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}},{key:"children",value:function(t,n){var r=this.bb.__offset(this.bb_pos,14);return r?(n||new e.apache.arrow.flatbuf.Field).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*t),this.bb):null}},{key:"childrenLength",value:function(){var e=this.bb.__offset(this.bb_pos,14);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"customMetadata",value:function(t,n){var r=this.bb.__offset(this.bb_pos,16);return r?(n||new e.apache.arrow.flatbuf.KeyValue).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*t),this.bb):null}},{key:"customMetadataLength",value:function(){var e=this.bb.__offset(this.bb_pos,16);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsField",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startField",value:function(e){e.startObject(7)}},{key:"addName",value:function(e,t){e.addFieldOffset(0,t,0)}},{key:"addNullable",value:function(e,t){e.addFieldInt8(1,+t,0)}},{key:"addTypeType",value:function(t,n){t.addFieldInt8(2,n,e.apache.arrow.flatbuf.Type.NONE)}},{key:"addType",value:function(e,t){e.addFieldOffset(3,t,0)}},{key:"addDictionary",value:function(e,t){e.addFieldOffset(4,t,0)}},{key:"addChildren",value:function(e,t){e.addFieldOffset(5,t,0)}},{key:"createChildrenVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startChildrenVector",value:function(e,t){e.startVector(4,t,4)}},{key:"addCustomMetadata",value:function(e,t){e.addFieldOffset(6,t,0)}},{key:"createCustomMetadataVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startCustomMetadataVector",value:function(e,t){e.startVector(4,t,4)}},{key:"endField",value:function(e){return e.endObject()}},{key:"createField",value:function(e,n,r,i,a,o,u,s){return t.startField(e),t.addName(e,n),t.addNullable(e,r),t.addTypeType(e,i),t.addType(e,a),t.addDictionary(e,o),t.addChildren(e,u),t.addCustomMetadata(e,s),t.endField(e)}}]),t}();t.Field=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"offset",value:function(){return this.bb.readInt64(this.bb_pos)}},{key:"length",value:function(){return this.bb.readInt64(this.bb_pos+8)}}],[{key:"createBuffer",value:function(e,t,n){return e.prep(8,16),e.writeInt64(n),e.writeInt64(t),e.offset()}}]),e}();e.Buffer=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"endianness",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.Endianness.Little}},{key:"fields",value:function(t,n){var r=this.bb.__offset(this.bb_pos,6);return r?(n||new e.apache.arrow.flatbuf.Field).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*t),this.bb):null}},{key:"fieldsLength",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"customMetadata",value:function(t,n){var r=this.bb.__offset(this.bb_pos,8);return r?(n||new e.apache.arrow.flatbuf.KeyValue).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*t),this.bb):null}},{key:"customMetadataLength",value:function(){var e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsSchema",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startSchema",value:function(e){e.startObject(3)}},{key:"addEndianness",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.Endianness.Little)}},{key:"addFields",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"createFieldsVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startFieldsVector",value:function(e,t){e.startVector(4,t,4)}},{key:"addCustomMetadata",value:function(e,t){e.addFieldOffset(2,t,0)}},{key:"createCustomMetadataVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startCustomMetadataVector",value:function(e,t){e.startVector(4,t,4)}},{key:"endSchema",value:function(e){return e.endObject()}},{key:"finishSchemaBuffer",value:function(e,t){e.finish(t)}},{key:"createSchema",value:function(e,n,r,i){return t.startSchema(e),t.addEndianness(e,n),t.addFields(e,r),t.addCustomMetadata(e,i),t.endSchema(e)}}]),t}();t.Schema=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Vt||(Vt={})),function(e){!function(e){!function(e){!function(e){e.Schema=Vt.apache.arrow.flatbuf.Schema}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Kt||(Kt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.NONE=0]="NONE",e[e.Schema=1]="Schema",e[e.DictionaryBatch=2]="DictionaryBatch",e[e.RecordBatch=3]="RecordBatch",e[e.Tensor=4]="Tensor",e[e.SparseTensor=5]="SparseTensor"}(e.MessageHeader||(e.MessageHeader={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Kt||(Kt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"length",value:function(){return this.bb.readInt64(this.bb_pos)}},{key:"nullCount",value:function(){return this.bb.readInt64(this.bb_pos+8)}}],[{key:"createFieldNode",value:function(e,t,n){return e.prep(8,16),e.writeInt64(n),e.writeInt64(t),e.offset()}}]),e}();e.FieldNode=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Kt||(Kt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"length",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}},{key:"nodes",value:function(t,n){var r=this.bb.__offset(this.bb_pos,6);return r?(n||new e.apache.arrow.flatbuf.FieldNode).__init(this.bb.__vector(this.bb_pos+r)+16*t,this.bb):null}},{key:"nodesLength",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"buffers",value:function(e,t){var n=this.bb.__offset(this.bb_pos,8);return n?(t||new Vt.apache.arrow.flatbuf.Buffer).__init(this.bb.__vector(this.bb_pos+n)+16*e,this.bb):null}},{key:"buffersLength",value:function(){var e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsRecordBatch",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startRecordBatch",value:function(e){e.startObject(3)}},{key:"addLength",value:function(e,t){e.addFieldInt64(0,t,e.createLong(0,0))}},{key:"addNodes",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"startNodesVector",value:function(e,t){e.startVector(16,t,8)}},{key:"addBuffers",value:function(e,t){e.addFieldOffset(2,t,0)}},{key:"startBuffersVector",value:function(e,t){e.startVector(16,t,8)}},{key:"endRecordBatch",value:function(e){return e.endObject()}},{key:"createRecordBatch",value:function(e,n,r,i){return t.startRecordBatch(e),t.addLength(e,n),t.addNodes(e,r),t.addBuffers(e,i),t.endRecordBatch(e)}}]),t}();t.RecordBatch=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Kt||(Kt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"id",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}},{key:"data",value:function(t){var n=this.bb.__offset(this.bb_pos,6);return n?(t||new e.apache.arrow.flatbuf.RecordBatch).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}},{key:"isDelta",value:function(){var e=this.bb.__offset(this.bb_pos,8);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}}],[{key:"getRootAsDictionaryBatch",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startDictionaryBatch",value:function(e){e.startObject(3)}},{key:"addId",value:function(e,t){e.addFieldInt64(0,t,e.createLong(0,0))}},{key:"addData",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"addIsDelta",value:function(e,t){e.addFieldInt8(2,+t,0)}},{key:"endDictionaryBatch",value:function(e){return e.endObject()}},{key:"createDictionaryBatch",value:function(e,n,r,i){return t.startDictionaryBatch(e),t.addId(e,n),t.addData(e,r),t.addIsDelta(e,i),t.endDictionaryBatch(e)}}]),t}();t.DictionaryBatch=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Kt||(Kt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"version",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):Vt.apache.arrow.flatbuf.MetadataVersion.V1}},{key:"headerType",value:function(){var t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):e.apache.arrow.flatbuf.MessageHeader.NONE}},{key:"header",value:function(e){var t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__union(e,this.bb_pos+t):null}},{key:"bodyLength",value:function(){var e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}},{key:"customMetadata",value:function(e,t){var n=this.bb.__offset(this.bb_pos,12);return n?(t||new Vt.apache.arrow.flatbuf.KeyValue).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*e),this.bb):null}},{key:"customMetadataLength",value:function(){var e=this.bb.__offset(this.bb_pos,12);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsMessage",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startMessage",value:function(e){e.startObject(5)}},{key:"addVersion",value:function(e,t){e.addFieldInt16(0,t,Vt.apache.arrow.flatbuf.MetadataVersion.V1)}},{key:"addHeaderType",value:function(t,n){t.addFieldInt8(1,n,e.apache.arrow.flatbuf.MessageHeader.NONE)}},{key:"addHeader",value:function(e,t){e.addFieldOffset(2,t,0)}},{key:"addBodyLength",value:function(e,t){e.addFieldInt64(3,t,e.createLong(0,0))}},{key:"addCustomMetadata",value:function(e,t){e.addFieldOffset(4,t,0)}},{key:"createCustomMetadataVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startCustomMetadataVector",value:function(e,t){e.startVector(4,t,4)}},{key:"endMessage",value:function(e){return e.endObject()}},{key:"finishMessageBuffer",value:function(e,t){e.finish(t)}},{key:"createMessage",value:function(e,n,r,i,a,o){return t.startMessage(e),t.addVersion(e,n),t.addHeaderType(e,r),t.addHeader(e,i),t.addBodyLength(e,a),t.addCustomMetadata(e,o),t.endMessage(e)}}]),t}();t.Message=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Kt||(Kt={}));Vt.apache.arrow.flatbuf.Type;var Gt,qt,Xt=Vt.apache.arrow.flatbuf.DateUnit,Zt=Vt.apache.arrow.flatbuf.TimeUnit,Jt=Vt.apache.arrow.flatbuf.Precision,en=Vt.apache.arrow.flatbuf.UnionMode,tn=Vt.apache.arrow.flatbuf.IntervalUnit,nn=Kt.apache.arrow.flatbuf.MessageHeader,rn=Vt.apache.arrow.flatbuf.MetadataVersion;!function(e){e[e.NONE=0]="NONE",e[e.Null=1]="Null",e[e.Int=2]="Int",e[e.Float=3]="Float",e[e.Binary=4]="Binary",e[e.Utf8=5]="Utf8",e[e.Bool=6]="Bool",e[e.Decimal=7]="Decimal",e[e.Date=8]="Date",e[e.Time=9]="Time",e[e.Timestamp=10]="Timestamp",e[e.Interval=11]="Interval",e[e.List=12]="List",e[e.Struct=13]="Struct",e[e.Union=14]="Union",e[e.FixedSizeBinary=15]="FixedSizeBinary",e[e.FixedSizeList=16]="FixedSizeList",e[e.Map=17]="Map",e[e.Dictionary=-1]="Dictionary",e[e.Int8=-2]="Int8",e[e.Int16=-3]="Int16",e[e.Int32=-4]="Int32",e[e.Int64=-5]="Int64",e[e.Uint8=-6]="Uint8",e[e.Uint16=-7]="Uint16",e[e.Uint32=-8]="Uint32",e[e.Uint64=-9]="Uint64",e[e.Float16=-10]="Float16",e[e.Float32=-11]="Float32",e[e.Float64=-12]="Float64",e[e.DateDay=-13]="DateDay",e[e.DateMillisecond=-14]="DateMillisecond",e[e.TimestampSecond=-15]="TimestampSecond",e[e.TimestampMillisecond=-16]="TimestampMillisecond",e[e.TimestampMicrosecond=-17]="TimestampMicrosecond",e[e.TimestampNanosecond=-18]="TimestampNanosecond",e[e.TimeSecond=-19]="TimeSecond",e[e.TimeMillisecond=-20]="TimeMillisecond",e[e.TimeMicrosecond=-21]="TimeMicrosecond",e[e.TimeNanosecond=-22]="TimeNanosecond",e[e.DenseUnion=-23]="DenseUnion",e[e.SparseUnion=-24]="SparseUnion",e[e.IntervalDayTime=-25]="IntervalDayTime",e[e.IntervalYearMonth=-26]="IntervalYearMonth"}(Gt||(Gt={})),function(e){e[e.OFFSET=0]="OFFSET",e[e.DATA=1]="DATA",e[e.VALIDITY=2]="VALIDITY",e[e.TYPE=3]="TYPE"}(qt||(qt={}));var an=N.mark(fn);function on(e,t,n,r){return 0!==(n&1<<r)}function un(e,t,n,r){return(n&1<<r)>>r}function sn(e,t,n){return n?!!(e[t>>3]|=1<<t%8)||!0:!(e[t>>3]&=~(1<<t%8))&&!1}function ln(e,t,n){var r=n.byteLength+7&-8;if(e>0||n.byteLength<r){var i=new Uint8Array(r);return i.set(e%8===0?n.subarray(e>>3):cn(fn(n,e,t,null,on)).subarray(0,r)),i}return n}function cn(e){var t,n=[],r=0,i=0,a=0,o=O(e);try{for(o.s();!(t=o.n()).done;){t.value&&(a|=1<<i),8===++i&&(n[r++]=a,a=i=0)}}catch(s){o.e(s)}finally{o.f()}(0===r||i>0)&&(n[r++]=a);var u=new Uint8Array(n.length+7&-8);return u.set(n),u}function fn(e,t,n,r,i){var a,o,u,s,l;return N.wrap((function(c){for(;;)switch(c.prev=c.next){case 0:a=t%8,o=t>>3,u=0,s=n;case 3:if(!(s>0)){c.next=11;break}l=e[o++];case 5:return c.next=7,i(r,u++,l,a);case 7:if(--s>0&&++a<8){c.next=5;break}case 8:a=0,c.next=3;break;case 11:case"end":return c.stop()}}),an)}function dn(e,t,n){if(n-t<=0)return 0;if(n-t<8){var r,i=0,a=O(fn(e,t,n-t,e,un));try{for(a.s();!(r=a.n()).done;){i+=r.value}}catch(s){a.e(s)}finally{a.f()}return i}var o=n>>3<<3,u=t+(t%8===0?0:8-t%8);return dn(e,t,u)+dn(e,o,n)+hn(e,u>>3,o-u>>3)}function hn(e,t,n){for(var r=0,i=0|t,a=new DataView(e.buffer,e.byteOffset,e.byteLength),o=void 0===n?e.byteLength:i+n;o-i>=4;)r+=pn(a.getUint32(i)),i+=4;for(;o-i>=2;)r+=pn(a.getUint16(i)),i+=2;for(;o-i>=1;)r+=pn(a.getUint8(i)),i+=1;return r}function pn(e){var t=0|e;return 16843009*((t=(858993459&(t-=t>>>1&1431655765))+(t>>>2&858993459))+(t>>>4)&252645135)>>>24}var yn=n(6);function vn(e){return function(e){if(Array.isArray(e))return Object(yn.a)(e)}(e)||function(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||Object(F.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var bn=function(){function e(){C(this,e)}return M(e,[{key:"visitMany",value:function(e){for(var t=this,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return e.map((function(e,n){return t.visit.apply(t,[e].concat(vn(r.map((function(e){return e[n]})))))}))}},{key:"visit",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.getVisitFn(t[0],!1).apply(this,t)}},{key:"getVisitFn",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return mn(this,e,t)}},{key:"visitNull",value:function(e){return null}},{key:"visitBool",value:function(e){return null}},{key:"visitInt",value:function(e){return null}},{key:"visitFloat",value:function(e){return null}},{key:"visitUtf8",value:function(e){return null}},{key:"visitBinary",value:function(e){return null}},{key:"visitFixedSizeBinary",value:function(e){return null}},{key:"visitDate",value:function(e){return null}},{key:"visitTimestamp",value:function(e){return null}},{key:"visitTime",value:function(e){return null}},{key:"visitDecimal",value:function(e){return null}},{key:"visitList",value:function(e){return null}},{key:"visitStruct",value:function(e){return null}},{key:"visitUnion",value:function(e){return null}},{key:"visitDictionary",value:function(e){return null}},{key:"visitInterval",value:function(e){return null}},{key:"visitFixedSizeList",value:function(e){return null}},{key:"visitMap",value:function(e){return null}}]),e}();function mn(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=null,i=Gt.NONE;switch(t instanceof hr||t instanceof Qt?i=gn(t.type):t instanceof Dn?i=gn(t):"number"!==typeof(i=t)&&(i=Gt[t]),i){case Gt.Null:r=e.visitNull;break;case Gt.Bool:r=e.visitBool;break;case Gt.Int:r=e.visitInt;break;case Gt.Int8:r=e.visitInt8||e.visitInt;break;case Gt.Int16:r=e.visitInt16||e.visitInt;break;case Gt.Int32:r=e.visitInt32||e.visitInt;break;case Gt.Int64:r=e.visitInt64||e.visitInt;break;case Gt.Uint8:r=e.visitUint8||e.visitInt;break;case Gt.Uint16:r=e.visitUint16||e.visitInt;break;case Gt.Uint32:r=e.visitUint32||e.visitInt;break;case Gt.Uint64:r=e.visitUint64||e.visitInt;break;case Gt.Float:r=e.visitFloat;break;case Gt.Float16:r=e.visitFloat16||e.visitFloat;break;case Gt.Float32:r=e.visitFloat32||e.visitFloat;break;case Gt.Float64:r=e.visitFloat64||e.visitFloat;break;case Gt.Utf8:r=e.visitUtf8;break;case Gt.Binary:r=e.visitBinary;break;case Gt.FixedSizeBinary:r=e.visitFixedSizeBinary;break;case Gt.Date:r=e.visitDate;break;case Gt.DateDay:r=e.visitDateDay||e.visitDate;break;case Gt.DateMillisecond:r=e.visitDateMillisecond||e.visitDate;break;case Gt.Timestamp:r=e.visitTimestamp;break;case Gt.TimestampSecond:r=e.visitTimestampSecond||e.visitTimestamp;break;case Gt.TimestampMillisecond:r=e.visitTimestampMillisecond||e.visitTimestamp;break;case Gt.TimestampMicrosecond:r=e.visitTimestampMicrosecond||e.visitTimestamp;break;case Gt.TimestampNanosecond:r=e.visitTimestampNanosecond||e.visitTimestamp;break;case Gt.Time:r=e.visitTime;break;case Gt.TimeSecond:r=e.visitTimeSecond||e.visitTime;break;case Gt.TimeMillisecond:r=e.visitTimeMillisecond||e.visitTime;break;case Gt.TimeMicrosecond:r=e.visitTimeMicrosecond||e.visitTime;break;case Gt.TimeNanosecond:r=e.visitTimeNanosecond||e.visitTime;break;case Gt.Decimal:r=e.visitDecimal;break;case Gt.List:r=e.visitList;break;case Gt.Struct:r=e.visitStruct;break;case Gt.Union:r=e.visitUnion;break;case Gt.DenseUnion:r=e.visitDenseUnion||e.visitUnion;break;case Gt.SparseUnion:r=e.visitSparseUnion||e.visitUnion;break;case Gt.Dictionary:r=e.visitDictionary;break;case Gt.Interval:r=e.visitInterval;break;case Gt.IntervalDayTime:r=e.visitIntervalDayTime||e.visitInterval;break;case Gt.IntervalYearMonth:r=e.visitIntervalYearMonth||e.visitInterval;break;case Gt.FixedSizeList:r=e.visitFixedSizeList;break;case Gt.Map:r=e.visitMap}if("function"===typeof r)return r;if(!n)return function(){return null};throw new Error("Unrecognized type '".concat(Gt[i],"'"))}function gn(e){switch(e.typeId){case Gt.Null:return Gt.Null;case Gt.Int:var t=e.bitWidth,n=e.isSigned;switch(t){case 8:return n?Gt.Int8:Gt.Uint8;case 16:return n?Gt.Int16:Gt.Uint16;case 32:return n?Gt.Int32:Gt.Uint32;case 64:return n?Gt.Int64:Gt.Uint64}return Gt.Int;case Gt.Float:switch(e.precision){case Jt.HALF:return Gt.Float16;case Jt.SINGLE:return Gt.Float32;case Jt.DOUBLE:return Gt.Float64}return Gt.Float;case Gt.Binary:return Gt.Binary;case Gt.Utf8:return Gt.Utf8;case Gt.Bool:return Gt.Bool;case Gt.Decimal:return Gt.Decimal;case Gt.Time:switch(e.unit){case Zt.SECOND:return Gt.TimeSecond;case Zt.MILLISECOND:return Gt.TimeMillisecond;case Zt.MICROSECOND:return Gt.TimeMicrosecond;case Zt.NANOSECOND:return Gt.TimeNanosecond}return Gt.Time;case Gt.Timestamp:switch(e.unit){case Zt.SECOND:return Gt.TimestampSecond;case Zt.MILLISECOND:return Gt.TimestampMillisecond;case Zt.MICROSECOND:return Gt.TimestampMicrosecond;case Zt.NANOSECOND:return Gt.TimestampNanosecond}return Gt.Timestamp;case Gt.Date:switch(e.unit){case Xt.DAY:return Gt.DateDay;case Xt.MILLISECOND:return Gt.DateMillisecond}return Gt.Date;case Gt.Interval:switch(e.unit){case tn.DAY_TIME:return Gt.IntervalDayTime;case tn.YEAR_MONTH:return Gt.IntervalYearMonth}return Gt.Interval;case Gt.Map:return Gt.Map;case Gt.List:return Gt.List;case Gt.Struct:return Gt.Struct;case Gt.Union:switch(e.mode){case en.Dense:return Gt.DenseUnion;case en.Sparse:return Gt.SparseUnion}return Gt.Union;case Gt.FixedSizeBinary:return Gt.FixedSizeBinary;case Gt.FixedSizeList:return Gt.FixedSizeList;case Gt.Dictionary:return Gt.Dictionary}throw new Error("Unrecognized type '".concat(Gt[e.typeId],"'"))}bn.prototype.visitInt8=null,bn.prototype.visitInt16=null,bn.prototype.visitInt32=null,bn.prototype.visitInt64=null,bn.prototype.visitUint8=null,bn.prototype.visitUint16=null,bn.prototype.visitUint32=null,bn.prototype.visitUint64=null,bn.prototype.visitFloat16=null,bn.prototype.visitFloat32=null,bn.prototype.visitFloat64=null,bn.prototype.visitDateDay=null,bn.prototype.visitDateMillisecond=null,bn.prototype.visitTimestampSecond=null,bn.prototype.visitTimestampMillisecond=null,bn.prototype.visitTimestampMicrosecond=null,bn.prototype.visitTimestampNanosecond=null,bn.prototype.visitTimeSecond=null,bn.prototype.visitTimeMillisecond=null,bn.prototype.visitTimeMicrosecond=null,bn.prototype.visitTimeNanosecond=null,bn.prototype.visitDenseUnion=null,bn.prototype.visitSparseUnion=null,bn.prototype.visitIntervalDayTime=null,bn.prototype.visitIntervalYearMonth=null;var kn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"compareSchemas",value:function(e,t){return e===t||t instanceof e.constructor&&Bn.compareFields(e.fields,t.fields)}},{key:"compareFields",value:function(e,t){return e===t||Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every((function(e,n){return Bn.compareField(e,t[n])}))}},{key:"compareField",value:function(e,t){return e===t||t instanceof e.constructor&&e.name===t.name&&e.nullable===t.nullable&&Bn.visit(e.type,t.type)}}]),n}(bn);function wn(e,t){return t instanceof e.constructor}function _n(e,t){return e===t||wn(e,t)}function xn(e,t){return e===t||wn(e,t)&&e.bitWidth===t.bitWidth&&e.isSigned===t.isSigned}function Sn(e,t){return e===t||wn(e,t)&&e.precision===t.precision}function Tn(e,t){return e===t||wn(e,t)&&e.unit===t.unit}function In(e,t){return e===t||wn(e,t)&&e.unit===t.unit&&e.timezone===t.timezone}function En(e,t){return e===t||wn(e,t)&&e.unit===t.unit&&e.bitWidth===t.bitWidth}function An(e,t){return e===t||wn(e,t)&&e.mode===t.mode&&e.typeIds.every((function(e,n){return e===t.typeIds[n]}))&&Bn.compareFields(e.children,t.children)}function Fn(e,t){return e===t||wn(e,t)&&e.unit===t.unit}kn.prototype.visitNull=_n,kn.prototype.visitBool=_n,kn.prototype.visitInt=xn,kn.prototype.visitInt8=xn,kn.prototype.visitInt16=xn,kn.prototype.visitInt32=xn,kn.prototype.visitInt64=xn,kn.prototype.visitUint8=xn,kn.prototype.visitUint16=xn,kn.prototype.visitUint32=xn,kn.prototype.visitUint64=xn,kn.prototype.visitFloat=Sn,kn.prototype.visitFloat16=Sn,kn.prototype.visitFloat32=Sn,kn.prototype.visitFloat64=Sn,kn.prototype.visitUtf8=_n,kn.prototype.visitBinary=_n,kn.prototype.visitFixedSizeBinary=function(e,t){return e===t||wn(e,t)&&e.byteWidth===t.byteWidth},kn.prototype.visitDate=Tn,kn.prototype.visitDateDay=Tn,kn.prototype.visitDateMillisecond=Tn,kn.prototype.visitTimestamp=In,kn.prototype.visitTimestampSecond=In,kn.prototype.visitTimestampMillisecond=In,kn.prototype.visitTimestampMicrosecond=In,kn.prototype.visitTimestampNanosecond=In,kn.prototype.visitTime=En,kn.prototype.visitTimeSecond=En,kn.prototype.visitTimeMillisecond=En,kn.prototype.visitTimeMicrosecond=En,kn.prototype.visitTimeNanosecond=En,kn.prototype.visitDecimal=_n,kn.prototype.visitList=function(e,t){return e===t||wn(e,t)&&e.children.length===t.children.length&&Bn.compareFields(e.children,t.children)},kn.prototype.visitStruct=function(e,t){return e===t||wn(e,t)&&e.children.length===t.children.length&&Bn.compareFields(e.children,t.children)},kn.prototype.visitUnion=An,kn.prototype.visitDenseUnion=An,kn.prototype.visitSparseUnion=An,kn.prototype.visitDictionary=function(e,t){return e===t||wn(e,t)&&e.id===t.id&&e.isOrdered===t.isOrdered&&Bn.visit(e.indices,t.indices)&&Bn.visit(e.dictionary,t.dictionary)},kn.prototype.visitInterval=Fn,kn.prototype.visitIntervalDayTime=Fn,kn.prototype.visitIntervalYearMonth=Fn,kn.prototype.visitFixedSizeList=function(e,t){return e===t||wn(e,t)&&e.listSize===t.listSize&&e.children.length===t.children.length&&Bn.compareFields(e.children,t.children)},kn.prototype.visitMap=function(e,t){return e===t||wn(e,t)&&e.keysSorted===t.keysSorted&&e.children.length===t.children.length&&Bn.compareFields(e.children,t.children)};var On,Bn=new kn,Dn=function(){function e(){C(this,e)}return M(e,[{key:"typeId",get:function(){return Gt.NONE}},{key:"compareTo",value:function(e){return Bn.visit(this,e)}}],[{key:"isNull",value:function(e){return e&&e.typeId===Gt.Null}},{key:"isInt",value:function(e){return e&&e.typeId===Gt.Int}},{key:"isFloat",value:function(e){return e&&e.typeId===Gt.Float}},{key:"isBinary",value:function(e){return e&&e.typeId===Gt.Binary}},{key:"isUtf8",value:function(e){return e&&e.typeId===Gt.Utf8}},{key:"isBool",value:function(e){return e&&e.typeId===Gt.Bool}},{key:"isDecimal",value:function(e){return e&&e.typeId===Gt.Decimal}},{key:"isDate",value:function(e){return e&&e.typeId===Gt.Date}},{key:"isTime",value:function(e){return e&&e.typeId===Gt.Time}},{key:"isTimestamp",value:function(e){return e&&e.typeId===Gt.Timestamp}},{key:"isInterval",value:function(e){return e&&e.typeId===Gt.Interval}},{key:"isList",value:function(e){return e&&e.typeId===Gt.List}},{key:"isStruct",value:function(e){return e&&e.typeId===Gt.Struct}},{key:"isUnion",value:function(e){return e&&e.typeId===Gt.Union}},{key:"isFixedSizeBinary",value:function(e){return e&&e.typeId===Gt.FixedSizeBinary}},{key:"isFixedSizeList",value:function(e){return e&&e.typeId===Gt.FixedSizeList}},{key:"isMap",value:function(e){return e&&e.typeId===Gt.Map}},{key:"isDictionary",value:function(e){return e&&e.typeId===Gt.Dictionary}}]),e}();Dn[Symbol.toStringTag]=((On=Dn.prototype).children=null,On.ArrayType=Array,On[Symbol.toStringTag]="DataType");var Cn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"toString",value:function(){return"Null"}},{key:"typeId",get:function(){return Gt.Null}}]),n}(Dn);Cn[Symbol.toStringTag]=function(e){return e[Symbol.toStringTag]="Null"}(Cn.prototype);var Ln=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this)).isSigned=e,i.bitWidth=r,i}return M(n,[{key:"typeId",get:function(){return Gt.Int}},{key:"ArrayType",get:function(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:case 64:return this.isSigned?Int32Array:Uint32Array}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}},{key:"toString",value:function(){return"".concat(this.isSigned?"I":"Ui","nt").concat(this.bitWidth)}}]),n}(Dn);Ln[Symbol.toStringTag]=function(e){return e.isSigned=null,e.bitWidth=null,e[Symbol.toStringTag]="Int"}(Ln.prototype);var Mn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,!0,8)}return n}(Ln),Nn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,!0,16)}return n}(Ln),Pn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,!0,32)}return n}(Ln),Un=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,!0,64)}return n}(Ln),Rn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,!1,8)}return n}(Ln),zn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,!1,16)}return n}(Ln),jn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,!1,32)}return n}(Ln),Vn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,!1,64)}return n}(Ln);Object.defineProperty(Mn.prototype,"ArrayType",{value:Int8Array}),Object.defineProperty(Nn.prototype,"ArrayType",{value:Int16Array}),Object.defineProperty(Pn.prototype,"ArrayType",{value:Int32Array}),Object.defineProperty(Un.prototype,"ArrayType",{value:Int32Array}),Object.defineProperty(Rn.prototype,"ArrayType",{value:Uint8Array}),Object.defineProperty(zn.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(jn.prototype,"ArrayType",{value:Uint32Array}),Object.defineProperty(Vn.prototype,"ArrayType",{value:Uint32Array});var Wn=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this)).precision=e,r}return M(n,[{key:"typeId",get:function(){return Gt.Float}},{key:"ArrayType",get:function(){switch(this.precision){case Jt.HALF:return Uint16Array;case Jt.SINGLE:return Float32Array;case Jt.DOUBLE:return Float64Array}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}},{key:"toString",value:function(){return"Float".concat(this.precision<<5||16)}}]),n}(Dn);Wn[Symbol.toStringTag]=function(e){return e.precision=null,e[Symbol.toStringTag]="Float"}(Wn.prototype);var Hn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,Jt.HALF)}return n}(Wn),Yn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,Jt.SINGLE)}return n}(Wn),$n=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,Jt.DOUBLE)}return n}(Wn);Object.defineProperty(Hn.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(Yn.prototype,"ArrayType",{value:Float32Array}),Object.defineProperty($n.prototype,"ArrayType",{value:Float64Array});var Kn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this)}return M(n,[{key:"typeId",get:function(){return Gt.Binary}},{key:"toString",value:function(){return"Binary"}}]),n}(Dn);Kn[Symbol.toStringTag]=function(e){return e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Binary"}(Kn.prototype);var Qn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this)}return M(n,[{key:"typeId",get:function(){return Gt.Utf8}},{key:"toString",value:function(){return"Utf8"}}]),n}(Dn);Qn[Symbol.toStringTag]=function(e){return e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Utf8"}(Qn.prototype);var Gn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this)}return M(n,[{key:"typeId",get:function(){return Gt.Bool}},{key:"toString",value:function(){return"Bool"}}]),n}(Dn);Gn[Symbol.toStringTag]=function(e){return e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Bool"}(Gn.prototype);var qn=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this)).scale=e,i.precision=r,i}return M(n,[{key:"typeId",get:function(){return Gt.Decimal}},{key:"toString",value:function(){return"Decimal[".concat(this.precision,"e").concat(this.scale>0?"+":"").concat(this.scale,"]")}}]),n}(Dn);qn[Symbol.toStringTag]=function(e){return e.scale=null,e.precision=null,e.ArrayType=Uint32Array,e[Symbol.toStringTag]="Decimal"}(qn.prototype);var Xn=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this)).unit=e,r}return M(n,[{key:"typeId",get:function(){return Gt.Date}},{key:"toString",value:function(){return"Date".concat(32*(this.unit+1),"<").concat(Xt[this.unit],">")}}]),n}(Dn);Xn[Symbol.toStringTag]=function(e){return e.unit=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Date"}(Xn.prototype);var Zn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,Xt.DAY)}return n}(Xn),Jn=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.call(this,Xt.MILLISECOND)}return n}(Xn),er=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this)).unit=e,i.bitWidth=r,i}return M(n,[{key:"typeId",get:function(){return Gt.Time}},{key:"toString",value:function(){return"Time".concat(this.bitWidth,"<").concat(Zt[this.unit],">")}}]),n}(Dn);er[Symbol.toStringTag]=function(e){return e.unit=null,e.bitWidth=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Time"}(er.prototype);var tr=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this)).unit=e,i.timezone=r,i}return M(n,[{key:"typeId",get:function(){return Gt.Timestamp}},{key:"toString",value:function(){return"Timestamp<".concat(Zt[this.unit]).concat(this.timezone?", ".concat(this.timezone):"",">")}}]),n}(Dn);tr[Symbol.toStringTag]=function(e){return e.unit=null,e.timezone=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Timestamp"}(tr.prototype);var nr=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this)).unit=e,r}return M(n,[{key:"typeId",get:function(){return Gt.Interval}},{key:"toString",value:function(){return"Interval<".concat(tn[this.unit],">")}}]),n}(Dn);nr[Symbol.toStringTag]=function(e){return e.unit=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Interval"}(nr.prototype);var rr=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this)).children=[e],r}return M(n,[{key:"typeId",get:function(){return Gt.List}},{key:"toString",value:function(){return"List<".concat(this.valueType,">")}},{key:"valueType",get:function(){return this.children[0].type}},{key:"valueField",get:function(){return this.children[0]}},{key:"ArrayType",get:function(){return this.valueType.ArrayType}}]),n}(Dn);rr[Symbol.toStringTag]=function(e){return e.children=null,e[Symbol.toStringTag]="List"}(rr.prototype);var ir=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this)).children=e,r}return M(n,[{key:"typeId",get:function(){return Gt.Struct}},{key:"toString",value:function(){return"Struct<{".concat(this.children.map((function(e){return"".concat(e.name,":").concat(e.type)})).join(", "),"}>")}}]),n}(Dn);ir[Symbol.toStringTag]=function(e){return e.children=null,e[Symbol.toStringTag]="Struct"}(ir.prototype);var ar=function(e){ie(n,e);var t=ce(n);function n(e,r,i){var a;return C(this,n),(a=t.call(this)).mode=e,a.children=i,a.typeIds=r=Int32Array.from(r),a.typeIdToChildIndex=r.reduce((function(e,t,n){return(e[t]=n)&&e||e}),Object.create(null)),a}return M(n,[{key:"typeId",get:function(){return Gt.Union}},{key:"toString",value:function(){return"".concat(this[Symbol.toStringTag],"<").concat(this.children.map((function(e){return"".concat(e.type)})).join(" | "),">")}}]),n}(Dn);ar[Symbol.toStringTag]=function(e){return e.mode=null,e.typeIds=null,e.children=null,e.typeIdToChildIndex=null,e.ArrayType=Int8Array,e[Symbol.toStringTag]="Union"}(ar.prototype);var or=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this)).byteWidth=e,r}return M(n,[{key:"typeId",get:function(){return Gt.FixedSizeBinary}},{key:"toString",value:function(){return"FixedSizeBinary[".concat(this.byteWidth,"]")}}]),n}(Dn);or[Symbol.toStringTag]=function(e){return e.byteWidth=null,e.ArrayType=Uint8Array,e[Symbol.toStringTag]="FixedSizeBinary"}(or.prototype);var ur=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this)).listSize=e,i.children=[r],i}return M(n,[{key:"typeId",get:function(){return Gt.FixedSizeList}},{key:"valueType",get:function(){return this.children[0].type}},{key:"valueField",get:function(){return this.children[0]}},{key:"ArrayType",get:function(){return this.valueType.ArrayType}},{key:"toString",value:function(){return"FixedSizeList[".concat(this.listSize,"]<").concat(this.valueType,">")}}]),n}(Dn);ur[Symbol.toStringTag]=function(e){return e.children=null,e.listSize=null,e[Symbol.toStringTag]="FixedSizeList"}(ur.prototype);var sr=function(e){ie(n,e);var t=ce(n);function n(e){var r,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return C(this,n),(r=t.call(this)).children=[e],r.keysSorted=i,r}return M(n,[{key:"typeId",get:function(){return Gt.Map}},{key:"keyType",get:function(){return this.children[0].type.children[0].type}},{key:"valueType",get:function(){return this.children[0].type.children[1].type}},{key:"toString",value:function(){return"Map<{".concat(this.children[0].type.children.map((function(e){return"".concat(e.name,":").concat(e.type)})).join(", "),"}>")}}]),n}(Dn);sr[Symbol.toStringTag]=function(e){return e.children=null,e.keysSorted=null,e[Symbol.toStringTag]="Map_"}(sr.prototype);var lr,cr=(lr=-1,function(){return++lr}),fr=function(e){ie(n,e);var t=ce(n);function n(e,r,i,a){var o;return C(this,n),(o=t.call(this)).indices=r,o.dictionary=e,o.isOrdered=a||!1,o.id=null==i?cr():"number"===typeof i?i:i.low,o}return M(n,[{key:"typeId",get:function(){return Gt.Dictionary}},{key:"children",get:function(){return this.dictionary.children}},{key:"valueType",get:function(){return this.dictionary}},{key:"ArrayType",get:function(){return this.dictionary.ArrayType}},{key:"toString",value:function(){return"Dictionary<".concat(this.indices,", ").concat(this.dictionary,">")}}]),n}(Dn);function dr(e){var t=e;switch(e.typeId){case Gt.Decimal:return 4;case Gt.Timestamp:return 2;case Gt.Date:case Gt.Interval:return 1+t.unit;case Gt.Int:case Gt.Time:return+(t.bitWidth>32)+1;case Gt.FixedSizeList:return t.listSize;case Gt.FixedSizeBinary:return t.byteWidth;default:return 1}}fr[Symbol.toStringTag]=function(e){return e.id=null,e.indices=null,e.isOrdered=null,e.dictionary=null,e[Symbol.toStringTag]="Dictionary"}(fr.prototype);var hr=function(){function e(t,n,r,i,a,o,u){var s;C(this,e),this.type=t,this.dictionary=u,this.offset=Math.floor(Math.max(n||0,0)),this.length=Math.floor(Math.max(r||0,0)),this._nullCount=Math.floor(Math.max(i||0,-1)),this.childData=(o||[]).map((function(t){return t instanceof e?t:t.data})),a instanceof e?(this.stride=a.stride,this.values=a.values,this.typeIds=a.typeIds,this.nullBitmap=a.nullBitmap,this.valueOffsets=a.valueOffsets):(this.stride=dr(t),a&&((s=a[0])&&(this.valueOffsets=s),(s=a[1])&&(this.values=s),(s=a[2])&&(this.nullBitmap=s),(s=a[3])&&(this.typeIds=s)))}return M(e,[{key:"typeId",get:function(){return this.type.typeId}},{key:"ArrayType",get:function(){return this.type.ArrayType}},{key:"buffers",get:function(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}},{key:"byteLength",get:function(){var e=0,t=this.valueOffsets,n=this.values,r=this.nullBitmap,i=this.typeIds;return t&&(e+=t.byteLength),n&&(e+=n.byteLength),r&&(e+=r.byteLength),i&&(e+=i.byteLength),this.childData.reduce((function(e,t){return e+t.byteLength}),e)}},{key:"nullCount",get:function(){var e,t=this._nullCount;return t<=-1&&(e=this.nullBitmap)&&(this._nullCount=t=this.length-dn(e,this.offset,this.offset+this.length)),t}},{key:"clone",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.offset,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.length,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this._nullCount,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.childData;return new e(t,n,r,i,a,o,this.dictionary)}},{key:"slice",value:function(e,t){var n=this.stride,r=this.typeId,i=this.childData,a=+(0===this._nullCount)-1,o=16===r?n:1,u=this._sliceBuffers(e,t,n,r);return this.clone(this.type,this.offset+e,t,a,u,!i.length||this.valueOffsets?i:this._sliceChildren(i,o*e,o*t))}},{key:"_changeLengthAndBackfillNullBitmap",value:function(e){if(this.typeId===Gt.Null)return this.clone(this.type,0,e,0);var t=this.length,n=this.nullCount,r=new Uint8Array((e+63&-64)>>3).fill(255,0,t>>3);r[t>>3]=(1<<t-(-8&t))-1,n>0&&r.set(ln(this.offset,t,this.nullBitmap),0);var i=this.buffers;return i[qt.VALIDITY]=r,this.clone(this.type,0,e,n+(e-t),i)}},{key:"_sliceBuffers",value:function(e,t,n,r){var i,a=this.buffers;return(i=a[qt.TYPE])&&(a[qt.TYPE]=i.subarray(e,e+t)),(i=a[qt.OFFSET])&&(a[qt.OFFSET]=i.subarray(e,e+t+1))||(i=a[qt.DATA])&&(a[qt.DATA]=6===r?i:i.subarray(n*e,n*(e+t))),a}},{key:"_sliceChildren",value:function(e,t,n){return e.map((function(e){return e.slice(t,n)}))}}],[{key:"new",value:function(t,n,r,i,a,o,u){switch(a instanceof e?a=a.buffers:a||(a=[]),t.typeId){case Gt.Null:return e.Null(t,n,r);case Gt.Int:return e.Int(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[]);case Gt.Dictionary:return e.Dictionary(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[],u);case Gt.Float:return e.Float(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[]);case Gt.Bool:return e.Bool(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[]);case Gt.Decimal:return e.Decimal(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[]);case Gt.Date:return e.Date(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[]);case Gt.Time:return e.Time(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[]);case Gt.Timestamp:return e.Timestamp(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[]);case Gt.Interval:return e.Interval(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[]);case Gt.FixedSizeBinary:return e.FixedSizeBinary(t,n,r,i||0,a[qt.VALIDITY],a[qt.DATA]||[]);case Gt.Binary:return e.Binary(t,n,r,i||0,a[qt.VALIDITY],a[qt.OFFSET]||[],a[qt.DATA]||[]);case Gt.Utf8:return e.Utf8(t,n,r,i||0,a[qt.VALIDITY],a[qt.OFFSET]||[],a[qt.DATA]||[]);case Gt.List:return e.List(t,n,r,i||0,a[qt.VALIDITY],a[qt.OFFSET]||[],(o||[])[0]);case Gt.FixedSizeList:return e.FixedSizeList(t,n,r,i||0,a[qt.VALIDITY],(o||[])[0]);case Gt.Struct:return e.Struct(t,n,r,i||0,a[qt.VALIDITY],o||[]);case Gt.Map:return e.Map(t,n,r,i||0,a[qt.VALIDITY],a[qt.OFFSET]||[],(o||[])[0]);case Gt.Union:return e.Union(t,n,r,i||0,a[qt.VALIDITY],a[qt.TYPE]||[],a[qt.OFFSET]||o,o)}throw new Error("Unrecognized typeId ".concat(t.typeId))}},{key:"Null",value:function(t,n,r){return new e(t,n,r,0)}},{key:"Int",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ye(t.ArrayType,o),qe(a)])}},{key:"Dictionary",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[void 0,Ye(t.indices.ArrayType,o),qe(a)],[],u)}},{key:"Float",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ye(t.ArrayType,o),qe(a)])}},{key:"Bool",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ye(t.ArrayType,o),qe(a)])}},{key:"Decimal",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ye(t.ArrayType,o),qe(a)])}},{key:"Date",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ye(t.ArrayType,o),qe(a)])}},{key:"Time",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ye(t.ArrayType,o),qe(a)])}},{key:"Timestamp",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ye(t.ArrayType,o),qe(a)])}},{key:"Interval",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ye(t.ArrayType,o),qe(a)])}},{key:"FixedSizeBinary",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ye(t.ArrayType,o),qe(a)])}},{key:"Binary",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[Qe(o),qe(u),qe(a)])}},{key:"Utf8",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[Qe(o),qe(u),qe(a)])}},{key:"List",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[Qe(o),void 0,qe(a)],[u])}},{key:"FixedSizeList",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,void 0,qe(a)],[o])}},{key:"Struct",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,void 0,qe(a)],o)}},{key:"Map",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[Qe(o),void 0,qe(a)],[u])}},{key:"Union",value:function(t,n,r,i,a,o,u,s){var l=[void 0,void 0,qe(a),Ye(t.ArrayType,o)];return t.mode===en.Sparse?new e(t,n,r,i,l,u):(l[qt.OFFSET]=Qe(u),new e(t,n,r,i,l,s))}}]),e}();hr.prototype.childData=Object.freeze([]);function pr(e){if(null===e)return"null";if(void 0===e)return"undefined";switch(typeof e){case"number":case"bigint":return"".concat(e);case"string":return'"'.concat(e,'"')}return"function"===typeof e[Symbol.toPrimitive]?e[Symbol.toPrimitive]("string"):ArrayBuffer.isView(e)?"[".concat(e,"]"):JSON.stringify(e)}function yr(e){if(!e||e.length<=0)return function(e){return!0};var t="",n=e.filter((function(e){return e===e}));return n.length>0&&(t="\n    switch (x) {".concat(n.map((function(e){return"\n        case ".concat(function(e){if("bigint"!==typeof e)return pr(e);if(me)return"".concat(pr(e),"n");return'"'.concat(pr(e),'"')}(e),":")})).join(""),"\n            return false;\n    }")),e.length!==n.length&&(t="if (x !== x) return false;\n".concat(t)),new Function("x","".concat(t,"\nreturn true;"))}var vr=function(e,t){return(e*t+63&-64||64)/t},br=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e.length>=t?e.subarray(0,t):We(new e.constructor(t),e,0)},mr=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;C(this,e),this.buffer=t,this.stride=n,this.BYTES_PER_ELEMENT=t.BYTES_PER_ELEMENT,this.ArrayType=t.constructor,this._resize(this.length=t.length/n|0)}return M(e,[{key:"byteLength",get:function(){return this.length*this.stride*this.BYTES_PER_ELEMENT|0}},{key:"reservedLength",get:function(){return this.buffer.length/this.stride}},{key:"reservedByteLength",get:function(){return this.buffer.byteLength}},{key:"set",value:function(e,t){return this}},{key:"append",value:function(e){return this.set(this.length,e)}},{key:"reserve",value:function(e){if(e>0){this.length+=e;var t=this.stride,n=this.length*t,r=this.buffer.length;n>=r&&this._resize(vr(0===r?1*n:2*n,this.BYTES_PER_ELEMENT))}return this}},{key:"flush",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.length;e=vr(e*this.stride,this.BYTES_PER_ELEMENT);var t=br(this.buffer,e);return this.clear(),t}},{key:"clear",value:function(){return this.length=0,this._resize(0),this}},{key:"_resize",value:function(e){return this.buffer=We(new this.ArrayType(e),this.buffer)}}]),e}();mr.prototype.offset=0;var gr=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"last",value:function(){return this.get(this.length-1)}},{key:"get",value:function(e){return this.buffer[e]}},{key:"set",value:function(e,t){return this.reserve(e-this.length+1),this.buffer[e*this.stride]=t,this}}]),n}(mr),kr=function(e){ie(n,e);var t=ce(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Uint8Array(0);return C(this,n),(e=t.call(this,r,1/8)).numValid=0,e}return M(n,[{key:"numInvalid",get:function(){return this.length-this.numValid}},{key:"get",value:function(e){return this.buffer[e>>3]>>e%8&1}},{key:"set",value:function(e,t){var n=this.reserve(e-this.length+1).buffer,r=e>>3,i=e%8,a=n[r]>>i&1;return t?0===a&&(n[r]|=1<<i,++this.numValid):1===a&&(n[r]&=~(1<<i),--this.numValid),this}},{key:"clear",value:function(){return this.numValid=0,jt(ae(n.prototype),"clear",this).call(this)}}]),n}(gr),wr=function(e){ie(n,e);var t=ce(n);function n(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Int32Array(1);return C(this,n),t.call(this,e,1)}return M(n,[{key:"append",value:function(e){return this.set(this.length-1,e)}},{key:"set",value:function(e,t){var n=this.length-1,r=this.reserve(e-n+1).buffer;return n<e++&&r.fill(r[n],n,e),r[e]=r[e-1]+t,this}},{key:"flush",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.length-1;return e>this.length&&this.set(e-1,0),jt(ae(n.prototype),"flush",this).call(this,e+1)}}]),n}(gr),_r=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"ArrayType64",get:function(){return this._ArrayType64||(this._ArrayType64=this.buffer instanceof Int32Array?we:Se)}},{key:"set",value:function(e,t){switch(this.reserve(e-this.length+1),typeof t){case"bigint":this.buffer64[e]=t;break;case"number":this.buffer[e*this.stride]=t;break;default:this.buffer.set(t,e*this.stride)}return this}},{key:"_resize",value:function(e){var t=jt(ae(n.prototype),"_resize",this).call(this,e),r=t.byteLength/(this.BYTES_PER_ELEMENT*this.stride);return me&&(this.buffer64=new this.ArrayType64(t.buffer,t.byteOffset,r)),t}}]),n}(mr),xr=function(){function e(t){var n=t.type,r=t.nullValues;C(this,e),this.length=0,this.finished=!1,this.type=n,this.children=[],this.nullValues=r,this.stride=dr(n),this._nulls=new kr,r&&r.length>0&&(this._isValid=yr(r))}return M(e,[{key:"toVector",value:function(){return Qt.new(this.flush())}},{key:"ArrayType",get:function(){return this.type.ArrayType}},{key:"nullCount",get:function(){return this._nulls.numInvalid}},{key:"numChildren",get:function(){return this.children.length}},{key:"byteLength",get:function(){var e=0;return this._offsets&&(e+=this._offsets.byteLength),this._values&&(e+=this._values.byteLength),this._nulls&&(e+=this._nulls.byteLength),this._typeIds&&(e+=this._typeIds.byteLength),this.children.reduce((function(e,t){return e+t.byteLength}),e)}},{key:"reservedLength",get:function(){return this._nulls.reservedLength}},{key:"reservedByteLength",get:function(){var e=0;return this._offsets&&(e+=this._offsets.reservedByteLength),this._values&&(e+=this._values.reservedByteLength),this._nulls&&(e+=this._nulls.reservedByteLength),this._typeIds&&(e+=this._typeIds.reservedByteLength),this.children.reduce((function(e,t){return e+t.reservedByteLength}),e)}},{key:"valueOffsets",get:function(){return this._offsets?this._offsets.buffer:null}},{key:"values",get:function(){return this._values?this._values.buffer:null}},{key:"nullBitmap",get:function(){return this._nulls?this._nulls.buffer:null}},{key:"typeIds",get:function(){return this._typeIds?this._typeIds.buffer:null}},{key:"append",value:function(e){return this.set(this.length,e)}},{key:"isValid",value:function(e){return this._isValid(e)}},{key:"set",value:function(e,t){return this.setValid(e,this.isValid(t))&&this.setValue(e,t),this}},{key:"setValue",value:function(e,t){this._setValue(this,e,t)}},{key:"setValid",value:function(e,t){return this.length=this._nulls.set(e,+t).length,t}},{key:"addChild",value:function(e){arguments.length>1&&void 0!==arguments[1]||"".concat(this.numChildren);throw new Error('Cannot append children to non-nested type "'.concat(this.type,'"'))}},{key:"getChildAt",value:function(e){return this.children[e]||null}},{key:"flush",value:function(){var e=[],t=this._values,n=this._offsets,r=this._typeIds,i=this.length,a=this.nullCount;r?(e[qt.TYPE]=r.flush(i),n&&(e[qt.OFFSET]=n.flush(i))):n?(t&&(e[qt.DATA]=t.flush(n.last())),e[qt.OFFSET]=n.flush(i)):t&&(e[qt.DATA]=t.flush(i)),a>0&&(e[qt.VALIDITY]=this._nulls.flush(i));var o=hr.new(this.type,0,i,a,e,this.children.map((function(e){return e.flush()})));return this.clear(),o}},{key:"finish",value:function(){return this.finished=!0,this.children.forEach((function(e){return e.finish()})),this}},{key:"clear",value:function(){return this.length=0,this._offsets&&this._offsets.clear(),this._values&&this._values.clear(),this._nulls&&this._nulls.clear(),this._typeIds&&this._typeIds.clear(),this.children.forEach((function(e){return e.clear()})),this}}],[{key:"new",value:function(e){}},{key:"throughNode",value:function(e){throw new Error('"throughNode" not available in this environment')}},{key:"throughDOM",value:function(e){throw new Error('"throughDOM" not available in this environment')}},{key:"throughIterable",value:function(e){return function(e){var t=e.queueingStrategy,n=void 0===t?"count":t,r=e.highWaterMark,i=void 0===r?"bytes"!==n?1e3:Math.pow(2,14):r,a="bytes"!==n?"length":"byteLength";return N.mark((function t(n){var r,o,u,s,l;return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=0,o=xr.new(e),u=O(n),t.prev=3,u.s();case 5:if((s=u.n()).done){t.next=14;break}if(l=s.value,!(o.append(l)[a]>=i)){t.next=12;break}if(t.t0=++r,!t.t0){t.next=12;break}return t.next=12,o.toVector();case 12:t.next=5;break;case 14:t.next=19;break;case 16:t.prev=16,t.t1=t.catch(3),u.e(t.t1);case 19:return t.prev=19,u.f(),t.finish(19);case 22:if(!(o.finish().length>0||0===r)){t.next=25;break}return t.next=25,o.toVector();case 25:case"end":return t.stop()}}),t,null,[[3,16,19,22]])}))}(e)}},{key:"throughAsyncIterable",value:function(e){return function(e){var t=e.queueingStrategy,n=void 0===t?"count":t,r=e.highWaterMark,i=void 0===r?"bytes"!==n?1e3:Math.pow(2,14):r,a="bytes"!==n?"length":"byteLength";return function(){var t=j(N.mark((function t(n){var r,o,u,s,l,c,f,d,h;return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=0,o=xr.new(e),u=!0,s=!1,t.prev=4,c=V(n);case 6:return t.next=8,R(c.next());case 8:return f=t.sent,u=f.done,t.next=12,R(f.value);case 12:if(d=t.sent,u){t.next=23;break}if(h=d,!(o.append(h)[a]>=i)){t.next=20;break}if(t.t0=++r,!t.t0){t.next=20;break}return t.next=20,o.toVector();case 20:u=!0,t.next=6;break;case 23:t.next=29;break;case 25:t.prev=25,t.t1=t.catch(4),s=!0,l=t.t1;case 29:if(t.prev=29,t.prev=30,u||null==c.return){t.next=34;break}return t.next=34,R(c.return());case 34:if(t.prev=34,!s){t.next=37;break}throw l;case 37:return t.finish(34);case 38:return t.finish(29);case 39:if(!(o.finish().length>0||0===r)){t.next=42;break}return t.next=42,o.toVector();case 42:case"end":return t.stop()}}),t,null,[[4,25,29,39],[30,,34,38]])})));return function(e){return t.apply(this,arguments)}}()}(e)}}]),e}();xr.prototype.length=1,xr.prototype.stride=1,xr.prototype.children=null,xr.prototype.finished=!1,xr.prototype.nullValues=null,xr.prototype._isValid=function(){return!0};var Sr=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._values=new gr(new r.ArrayType(0),r.stride),r}return M(n,[{key:"setValue",value:function(e,t){var r=this._values;return r.reserve(e-r.length+1),jt(ae(n.prototype),"setValue",this).call(this,e,t)}}]),n}(xr),Tr=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._pendingLength=0,r._offsets=new wr,r}return M(n,[{key:"setValue",value:function(e,t){var n=this._pending||(this._pending=new Map),r=n.get(e);r&&(this._pendingLength-=r.length),this._pendingLength+=t.length,n.set(e,t)}},{key:"setValid",value:function(e,t){return!!jt(ae(n.prototype),"setValid",this).call(this,e,t)||((this._pending||(this._pending=new Map)).set(e,void 0),!1)}},{key:"clear",value:function(){return this._pendingLength=0,this._pending=void 0,jt(ae(n.prototype),"clear",this).call(this)}},{key:"flush",value:function(){return this._flush(),jt(ae(n.prototype),"flush",this).call(this)}},{key:"finish",value:function(){return this._flush(),jt(ae(n.prototype),"finish",this).call(this)}},{key:"_flush",value:function(){var e=this._pending,t=this._pendingLength;return this._pendingLength=0,this._pending=void 0,e&&e.size>0&&this._flushPending(e,t),this}}]),n}(xr);var Ir=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._values=new kr,r}return M(n,[{key:"setValue",value:function(e,t){this._values.set(e,+t)}}]),n}(xr),Er=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"setValue",value:function(e,t){}},{key:"setValid",value:function(e,t){return this.length=Math.max(e+1,this.length),t}}]),n}(xr),Ar=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Sr),Fr=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Ar),Or=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Ar),Br=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Sr),Dr=function(e){ie(n,e);var t=ce(n);function n(e){var r,i=e.type,a=e.nullValues,o=e.dictionaryHashFunction;return C(this,n),(r=t.call(this,{type:new fr(i.dictionary,i.indices,i.id,i.isOrdered)}))._nulls=null,r._dictionaryOffset=0,r._keysToIndices=Object.create(null),r.indices=xr.new({type:r.type.indices,nullValues:a}),r.dictionary=xr.new({type:r.type.dictionary,nullValues:null}),"function"===typeof o&&(r.valueToKey=o),r}return M(n,[{key:"values",get:function(){return this.indices.values}},{key:"nullCount",get:function(){return this.indices.nullCount}},{key:"nullBitmap",get:function(){return this.indices.nullBitmap}},{key:"byteLength",get:function(){return this.indices.byteLength+this.dictionary.byteLength}},{key:"reservedLength",get:function(){return this.indices.reservedLength+this.dictionary.reservedLength}},{key:"reservedByteLength",get:function(){return this.indices.reservedByteLength+this.dictionary.reservedByteLength}},{key:"isValid",value:function(e){return this.indices.isValid(e)}},{key:"setValid",value:function(e,t){var n=this.indices;return t=n.setValid(e,t),this.length=n.length,t}},{key:"setValue",value:function(e,t){var n=this._keysToIndices,r=this.valueToKey(t),i=n[r];return void 0===i&&(n[r]=i=this._dictionaryOffset+this.dictionary.append(t).length-1),this.indices.setValue(e,i)}},{key:"flush",value:function(){var e=this.type,t=this._dictionary,n=this.dictionary.toVector(),r=this.indices.flush().clone(e);return r.dictionary=t?t.concat(n):n,this.finished||(this._dictionaryOffset+=n.length),this._dictionary=r.dictionary,this.clear(),r}},{key:"finish",value:function(){return this.indices.finish(),this.dictionary.finish(),this._dictionaryOffset=0,this._keysToIndices=Object.create(null),jt(ae(n.prototype),"finish",this).call(this)}},{key:"clear",value:function(){return this.indices.clear(),this.dictionary.clear(),jt(ae(n.prototype),"clear",this).call(this)}},{key:"valueToKey",value:function(e){return"string"===typeof e?e:"".concat(e)}}]),n}(xr),Cr=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Sr),Lr=new Float64Array(1),Mr=new Uint32Array(Lr.buffer);function Nr(e){var t=(31744&e)>>10,n=(1023&e)/1024,r=Math.pow(-1,(32768&e)>>15);switch(t){case 31:return r*(n?NaN:1/0);case 0:return r*(n?6103515625e-14*n:0)}return r*Math.pow(2,t-15)*(1+n)}function Pr(e){if(e!==e)return 32256;Lr[0]=e;var t=(2147483648&Mr[1])>>16&65535,n=2146435072&Mr[1],r=0;return n>=1089470464?Mr[0]>0?n=31744:(n=(2080374784&n)>>16,r=(1048575&Mr[1])>>10):n<=1056964608?(r=1048576+((r=1048576+(1048575&Mr[1]))<<(n>>20)-998)>>21,n=0):(n=n-1056964608>>10,r=512+(1048575&Mr[1])>>10),t|n|65535&r}var Ur=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Sr),Rr=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"setValue",value:function(e,t){this._values.set(e,Pr(t))}}]),n}(Ur),zr=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"setValue",value:function(e,t){this._values.set(e,t)}}]),n}(Ur),jr=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"setValue",value:function(e,t){this._values.set(e,t)}}]),n}(Ur);function Vr(e,t,n){return(Vr=oe()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&re(i,n.prototype),i}).apply(null,arguments)}var Wr,Hr,Yr=Symbol.for("isArrowBigNum");function $r(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return 0===n.length?Object.setPrototypeOf(Ye(this.TypedArray,e),this.constructor.prototype):Object.setPrototypeOf(Vr(this.TypedArray,[e].concat(n)),this.constructor.prototype)}function Kr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return $r.apply(this,t)}function Qr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return $r.apply(this,t)}function Gr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return $r.apply(this,t)}function qr(e){for(var t,n,r=e.buffer,i=e.byteOffset,a=e.length,o=e.signed,u=new Int32Array(r,i,a),s=0,l=0,c=u.length;l<c;)n=u[l++],t=u[l++],o||(t>>>=0),s+=(n>>>0)+t*Math.pow(l,32);return s}function Xr(e){var t="",n=new Uint32Array(2),r=new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2),i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer),a=-1,o=r.length-1;do{for(n[0]=r[a=0];a<o;)r[a++]=n[1]=n[0]/10,n[0]=(n[0]-10*n[1]<<16)+r[a];r[a]=n[1]=n[0]/10,n[0]=n[0]-10*n[1],t="".concat(n[0]).concat(t)}while(i[0]||i[1]||i[2]||i[3]);return t||"0"}$r.prototype[Yr]=!0,$r.prototype.toJSON=function(){return'"'.concat(Wr(this),'"')},$r.prototype.valueOf=function(){return qr(this)},$r.prototype.toString=function(){return Wr(this)},$r.prototype[Symbol.toPrimitive]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";switch(e){case"number":return qr(this);case"string":return Wr(this);case"default":return Hr(this)}return Wr(this)},Object.setPrototypeOf(Kr.prototype,Object.create(Int32Array.prototype)),Object.setPrototypeOf(Qr.prototype,Object.create(Uint32Array.prototype)),Object.setPrototypeOf(Gr.prototype,Object.create(Uint32Array.prototype)),Object.assign(Kr.prototype,$r.prototype,{constructor:Kr,signed:!0,TypedArray:Int32Array,BigIntArray:we}),Object.assign(Qr.prototype,$r.prototype,{constructor:Qr,signed:!1,TypedArray:Uint32Array,BigIntArray:Se}),Object.assign(Gr.prototype,$r.prototype,{constructor:Gr,signed:!0,TypedArray:Uint32Array,BigIntArray:Se}),me?(Hr=function(e){return 8===e.byteLength?new e.BigIntArray(e.buffer,e.byteOffset,1)[0]:Xr(e)},Wr=function(e){return 8===e.byteLength?"".concat(new e.BigIntArray(e.buffer,e.byteOffset,1)[0]):Xr(e)}):Hr=Wr=Xr;var Zr,Jr=function(){function e(t,n){return C(this,e),e.new(t,n)}return M(e,null,[{key:"new",value:function(e,t){switch(t){case!0:return new Kr(e);case!1:return new Qr(e)}switch(e.constructor){case Int8Array:case Int16Array:case Int32Array:case we:return new Kr(e)}return 16===e.byteLength?new Gr(e):new Qr(e)}},{key:"signed",value:function(e){return new Kr(e)}},{key:"unsigned",value:function(e){return new Qr(e)}},{key:"decimal",value:function(e){return new Gr(e)}}]),e}(),ei=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"setValue",value:function(e,t){this._values.set(e,t)}}]),n}(Sr),ti=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ei),ni=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ei),ri=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ei),ii=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),e.nullValues&&(e.nullValues=e.nullValues.map(li)),(r=t.call(this,e))._values=new _r(new Int32Array(0),2),r}return M(n,[{key:"values64",get:function(){return this._values.buffer64}},{key:"isValid",value:function(e){return jt(ae(n.prototype),"isValid",this).call(this,li(e))}}]),n}(ei),ai=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ei),oi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ei),ui=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ei),si=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),e.nullValues&&(e.nullValues=e.nullValues.map(li)),(r=t.call(this,e))._values=new _r(new Uint32Array(0),2),r}return M(n,[{key:"values64",get:function(){return this._values.buffer64}},{key:"isValid",value:function(e){return jt(ae(n.prototype),"isValid",this).call(this,li(e))}}]),n}(ei),li=(Zr={BigIntArray:we},function(e){return ArrayBuffer.isView(e)&&(Zr.buffer=e.buffer,Zr.byteOffset=e.byteOffset,Zr.byteLength=e.byteLength,e=Hr(Zr),Zr.buffer=null),e}),ci=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Sr),fi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ci),di=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ci),hi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ci),pi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ci),yi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Sr),vi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(yi),bi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(yi),mi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(yi),gi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(yi),ki=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Sr),wi=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ki),_i=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ki),xi=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._values=new mr(new Uint8Array(0)),r}return M(n,[{key:"byteLength",get:function(){var e=this._pendingLength+4*this.length;return this._offsets&&(e+=this._offsets.byteLength),this._values&&(e+=this._values.byteLength),this._nulls&&(e+=this._nulls.byteLength),e}},{key:"setValue",value:function(e,t){return jt(ae(n.prototype),"setValue",this).call(this,e,qe(t))}},{key:"_flushPending",value:function(e,t){var n,r,i=this._offsets,a=this._values.reserve(t).buffer,o=0,u=0,s=0,l=O(e);try{for(l.s();!(r=l.n()).done;){var c=Object(P.a)(r.value,2);o=c[0],void 0===(n=c[1])?i.set(o,0):(u=n.length,a.set(n,s),i.set(o,u),s+=u)}}catch(f){l.e(f)}finally{l.f()}}}]),n}(Tr),Si=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._values=new mr(new Uint8Array(0)),r}return M(n,[{key:"byteLength",get:function(){var e=this._pendingLength+4*this.length;return this._offsets&&(e+=this._offsets.byteLength),this._values&&(e+=this._values.byteLength),this._nulls&&(e+=this._nulls.byteLength),e}},{key:"setValue",value:function(e,t){return jt(ae(n.prototype),"setValue",this).call(this,e,ne(t))}},{key:"_flushPending",value:function(e,t){}}]),n}(Tr);Si.prototype._flushPending=xi.prototype._flushPending;var Ti=function(){function e(){C(this,e)}return M(e,[{key:"length",get:function(){return this._values.length}},{key:"get",value:function(e){return this._values[e]}},{key:"clear",value:function(){return this._values=null,this}},{key:"bind",value:function(e){return e instanceof Qt?e:(this._values=e,this)}}]),e}(),Ii=Symbol.for("parent"),Ei=Symbol.for("rowIndex"),Ai=Symbol.for("keyToIdx"),Fi=Symbol.for("idxToVal"),Oi=Symbol.for("nodejs.util.inspect.custom"),Bi=function(){function e(t,n){C(this,e),this[Ii]=t,this.size=n}return M(e,[{key:"entries",value:function(){return this[Symbol.iterator]()}},{key:"has",value:function(e){return void 0!==this.get(e)}},{key:"get",value:function(e){var t=void 0;if(null!==e&&void 0!==e){var n=this[Ai]||(this[Ai]=new Map),r=n.get(e);if(void 0!==r){var i=this[Fi]||(this[Fi]=new Array(this.size));void 0!==(t=i[r])||(i[r]=t=this.getValue(r))}else if((r=this.getIndex(e))>-1){n.set(e,r);var a=this[Fi]||(this[Fi]=new Array(this.size));void 0!==(t=a[r])||(a[r]=t=this.getValue(r))}}return t}},{key:"set",value:function(e,t){if(null!==e&&void 0!==e){var n=this[Ai]||(this[Ai]=new Map),r=n.get(e);if(void 0===r&&n.set(e,r=this.getIndex(e)),r>-1)(this[Fi]||(this[Fi]=new Array(this.size)))[r]=this.setValue(r,t)}return this}},{key:"clear",value:function(){throw new Error("Clearing ".concat(this[Symbol.toStringTag]," not supported."))}},{key:"delete",value:function(e){throw new Error("Deleting ".concat(this[Symbol.toStringTag]," values not supported."))}},{key:Symbol.iterator,value:N.mark((function e(){var t,n,r,i,a,o,u,s,l;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this.keys(),n=this.values(),r=this[Ai]||(this[Ai]=new Map),i=this[Fi]||(this[Fi]=new Array(this.size)),u=0;case 5:if((s=t.next()).done||(l=n.next()).done){e.next=15;break}return a=s.value,o=l.value,i[u]=o,r.has(a)||r.set(a,u),e.next=12,[a,o];case 12:++u,e.next=5;break;case 15:case"end":return e.stop()}}),e,this)}))},{key:"forEach",value:function(e,t){for(var n,r,i,a,o=this.keys(),u=this.values(),s=void 0===t?e:function(n,r,i){return e.call(t,n,r,i)},l=this[Ai]||(this[Ai]=new Map),c=this[Fi]||(this[Fi]=new Array(this.size)),f=0;!(i=o.next()).done&&!(a=u.next()).done;++f)n=i.value,r=a.value,c[f]=r,l.has(n)||l.set(n,f),s(r,n,this)}},{key:"toArray",value:function(){return vn(this.values())}},{key:"toJSON",value:function(){var e={};return this.forEach((function(t,n){return e[n]=t})),e}},{key:"inspect",value:function(){return this.toString()}},{key:Oi,value:function(){return this.toString()}},{key:"toString",value:function(){var e=[];return this.forEach((function(t,n){n=pr(n),t=pr(t),e.push("".concat(n,": ").concat(t))})),"{ ".concat(e.join(", ")," }")}}]),e}();Bi[Symbol.toStringTag]=function(e){var t;return Object.defineProperties(e,(t={size:{writable:!0,enumerable:!1,configurable:!1,value:0}},Object(Ut.a)(t,Ii,{writable:!0,enumerable:!1,configurable:!1,value:null}),Object(Ut.a)(t,Ei,{writable:!0,enumerable:!1,configurable:!1,value:-1}),t)),e[Symbol.toStringTag]="Row"}(Bi.prototype);var Di=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),le(r=t.call(this,e,e.length),Ni(se(r)))}return M(n,[{key:"keys",value:function(){return this[Ii].getChildAt(0)[Symbol.iterator]()}},{key:"values",value:function(){return this[Ii].getChildAt(1)[Symbol.iterator]()}},{key:"getKey",value:function(e){return this[Ii].getChildAt(0).get(e)}},{key:"getIndex",value:function(e){return this[Ii].getChildAt(0).indexOf(e)}},{key:"getValue",value:function(e){return this[Ii].getChildAt(1).get(e)}},{key:"setValue",value:function(e,t){this[Ii].getChildAt(1).set(e,t)}}]),n}(Bi),Ci=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),le(r=t.call(this,e,e.type.children.length),Mi(se(r)))}return M(n,[{key:"keys",value:N.mark((function e(){var t,n,r;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=O(this[Ii].type.children),e.prev=1,t.s();case 3:if((n=t.n()).done){e.next=9;break}return r=n.value,e.next=7,r.name;case 7:e.next=3;break;case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(1),t.e(e.t0);case 14:return e.prev=14,t.f(),e.finish(14);case 17:case"end":return e.stop()}}),e,this,[[1,11,14,17]])}))},{key:"values",value:N.mark((function e(){var t,n,r;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=O(this[Ii].type.children),e.prev=1,t.s();case 3:if((n=t.n()).done){e.next=9;break}return r=n.value,e.next=7,this[r.name];case 7:e.next=3;break;case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(1),t.e(e.t0);case 14:return e.prev=14,t.f(),e.finish(14);case 17:case"end":return e.stop()}}),e,this,[[1,11,14,17]])}))},{key:"getKey",value:function(e){return this[Ii].type.children[e].name}},{key:"getIndex",value:function(e){return this[Ii].type.children.findIndex((function(t){return t.name===e}))}},{key:"getValue",value:function(e){return this[Ii].getChildAt(e).get(this[Ei])}},{key:"setValue",value:function(e,t){return this[Ii].getChildAt(e).set(this[Ei],t)}}]),n}(Bi);Object.setPrototypeOf(Bi.prototype,Map.prototype);var Li,Mi=function(){var e={enumerable:!0,configurable:!1,get:null,set:null};return function(t){var n,r=-1,i=t[Ai]||(t[Ai]=new Map),a=function(e){return function(){return this.get(e)}},o=function(e){return function(t){return this.set(e,t)}},u=O(t.keys());try{for(u.s();!(n=u.n()).done;){var s=n.value;i.set(s,++r),e.get=a(s),e.set=o(s),t.hasOwnProperty(s)||(e.enumerable=!0,Object.defineProperty(t,s,e)),t.hasOwnProperty(r)||(e.enumerable=!1,Object.defineProperty(t,r,e))}}catch(l){u.e(l)}finally{u.f()}return e.get=e.set=null,t}}(),Ni=function(){if("undefined"===typeof Proxy)return Mi;var e=Bi.prototype.has,t=Bi.prototype.get,n=Bi.prototype.set,r=Bi.prototype.getKey,i={isExtensible:function(){return!1},deleteProperty:function(){return!1},preventExtensions:function(){return!0},ownKeys:function(e){return vn(e.keys()).map((function(e){return"".concat(e)}))},has:function(e,t){switch(t){case"getKey":case"getIndex":case"getValue":case"setValue":case"toArray":case"toJSON":case"inspect":case"constructor":case"isPrototypeOf":case"propertyIsEnumerable":case"toString":case"toLocaleString":case"valueOf":case"size":case"has":case"get":case"set":case"clear":case"delete":case"keys":case"values":case"entries":case"forEach":case"__proto__":case"__defineGetter__":case"__defineSetter__":case"hasOwnProperty":case"__lookupGetter__":case"__lookupSetter__":case Symbol.iterator:case Symbol.toStringTag:case Ii:case Ei:case Fi:case Ai:case Oi:return!0}return"number"!==typeof t||e.has(t)||(t=e.getKey(t)),e.has(t)},get:function(n,i,a){switch(i){case"getKey":case"getIndex":case"getValue":case"setValue":case"toArray":case"toJSON":case"inspect":case"constructor":case"isPrototypeOf":case"propertyIsEnumerable":case"toString":case"toLocaleString":case"valueOf":case"size":case"has":case"get":case"set":case"clear":case"delete":case"keys":case"values":case"entries":case"forEach":case"__proto__":case"__defineGetter__":case"__defineSetter__":case"hasOwnProperty":case"__lookupGetter__":case"__lookupSetter__":case Symbol.iterator:case Symbol.toStringTag:case Ii:case Ei:case Fi:case Ai:case Oi:return Reflect.get(n,i,a)}return"number"!==typeof i||e.call(a,i)||(i=r.call(a,i)),t.call(a,i)},set:function(t,i,a,o){switch(i){case Ii:case Ei:case Fi:case Ai:return Reflect.set(t,i,a,o);case"getKey":case"getIndex":case"getValue":case"setValue":case"toArray":case"toJSON":case"inspect":case"constructor":case"isPrototypeOf":case"propertyIsEnumerable":case"toString":case"toLocaleString":case"valueOf":case"size":case"has":case"get":case"set":case"clear":case"delete":case"keys":case"values":case"entries":case"forEach":case"__proto__":case"__defineGetter__":case"__defineSetter__":case"hasOwnProperty":case"__lookupGetter__":case"__lookupSetter__":case Symbol.iterator:case Symbol.toStringTag:return!1}return"number"!==typeof i||e.call(o,i)||(i=r.call(o,i)),!!e.call(o,i)&&!!n.call(o,i,a)}};return function(e){return new Proxy(e,i)}}();function Pi(e,t,n){var r=e.length,i=t>-1?t:r+t%r;return n?n(e,i):i}function Ui(e,t,n,r){var i=e.length,a=void 0===i?0:i,o="number"!==typeof t?0:t,u="number"!==typeof n?a:n;return o<0&&(o=(o%a+a)%a),u<0&&(u=(u%a+a)%a),u<o&&(Li=o,o=u,u=Li),u>a&&(u=a),r?r(e,o,u):[o,u]}var Ri=me?be(0):0,zi=function(e){return e!==e};function ji(e){var t=typeof e;if("object"!==t||null===e)return zi(e)?zi:"bigint"!==t?function(t){return t===e}:function(t){return Ri+t===e};if(e instanceof Date){var n=e.valueOf();return function(e){return e instanceof Date&&e.valueOf()===n}}return ArrayBuffer.isView(e)?function(t){return!!t&&It(e,t)}:e instanceof Map?function(e){var t=-1,n=[];return e.forEach((function(e){return n[++t]=ji(e)})),Vi(n)}(e):Array.isArray(e)?function(e){for(var t=[],n=-1,r=e.length;++n<r;)t[n]=ji(e[n]);return Vi(t)}(e):e instanceof Qt?function(e){for(var t=[],n=-1,r=e.length;++n<r;)t[n]=ji(e.get(n));return Vi(t)}(e):function(e){var t=Object.keys(e);if(0===t.length)return function(){return!1};for(var n=[],r=-1,i=t.length;++r<i;)n[r]=ji(e[t[r]]);return Vi(n,t)}(e)}function Vi(e,t){return function(n){if(!n||"object"!==typeof n)return!1;switch(n.constructor){case Array:return function(e,t){var n=e.length;if(t.length!==n)return!1;for(var r=-1;++r<n;)if(!e[r](t[r]))return!1;return!0}(e,n);case Map:case Di:case Ci:return Wi(e,n,n.keys());case Object:case void 0:return Wi(e,n,t||Object.keys(n))}return n instanceof Qt&&function(e,t){var n=e.length;if(t.length!==n)return!1;for(var r=-1;++r<n;)if(!e[r](t.get(r)))return!1;return!0}(e,n)}}function Wi(e,t,n){for(var r=n[Symbol.iterator](),i=t instanceof Map?t.keys():Object.keys(t)[Symbol.iterator](),a=t instanceof Map?t.values():Object.values(t)[Symbol.iterator](),o=0,u=e.length,s=a.next(),l=r.next(),c=i.next();o<u&&!l.done&&!c.done&&!s.done&&(l.value===c.value&&e[o](s.value));++o,l=r.next(),c=i.next(),s=a.next());return!!(o===u&&l.done&&c.done&&s.done)||(r.return&&r.return(),i.return&&i.return(),a.return&&a.return(),!1)}var Hi=function(e){ie(n,e);var t=ce(n);function n(e){var r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Yi(i);return C(this,n),(r=t.call(this))._nullCount=-1,r._type=e,r._chunks=i,r._chunkOffsets=a,r._length=a[a.length-1],r._numChildren=(r._type.children||[]).length,r}return M(n,[{key:"type",get:function(){return this._type}},{key:"length",get:function(){return this._length}},{key:"chunks",get:function(){return this._chunks}},{key:"typeId",get:function(){return this._type.typeId}},{key:"VectorName",get:function(){return"Chunked<".concat(this._type,">")}},{key:"data",get:function(){return this._chunks[0]?this._chunks[0].data:null}},{key:"ArrayType",get:function(){return this._type.ArrayType}},{key:"numChildren",get:function(){return this._numChildren}},{key:"stride",get:function(){return this._chunks[0]?this._chunks[0].stride:1}},{key:"byteLength",get:function(){return this._chunks.reduce((function(e,t){return e+t.byteLength}),0)}},{key:"nullCount",get:function(){var e=this._nullCount;return e<0&&(this._nullCount=e=this._chunks.reduce((function(e,t){return e+t.nullCount}),0)),e}},{key:"indices",get:function(){if(Dn.isDictionary(this._type)){if(!this._indices){var e=this._chunks;this._indices=1===e.length?e[0].indices:n.concat.apply(n,vn(e.map((function(e){return e.indices}))))}return this._indices}return null}},{key:"dictionary",get:function(){return Dn.isDictionary(this._type)?this._chunks[this._chunks.length-1].data.dictionary:null}},{key:Symbol.iterator,value:N.mark((function e(){var t,n,r;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=O(this._chunks),e.prev=1,t.s();case 3:if((n=t.n()).done){e.next=8;break}return r=n.value,e.delegateYield(r,"t0",6);case 6:e.next=3;break;case 8:e.next=13;break;case 10:e.prev=10,e.t1=e.catch(1),t.e(e.t1);case 13:return e.prev=13,t.f(),e.finish(13);case 16:case"end":return e.stop()}}),e,this,[[1,10,13,16]])}))},{key:"clone",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._chunks;return new n(this._type,e)}},{key:"concat",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.clone(n.flatten.apply(n,[this].concat(t)))}},{key:"slice",value:function(e,t){return Ui(this,e,t,this._sliceInternal)}},{key:"getChildAt",value:function(e){if(e<0||e>=this._numChildren)return null;var t,r,i,a=this._children||(this._children=[]);return(t=a[e])?t:(r=(this._type.children||[])[e])&&(i=this._chunks.map((function(t){return t.getChildAt(e)})).filter((function(e){return null!=e}))).length>0?a[e]=new n(r.type,i):null}},{key:"search",value:function(e,t){var n=e,r=this._chunkOffsets,i=r.length-1;if(n<0)return null;if(n>=r[i])return null;if(i<=1)return t?t(this,0,n):[0,n];var a=0,o=0,u=0;do{if(a+1===i)return t?t(this,a,n-o):[a,n-o];n>=r[u=a+(i-a)/2|0]?a=u:i=u}while(n<r[i]&&n>=(o=r[a]));return null}},{key:"isValid",value:function(e){return!!this.search(e,this.isValidInternal)}},{key:"get",value:function(e){return this.search(e,this.getInternal)}},{key:"set",value:function(e,t){this.search(e,(function(e,n,r){return e.chunks[n].set(r,t)}))}},{key:"indexOf",value:function(e,t){var n=this;return t&&"number"===typeof t?this.search(t,(function(t,r,i){return n.indexOfInternal(t,r,i,e)})):this.indexOfInternal(this,0,Math.max(0,t||0),e)}},{key:"toArray",value:function(){var e=this.chunks,t=e.length,n=this._type.ArrayType;if(t<=0)return new n(0);if(t<=1)return e[0].toArray();for(var r=0,i=new Array(t),a=-1;++a<t;)r+=(i[a]=e[a].toArray()).length;n!==i[0].constructor&&(n=i[0].constructor);for(var o=new n(r),u=n===Array?Ki:$i,s=-1,l=0;++s<t;)l=u(i[s],o,l);return o}},{key:"getInternal",value:function(e,t,n){return e._chunks[t].get(n)}},{key:"isValidInternal",value:function(e,t,n){return e._chunks[t].isValid(n)}},{key:"indexOfInternal",value:function(e,t,n,r){for(var i=e._chunks,a=t-1,o=i.length,u=n,s=0,l=-1;++a<o;){if(~(l=i[a].indexOf(r,u)))return s+l;u=0,s+=i[a].length}return-1}},{key:"_sliceInternal",value:function(e,t,n){for(var r=[],i=e.chunks,a=e._chunkOffsets,o=-1,u=i.length;++o<u;){var s=i[o],l=s.length,c=a[o];if(c>=n)break;if(!(t>=c+l))if(c>=t&&c+l<=n)r.push(s);else{var f=Math.max(0,t-c),d=Math.min(n-c,l);r.push(s.slice(f,d))}}return e.clone(r)}}],[{key:"flatten",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return ea(Qt,t)}},{key:"concat",value:function(){var e=n.flatten.apply(n,arguments);return new n(e[0].type,e)}}]),n}(Qt);function Yi(e){for(var t=new Uint32Array((e||[]).length+1),n=t[0]=0,r=t.length,i=0;++i<r;)t[i]=n+=e[i-1].length;return t}var $i=function(e,t,n){return t.set(e,n),n+e.length},Ki=function(e,t,n){for(var r=n,i=-1,a=e.length;++i<a;)t[r++]=e[i];return r},Qi=function(e){ie(n,e);var t=ce(n);function n(e){var r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],a=arguments.length>2?arguments[2]:void 0;return C(this,n),i=Hi.flatten.apply(Hi,vn(i)),(r=t.call(this,e.type,i,a))._field=e,1!==i.length||se(r)instanceof Gi?r:le(r,new Gi(e,i[0],r._chunkOffsets))}return M(n,[{key:"field",get:function(){return this._field}},{key:"name",get:function(){return this._field.name}},{key:"nullable",get:function(){return this._field.nullable}},{key:"metadata",get:function(){return this._field.metadata}},{key:"clone",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._chunks;return new n(this._field,e)}},{key:"getChildAt",value:function(e){if(e<0||e>=this.numChildren)return null;var t,r,i,a=this._children||(this._children=[]);return(t=a[e])?t:(r=(this.type.children||[])[e])&&(i=this._chunks.map((function(t){return t.getChildAt(e)})).filter((function(e){return null!=e}))).length>0?a[e]=new n(r,i):null}}],[{key:"new",value:function(e,t){for(var r=arguments.length,i=new Array(r>2?r-2:0),a=2;a<r;a++)i[a-2]=arguments[a];var o=Hi.flatten(Array.isArray(t)?[].concat(vn(t),i):t instanceof Qt?[t].concat(i):[Qt.new.apply(Qt,[t].concat(i))]);if("string"===typeof e){var u=o[0].data.type;e=new oa(e,u,!0)}else!e.nullable&&o.some((function(e){return e.nullCount>0}))&&(e=e.clone({nullable:!0}));return new n(e,o)}}]),n}(Hi),Gi=function(e){ie(n,e);var t=ce(n);function n(e,r,i){var a;return C(this,n),(a=t.call(this,e,[r],i))._chunk=r,a}return M(n,[{key:"search",value:function(e,t){return t?t(this,0,e):[0,e]}},{key:"isValid",value:function(e){return this._chunk.isValid(e)}},{key:"get",value:function(e){return this._chunk.get(e)}},{key:"set",value:function(e,t){this._chunk.set(e,t)}},{key:"indexOf",value:function(e,t){return this._chunk.indexOf(e,t)}}]),n}(Qi),qi=Array.isArray,Xi=function(e,t){return na(e,t,[],0)},Zi=function(e){var t=ia(e,[[],[]]),n=Object(P.a)(t,2),r=n[0];return n[1].map((function(e,t){return e instanceof Qi?Qi.new(e.field.clone(r[t]),e):e instanceof Qt?Qi.new(r[t],e):Qi.new(r[t],[])}))},Ji=function(e){return ia(e,[[],[]])},ea=function(e,t){return function e(t,n,r,i){var a,o=i,u=-1,s=n.length;for(;++u<s;)qi(a=n[u])?o=e(t,a,r,o).length:a instanceof Hi?o=e(t,a.chunks,r,o).length:a instanceof t&&(r[o++]=a);return r}(e,t,[],0)},ta=function(e,t){return function e(t,n,r,i){var a,o=i,u=-1,s=n.length;for(;++u<s;)qi(a=n[u])?o=e(t,a,r,o).length:a instanceof t?o=na(Qt,a.schema.fields.map((function(e,t){return a.getChildAt(t)})),r,o).length:a instanceof Qt&&(r[o++]=a);return r}(e,t,[],0)};function na(e,t,n,r){for(var i,a=r,o=-1,u=t.length;++o<u;)qi(i=t[o])?a=na(e,i,n,a).length:i instanceof e&&(n[a++]=i);return n}var ra=function(e,t,n){var r=Object(P.a)(t,2),i=r[0],a=r[1];return e[0][n]=i,e[1][n]=a,e};function ia(e,t){var n,r;switch(r=e.length){case 0:return t;case 1:if(n=t[0],!e[0])return t;if(qi(e[0]))return ia(e[0],t);if(!(e[0]instanceof hr||e[0]instanceof Qt||e[0]instanceof Dn)){var i=Object.entries(e[0]).reduce(ra,t),a=Object(P.a)(i,2);n=a[0],e=a[1]}break;default:qi(n=e[r-1])?e=qi(e[0])?e[0]:e.slice(0,r-1):(e=qi(e[0])?e[0]:e,n=[])}for(var o,u,s=-1,l=-1,c=-1,f=e.length,d=Object(P.a)(t,2),h=d[0],p=d[1];++c<f;)if((u=e[c])instanceof Qi&&(p[++l]=u))h[++s]=u.field.clone(n[c],u.type,!0);else{var y=n[c];o=void 0===y?c:y,u instanceof Dn&&(p[++l]=u)?h[++s]=oa.new(o,u,!0):u&&u.type&&(p[++l]=u)&&(u instanceof hr&&(p[l]=u=Qt.new(u)),h[++s]=oa.new(o,u.type,!0))}return t}var aa=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0;C(this,e),this.fields=t||[],this.metadata=n||new Map,r||(r=sa(t)),this.dictionaries=r}return M(e,[{key:Symbol.toStringTag,get:function(){return"Schema"}},{key:"toString",value:function(){return"Schema<{ ".concat(this.fields.map((function(e,t){return"".concat(t,": ").concat(e)})).join(", ")," }>")}},{key:"compareTo",value:function(e){return Bn.compareSchemas(this,e)}},{key:"select",value:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var i=n.reduce((function(e,t){return(e[t]=!0)&&e}),Object.create(null));return new e(this.fields.filter((function(e){return i[e.name]})),this.metadata)}},{key:"selectAt",value:function(){for(var t=this,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return new e(r.map((function(e){return t.fields[e]})).filter(Boolean),this.metadata)}},{key:"assign",value:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var i=n[0]instanceof e?n[0]:new e(Xi(oa,n)),a=vn(this.fields),o=ua(ua(new Map,this.metadata),i.metadata),u=i.fields.filter((function(e){var t=a.findIndex((function(t){return t.name===e.name}));return!~t||(a[t]=e.clone({metadata:ua(ua(new Map,a[t].metadata),e.metadata)}))&&!1})),s=sa(u,new Map);return new e([].concat(vn(a),vn(u)),o,new Map([].concat(vn(this.dictionaries),vn(s))))}}],[{key:"from",value:function(){return e.new(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1])}},{key:"new",value:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return new e(Ji(n)[0])}}]),e}(),oa=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3?arguments[3]:void 0;C(this,e),this.name=t,this.type=n,this.nullable=r,this.metadata=i||new Map}return M(e,[{key:"typeId",get:function(){return this.type.typeId}},{key:Symbol.toStringTag,get:function(){return"Field"}},{key:"toString",value:function(){return"".concat(this.name,": ").concat(this.type)}},{key:"compareTo",value:function(e){return Bn.compareField(this,e)}},{key:"clone",value:function(){for(var t,n,r,i,a,o,u,s,l,c,f,d=arguments.length,h=new Array(d),p=0;p<d;p++)h[p]=arguments[p];var y=h[0],v=h[1],b=h[2],m=h[3];return h[0]&&"object"===typeof h[0]?(y=void 0===(s=(u=h[0]).name)?this.name:s,v=void 0===(l=u.type)?this.type:l,b=void 0===(c=u.nullable)?this.nullable:c,m=void 0===(f=u.metadata)?this.metadata:f):(t=h,y=void 0===(r=(n=Object(P.a)(t,4))[0])?this.name:r,v=void 0===(i=n[1])?this.type:i,b=void 0===(a=n[2])?this.nullable:a,m=void 0===(o=n[3])?this.metadata:o),e.new(y,v,b,m)}}],[{key:"new",value:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var i=n[0],a=n[1],o=n[2],u=n[3];return n[0]&&"object"===typeof n[0]&&(i=n[0].name,void 0===a&&(a=n[0].type),void 0===o&&(o=n[0].nullable),void 0===u&&(u=n[0].metadata)),new e("".concat(i),a,o,u)}}]),e}();function ua(e,t){return new Map([].concat(vn(e||new Map),vn(t||new Map)))}function sa(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map,n=-1,r=e.length;++n<r;){var i=e[n],a=i.type;if(Dn.isDictionary(a))if(t.has(a.id)){if(t.get(a.id)!==a.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else t.set(a.id,a.dictionary);a.children&&a.children.length>0&&sa(a.children,t)}return t}aa.prototype.fields=null,aa.prototype.metadata=null,aa.prototype.dictionaries=null,oa.prototype.type=null,oa.prototype.name=null,oa.prototype.nullable=null,oa.prototype.metadata=null;var la=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._run=new Ti,r._offsets=new wr,r}return M(n,[{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0";if(this.numChildren>0)throw new Error("ListBuilder can only have one child.");return this.children[this.numChildren]=e,this.type=new rr(new oa(t,e.type,!0)),this.numChildren-1}},{key:"clear",value:function(){return this._run.clear(),jt(ae(n.prototype),"clear",this).call(this)}},{key:"_flushPending",value:function(e){var t,n,r=this._run,i=this._offsets,a=this._setValue,o=0,u=O(e);try{for(u.s();!(n=u.n()).done;){var s=Object(P.a)(n.value,2);o=s[0],void 0===(t=s[1])?i.set(o,0):(i.set(o,t.length),a(this,o,r.bind(t)))}}catch(l){u.e(l)}finally{u.f()}}}]),n}(Tr),ca=function(e){ie(n,e);var t=ce(n);function n(){var e;return C(this,n),(e=t.apply(this,arguments))._run=new Ti,e}return M(n,[{key:"setValue",value:function(e,t){jt(ae(n.prototype),"setValue",this).call(this,e,this._run.bind(t))}},{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0";if(this.numChildren>0)throw new Error("FixedSizeListBuilder can only have one child.");var n=this.children.push(e);return this.type=new ur(this.type.listSize,new oa(t,e.type,!0)),n}},{key:"clear",value:function(){return this._run.clear(),jt(ae(n.prototype),"clear",this).call(this)}}]),n}(xr),fa=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"set",value:function(e,t){return jt(ae(n.prototype),"set",this).call(this,e,t)}},{key:"setValue",value:function(e,t){t=t instanceof Map?t:new Map(Object.entries(t));var n=this._pending||(this._pending=new Map),r=n.get(e);r&&(this._pendingLength-=r.size),this._pendingLength+=t.size,n.set(e,t)}},{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"".concat(this.numChildren);if(this.numChildren>0)throw new Error("ListBuilder can only have one child.");return this.children[this.numChildren]=e,this.type=new sr(new oa(t,e.type,!0),this.type.keysSorted),this.numChildren-1}},{key:"_flushPending",value:function(e){var t=this,n=this._offsets,r=this._setValue;e.forEach((function(e,i){void 0===e?n.set(i,0):(n.set(i,e.size),r(t,i,e))}))}}]),n}(Tr),da=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"".concat(this.numChildren),n=this.children.push(e);return this.type=new ir([].concat(vn(this.type.children),[new oa(t,e.type,!0)])),n}}]),n}(xr),ha=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._typeIds=new gr(new Int8Array(0),1),"function"===typeof e.valueToChildTypeId&&(r._valueToChildTypeId=e.valueToChildTypeId),r}return M(n,[{key:"typeIdToChildIndex",get:function(){return this.type.typeIdToChildIndex}},{key:"append",value:function(e,t){return this.set(this.length,e,t)}},{key:"set",value:function(e,t,n){return void 0===n&&(n=this._valueToChildTypeId(this,t,e)),this.setValid(e,this.isValid(t))&&this.setValue(e,t,n),this}},{key:"setValue",value:function(e,t,r){this._typeIds.set(e,r),jt(ae(n.prototype),"setValue",this).call(this,e,t)}},{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"".concat(this.children.length),n=this.children.push(e),r=this.type,i=r.children,a=r.mode,o=r.typeIds,u=[].concat(vn(i),[new oa(t,e.type)]);return this.type=new ar(a,[].concat(vn(o),[n]),u),n}},{key:"_valueToChildTypeId",value:function(e,t,n){throw new Error("Cannot map UnionBuilder value to child typeId. Pass the `childTypeId` as the second argument to unionBuilder.append(), or supply a `valueToChildTypeId` function as part of the UnionBuilder constructor options.")}}]),n}(xr),pa=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ha),ya=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._offsets=new gr(new Int32Array(0)),r}return M(n,[{key:"setValue",value:function(e,t,r){var i=this.type.typeIdToChildIndex[r];return this._offsets.set(e,this.getChildAt(i).length),jt(ae(n.prototype),"setValue",this).call(this,e,t,r)}}]),n}(ha),va=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(bn),ba=function(e,t,n){e[t]=n%4294967296|0,e[t+1]=n/4294967296|0},ma=function(e,t,n,r){var i=t[n],a=t[n+1];null!=i&&null!=a&&e.set(r.subarray(0,a-i),i)},ga=function(e,t,n){!function(e,t,n){e[t]=n/864e5|0}(e.values,t,n.valueOf())},ka=function(e,t,n){var r=e.values;ba(r,2*t,n.valueOf())},wa=function(e,t,n){var r=e.stride;e.values[r*t]=n},_a=function(e,t,n){var r=e.stride;e.values[r*t]=Pr(n)},xa=function(e,t,n){switch(typeof n){case"bigint":e.values64[t]=n;break;case"number":e.values[t*e.stride]=n;break;default:var r=n,i=e.stride,a=Ye(e.ArrayType,r);e.values.set(a.subarray(0,i),i*t)}},Sa=function(e,t,n){var r=e.values;return ba(r,2*t,n/1e3)},Ta=function(e,t,n){var r=e.values;return ba(r,2*t,n)},Ia=function(e,t,n){return function(e,t,n){e[t]=1e3*n%4294967296|0,e[t+1]=1e3*n/4294967296|0}(e.values,2*t,n)},Ea=function(e,t,n){return function(e,t,n){e[t]=1e6*n%4294967296|0,e[t+1]=1e6*n/4294967296|0}(e.values,2*t,n)},Aa=function(e,t,n){e.values[e.stride*t]=n},Fa=function(e,t,n){e.values[e.stride*t]=n},Oa=function(e,t,n){e.values.set(n.subarray(0,2),2*t)},Ba=function(e,t,n){e.values.set(n.subarray(0,2),2*t)},Da=function(e,t,n){var r=e.typeIdToChildIndex[e.typeIds[t]],i=e.getChildAt(r);i&&i.set(e.valueOffsets[t],n)},Ca=function(e,t,n){var r=e.typeIdToChildIndex[e.typeIds[t]],i=e.getChildAt(r);i&&i.set(t,n)},La=function(e,t,n){e.values.set(n.subarray(0,2),2*t)},Ma=function(e,t,n){e.values[t]=12*n[0]+n[1]%12};va.prototype.visitBool=function(e,t,n){var r=e.offset,i=e.values,a=r+t;n?i[a>>3]|=1<<a%8:i[a>>3]&=~(1<<a%8)},va.prototype.visitInt=function(e,t,n){e.type.bitWidth<64?wa(e,t,n):xa(e,t,n)},va.prototype.visitInt8=wa,va.prototype.visitInt16=wa,va.prototype.visitInt32=wa,va.prototype.visitInt64=xa,va.prototype.visitUint8=wa,va.prototype.visitUint16=wa,va.prototype.visitUint32=wa,va.prototype.visitUint64=xa,va.prototype.visitFloat=function(e,t,n){e.type.precision!==Jt.HALF?wa(e,t,n):_a(e,t,n)},va.prototype.visitFloat16=_a,va.prototype.visitFloat32=wa,va.prototype.visitFloat64=wa,va.prototype.visitUtf8=function(e,t,n){var r=e.values,i=e.valueOffsets;ma(r,i,t,ne(n))},va.prototype.visitBinary=function(e,t,n){var r=e.values,i=e.valueOffsets;return ma(r,i,t,n)},va.prototype.visitFixedSizeBinary=function(e,t,n){var r=e.stride;e.values.set(n.subarray(0,r),r*t)},va.prototype.visitDate=function(e,t,n){e.type.unit===Xt.DAY?ga(e,t,n):ka(e,t,n)},va.prototype.visitDateDay=ga,va.prototype.visitDateMillisecond=ka,va.prototype.visitTimestamp=function(e,t,n){switch(e.type.unit){case Zt.SECOND:return Sa(e,t,n);case Zt.MILLISECOND:return Ta(e,t,n);case Zt.MICROSECOND:return Ia(e,t,n);case Zt.NANOSECOND:return Ea(e,t,n)}},va.prototype.visitTimestampSecond=Sa,va.prototype.visitTimestampMillisecond=Ta,va.prototype.visitTimestampMicrosecond=Ia,va.prototype.visitTimestampNanosecond=Ea,va.prototype.visitTime=function(e,t,n){switch(e.type.unit){case Zt.SECOND:return Aa(e,t,n);case Zt.MILLISECOND:return Fa(e,t,n);case Zt.MICROSECOND:return Oa(e,t,n);case Zt.NANOSECOND:return Ba(e,t,n)}},va.prototype.visitTimeSecond=Aa,va.prototype.visitTimeMillisecond=Fa,va.prototype.visitTimeMicrosecond=Oa,va.prototype.visitTimeNanosecond=Ba,va.prototype.visitDecimal=function(e,t,n){e.values.set(n.subarray(0,4),4*t)},va.prototype.visitList=function(e,t,n){for(var r=e.getChildAt(0),i=e.valueOffsets,a=-1,o=i[t],u=i[t+1];o<u;)r.set(o++,n.get(++a))},va.prototype.visitStruct=function(e,t,n){var r,i,a=n instanceof Map?(r=t,i=n,function(e,t,n){return e&&e.set(r,i.get(t.name))}):n instanceof Qt?function(e,t){return function(n,r,i){return n&&n.set(e,t.get(i))}}(t,n):Array.isArray(n)?function(e,t){return function(n,r,i){return n&&n.set(e,t[i])}}(t,n):function(e,t){return function(n,r,i){return n&&n.set(e,t[r.name])}}(t,n);e.type.children.forEach((function(t,n){return a(e.getChildAt(n),t,n)}))},va.prototype.visitUnion=function(e,t,n){e.type.mode===en.Dense?Da(e,t,n):Ca(e,t,n)},va.prototype.visitDenseUnion=Da,va.prototype.visitSparseUnion=Ca,va.prototype.visitDictionary=function(e,t,n){var r=e.getKey(t);null!==r&&e.setValue(r,n)},va.prototype.visitInterval=function(e,t,n){e.type.unit===tn.DAY_TIME?La(e,t,n):Ma(e,t,n)},va.prototype.visitIntervalDayTime=La,va.prototype.visitIntervalYearMonth=Ma,va.prototype.visitFixedSizeList=function(e,t,n){for(var r=e.getChildAt(0),i=e.stride,a=-1,o=t*i;++a<i;)r.set(o+a,n.get(a))},va.prototype.visitMap=function(e,t,n){for(var r=e.getChildAt(0),i=e.valueOffsets,a=n instanceof Map?vn(n):Object.entries(n),o=-1,u=i[t],s=i[t+1];u<s;)r.set(u++,a[++o])};var Na,Pa=new va,Ua=new(function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"visitNull",value:function(){return Er}},{key:"visitBool",value:function(){return Ir}},{key:"visitInt",value:function(){return ei}},{key:"visitInt8",value:function(){return ti}},{key:"visitInt16",value:function(){return ni}},{key:"visitInt32",value:function(){return ri}},{key:"visitInt64",value:function(){return ii}},{key:"visitUint8",value:function(){return ai}},{key:"visitUint16",value:function(){return oi}},{key:"visitUint32",value:function(){return ui}},{key:"visitUint64",value:function(){return si}},{key:"visitFloat",value:function(){return Ur}},{key:"visitFloat16",value:function(){return Rr}},{key:"visitFloat32",value:function(){return zr}},{key:"visitFloat64",value:function(){return jr}},{key:"visitUtf8",value:function(){return Si}},{key:"visitBinary",value:function(){return xi}},{key:"visitFixedSizeBinary",value:function(){return Cr}},{key:"visitDate",value:function(){return Ar}},{key:"visitDateDay",value:function(){return Fr}},{key:"visitDateMillisecond",value:function(){return Or}},{key:"visitTimestamp",value:function(){return yi}},{key:"visitTimestampSecond",value:function(){return vi}},{key:"visitTimestampMillisecond",value:function(){return bi}},{key:"visitTimestampMicrosecond",value:function(){return mi}},{key:"visitTimestampNanosecond",value:function(){return gi}},{key:"visitTime",value:function(){return ci}},{key:"visitTimeSecond",value:function(){return fi}},{key:"visitTimeMillisecond",value:function(){return di}},{key:"visitTimeMicrosecond",value:function(){return hi}},{key:"visitTimeNanosecond",value:function(){return pi}},{key:"visitDecimal",value:function(){return Br}},{key:"visitList",value:function(){return la}},{key:"visitStruct",value:function(){return da}},{key:"visitUnion",value:function(){return ha}},{key:"visitDenseUnion",value:function(){return ya}},{key:"visitSparseUnion",value:function(){return pa}},{key:"visitDictionary",value:function(){return Dr}},{key:"visitInterval",value:function(){return ki}},{key:"visitIntervalDayTime",value:function(){return wi}},{key:"visitIntervalYearMonth",value:function(){return _i}},{key:"visitFixedSizeList",value:function(){return ca}},{key:"visitMap",value:function(){return fa}}]),n}(bn));xr.new=function e(t){var n=t.type,r=new(Ua.getVisitFn(n)())(t);if(n.children&&n.children.length>0){var i=t.children||[],a={nullValues:t.nullValues},o=Array.isArray(i)?function(e,t){return i[t]||a}:function(e){var t=e.name;return i[t]||a};n.children.forEach((function(t,n){var i=t.type,a=o(t,n);r.children.push(e(zt(zt({},a),{},{type:i})))}))}return r},Object.keys(Gt).map((function(e){return Gt[e]})).filter((function(e){return"number"===typeof e&&e!==Gt.NONE})).forEach((function(e){Ua.visit(e).prototype._setValue=Pa.getVisitFn(e)})),Si.prototype._setValue=Pa.visitBinary,function(e){!function(t){!function(t){!function(t){var n=function(){function t(){C(this,t),this.bb=null,this.bb_pos=0}return M(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"version",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):Vt.apache.arrow.flatbuf.MetadataVersion.V1}},{key:"schema",value:function(e){var t=this.bb.__offset(this.bb_pos,6);return t?(e||new Vt.apache.arrow.flatbuf.Schema).__init(this.bb.__indirect(this.bb_pos+t),this.bb):null}},{key:"dictionaries",value:function(t,n){var r=this.bb.__offset(this.bb_pos,8);return r?(n||new e.apache.arrow.flatbuf.Block).__init(this.bb.__vector(this.bb_pos+r)+24*t,this.bb):null}},{key:"dictionariesLength",value:function(){var e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"recordBatches",value:function(t,n){var r=this.bb.__offset(this.bb_pos,10);return r?(n||new e.apache.arrow.flatbuf.Block).__init(this.bb.__vector(this.bb_pos+r)+24*t,this.bb):null}},{key:"recordBatchesLength",value:function(){var e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsFooter",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startFooter",value:function(e){e.startObject(4)}},{key:"addVersion",value:function(e,t){e.addFieldInt16(0,t,Vt.apache.arrow.flatbuf.MetadataVersion.V1)}},{key:"addSchema",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"addDictionaries",value:function(e,t){e.addFieldOffset(2,t,0)}},{key:"startDictionariesVector",value:function(e,t){e.startVector(24,t,8)}},{key:"addRecordBatches",value:function(e,t){e.addFieldOffset(3,t,0)}},{key:"startRecordBatchesVector",value:function(e,t){e.startVector(24,t,8)}},{key:"endFooter",value:function(e){return e.endObject()}},{key:"finishFooterBuffer",value:function(e,t){e.finish(t)}},{key:"createFooter",value:function(e,n,r,i,a){return t.startFooter(e),t.addVersion(e,n),t.addSchema(e,r),t.addDictionaries(e,i),t.addRecordBatches(e,a),t.endFooter(e)}}]),t}();t.Footer=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Na||(Na={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){C(this,e),this.bb=null,this.bb_pos=0}return M(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"offset",value:function(){return this.bb.readInt64(this.bb_pos)}},{key:"metaDataLength",value:function(){return this.bb.readInt32(this.bb_pos+8)}},{key:"bodyLength",value:function(){return this.bb.readInt64(this.bb_pos+16)}}],[{key:"createBlock",value:function(e,t,n,r){return e.prep(8,24),e.writeInt64(r),e.pad(4),e.writeInt32(n),e.writeInt64(t),e.offset()}}]),e}();e.Block=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Na||(Na={}));var Ra=H.Long,za=H.Builder,ja=H.ByteBuffer,Va=Na.apache.arrow.flatbuf.Block,Wa=Na.apache.arrow.flatbuf.Footer,Ha=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rn.V4,r=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0;C(this,e),this.schema=t,this.version=n,r&&(this._recordBatches=r),i&&(this._dictionaryBatches=i)}return M(e,[{key:"numRecordBatches",get:function(){return this._recordBatches.length}},{key:"numDictionaries",get:function(){return this._dictionaryBatches.length}},{key:"recordBatches",value:N.mark((function e(){var t,n,r;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=-1,r=this.numRecordBatches;case 1:if(!(++n<r)){e.next=7;break}if(!(t=this.getRecordBatch(n))){e.next=5;break}return e.next=5,t;case 5:e.next=1;break;case 7:case"end":return e.stop()}}),e,this)}))},{key:"dictionaryBatches",value:N.mark((function e(){var t,n,r;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=-1,r=this.numDictionaries;case 1:if(!(++n<r)){e.next=7;break}if(!(t=this.getDictionaryBatch(n))){e.next=5;break}return e.next=5,t;case 5:e.next=1;break;case 7:case"end":return e.stop()}}),e,this)}))},{key:"getRecordBatch",value:function(e){return e>=0&&e<this.numRecordBatches&&this._recordBatches[e]||null}},{key:"getDictionaryBatch",value:function(e){return e>=0&&e<this.numDictionaries&&this._dictionaryBatches[e]||null}}],[{key:"decode",value:function(e){e=new ja(qe(e));var t=Wa.getRootAsFooter(e),n=aa.decode(t.schema());return new Ya(n,t)}},{key:"encode",value:function(e){var t=new za,n=aa.encode(t,e.schema);Wa.startRecordBatchesVector(t,e.numRecordBatches),vn(e.recordBatches()).slice().reverse().forEach((function(e){return $a.encode(t,e)}));var r=t.endVector();Wa.startDictionariesVector(t,e.numDictionaries),vn(e.dictionaryBatches()).slice().reverse().forEach((function(e){return $a.encode(t,e)}));var i=t.endVector();return Wa.startFooter(t),Wa.addSchema(t,n),Wa.addVersion(t,rn.V4),Wa.addRecordBatches(t,r),Wa.addDictionaries(t,i),Wa.finishFooterBuffer(t,Wa.endFooter(t)),t.asUint8Array()}}]),e}(),Ya=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this,e,r.version()))._footer=r,i}return M(n,[{key:"numRecordBatches",get:function(){return this._footer.recordBatchesLength()}},{key:"numDictionaries",get:function(){return this._footer.dictionariesLength()}},{key:"getRecordBatch",value:function(e){if(e>=0&&e<this.numRecordBatches){var t=this._footer.recordBatches(e);if(t)return $a.decode(t)}return null}},{key:"getDictionaryBatch",value:function(e){if(e>=0&&e<this.numDictionaries){var t=this._footer.dictionaries(e);if(t)return $a.decode(t)}return null}}]),n}(Ha),$a=function(){function e(t,n,r){C(this,e),this.metaDataLength=t,this.offset="number"===typeof r?r:r.low,this.bodyLength="number"===typeof n?n:n.low}return M(e,null,[{key:"decode",value:function(t){return new e(t.metaDataLength(),t.bodyLength(),t.offset())}},{key:"encode",value:function(e,t){var n=t.metaDataLength,r=new Ra(t.offset,0),i=new Ra(t.bodyLength,0);return Va.createBlock(e,r,n,i)}}]),e}(),Ka=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"write",value:function(e){if((e=qe(e)).byteLength>0)return jt(ae(n.prototype),"write",this).call(this,e)}},{key:"toString",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e?te(this.toUint8Array(!0)):this.toUint8Array(!1).then(te)}},{key:"toUint8Array",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return t?He(this._values)[0]:D(N.mark((function t(){var n,r,i,a,o,u,s,l,c;return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n=[],r=0,i=!0,a=!1,t.prev=3,u=V(e);case 5:return t.next=7,u.next();case 7:return s=t.sent,i=s.done,t.next=11,s.value;case 11:if(l=t.sent,i){t.next=19;break}c=l,n.push(c),r+=c.byteLength;case 16:i=!0,t.next=5;break;case 19:t.next=25;break;case 21:t.prev=21,t.t0=t.catch(3),a=!0,o=t.t0;case 25:if(t.prev=25,t.prev=26,i||null==u.return){t.next=30;break}return t.next=30,u.return();case 30:if(t.prev=30,!a){t.next=33;break}throw o;case 33:return t.finish(30);case 34:return t.finish(25);case 35:return t.abrupt("return",He(n,r)[0]);case 36:case"end":return t.stop()}}),t,null,[[3,21,25,35],[26,,30,34]])})))()}}]),n}(pe),Qa=function(){function e(t){C(this,e),t&&(this.source=new qa(At.fromIterable(t)))}return M(e,[{key:Symbol.iterator,value:function(){return this}},{key:"next",value:function(e){return this.source.next(e)}},{key:"throw",value:function(e){return this.source.throw(e)}},{key:"return",value:function(e){return this.source.return(e)}},{key:"peek",value:function(e){return this.source.peek(e)}},{key:"read",value:function(e){return this.source.read(e)}}]),e}(),Ga=function(){function e(t){C(this,e),t instanceof e?this.source=t.source:t instanceof Ka?this.source=new Xa(At.fromAsyncIterable(t)):Re(t)?this.source=new Xa(At.fromNodeStream(t)):Pe(t)?this.source=new Xa(At.fromDOMStream(t)):Me(t)?this.source=new Xa(At.fromDOMStream(t.body)):Oe(t)?this.source=new Xa(At.fromIterable(t)):(Fe(t)||Be(t))&&(this.source=new Xa(At.fromAsyncIterable(t)))}return M(e,[{key:Symbol.asyncIterator,value:function(){return this}},{key:"next",value:function(e){return this.source.next(e)}},{key:"throw",value:function(e){return this.source.throw(e)}},{key:"return",value:function(e){return this.source.return(e)}},{key:"closed",get:function(){return this.source.closed}},{key:"cancel",value:function(e){return this.source.cancel(e)}},{key:"peek",value:function(e){return this.source.peek(e)}},{key:"read",value:function(e){return this.source.read(e)}}]),e}(),qa=function(){function e(t){C(this,e),this.source=t}return M(e,[{key:"cancel",value:function(e){this.return(e)}},{key:"peek",value:function(e){return this.next(e,"peek").value}},{key:"read",value:function(e){return this.next(e,"read").value}},{key:"next",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"read";return this.source.next({cmd:t,size:e})}},{key:"throw",value:function(e){return Object.create(this.source.throw&&this.source.throw(e)||fe)}},{key:"return",value:function(e){return Object.create(this.source.return&&this.source.return(e)||fe)}}]),e}(),Xa=function(){function e(t){var n=this;C(this,e),this.source=t,this._closedPromise=new Promise((function(e){return n._closedPromiseResolve=e}))}return M(e,[{key:"cancel",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.return(t);case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"closed",get:function(){return this._closedPromise}},{key:"read",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next(t,"read");case 2:return e.abrupt("return",e.sent.value);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"peek",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next(t,"peek");case 2:return e.abrupt("return",e.sent.value);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"next",value:function(){var e=D(N.mark((function e(t){var n,r=arguments;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:"read",e.next=3,this.source.next({cmd:n,size:t});case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"throw",value:function(){var e=D(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t1=this.source.throw,!e.t1){e.next=5;break}return e.next=4,this.source.throw(t);case 4:e.t1=e.sent;case 5:if(e.t0=e.t1,e.t0){e.next=8;break}e.t0=fe;case 8:return n=e.t0,this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,e.abrupt("return",Object.create(n));case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=D(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t1=this.source.return,!e.t1){e.next=5;break}return e.next=4,this.source.return(t);case 4:e.t1=e.sent;case 5:if(e.t0=e.t1,e.t0){e.next=8;break}e.t0=fe;case 8:return n=e.t0,this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,e.abrupt("return",Object.create(n));case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),Za=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this)).position=0,i.buffer=qe(e),i.size="undefined"===typeof r?i.buffer.byteLength:r,i}return M(n,[{key:"readInt32",value:function(e){var t=this.readAt(e,4),n=t.buffer,r=t.byteOffset;return new DataView(n,r).getInt32(0,!0)}},{key:"seek",value:function(e){return this.position=Math.min(e,this.size),e<this.size}},{key:"read",value:function(e){var t=this.buffer,n=this.size,r=this.position;return t&&r<n?("number"!==typeof e&&(e=1/0),this.position=Math.min(n,r+Math.min(n-r,e)),t.subarray(r,this.position)):null}},{key:"readAt",value:function(e,t){var n=this.buffer,r=Math.min(this.size,e+t);return n?n.subarray(e,r):new Uint8Array(t)}},{key:"close",value:function(){this.buffer&&(this.buffer=null)}},{key:"throw",value:function(e){return this.close(),{done:!0,value:e}}},{key:"return",value:function(e){return this.close(),{done:!0,value:e}}}]),n}(Qa),Ja=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this)).position=0,i._handle=e,"number"===typeof r?i.size=r:i._pending=D(N.mark((function t(){return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.stat();case 2:i.size=t.sent.size,delete i._pending;case 4:case"end":return t.stop()}}),t)})))(),i}return M(n,[{key:"readInt32",value:function(){var e=D(N.mark((function e(t){var n,r,i;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.readAt(t,4);case 2:return n=e.sent,r=n.buffer,i=n.byteOffset,e.abrupt("return",new DataView(r,i).getInt32(0,!0));case 6:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"seek",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=this._pending,!e.t0){e.next=4;break}return e.next=4,this._pending;case 4:return this.position=Math.min(t,this.size),e.abrupt("return",t<this.size);case 6:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"read",value:function(){var e=D(N.mark((function e(t){var n,r,i,a,o,u,s,l,c;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=this._pending,!e.t0){e.next=4;break}return e.next=4,this._pending;case 4:if(n=this._handle,r=this.size,i=this.position,!(n&&i<r)){e.next=18;break}"number"!==typeof t&&(t=1/0),a=i,o=0,u=0,s=Math.min(r,a+Math.min(r-a,t)),l=new Uint8Array(Math.max(0,(this.position=s)-a));case 10:if(!((a+=u)<s&&(o+=u)<l.byteLength)){e.next=17;break}return e.next=13,n.read(l,o,l.byteLength-o,a);case 13:c=e.sent,u=c.bytesRead,e.next=10;break;case 17:return e.abrupt("return",l);case 18:return e.abrupt("return",null);case 19:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"readAt",value:function(){var e=D(N.mark((function e(t,n){var r,i,a,o;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=this._pending,!e.t0){e.next=4;break}return e.next=4,this._pending;case 4:if(r=this._handle,i=this.size,!(r&&t+n<i)){e.next=11;break}return a=Math.min(i,t+n),o=new Uint8Array(a-t),e.next=10,r.read(o,0,n,t);case 10:return e.abrupt("return",e.sent.buffer);case 11:return e.abrupt("return",new Uint8Array(n));case 12:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"close",value:function(){var e=D(N.mark((function e(){var t;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this._handle,this._handle=null,e.t0=t,!e.t0){e.next=6;break}return e.next=6,t.close();case 6:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"throw",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.close();case 2:return e.abrupt("return",{done:!0,value:t});case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.close();case 2:return e.abrupt("return",{done:!0,value:t});case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(Ga);function eo(e){return e<0&&(e=4294967295+e+1),"0x".concat(e.toString(16))}var to=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8],no=function(){function e(t){C(this,e),this.buffer=t}return M(e,[{key:"high",value:function(){return this.buffer[1]}},{key:"low",value:function(){return this.buffer[0]}},{key:"_times",value:function(e){var t=new Uint32Array([this.buffer[1]>>>16,65535&this.buffer[1],this.buffer[0]>>>16,65535&this.buffer[0]]),n=new Uint32Array([e.buffer[1]>>>16,65535&e.buffer[1],e.buffer[0]>>>16,65535&e.buffer[0]]),r=t[3]*n[3];this.buffer[0]=65535&r;var i=r>>>16;return i+=r=t[2]*n[3],i+=r=t[3]*n[2]>>>0,this.buffer[0]+=i<<16,this.buffer[1]=i>>>0<r?65536:0,this.buffer[1]+=i>>>16,this.buffer[1]+=t[1]*n[3]+t[2]*n[2]+t[3]*n[1],this.buffer[1]+=t[0]*n[3]+t[1]*n[2]+t[2]*n[1]+t[3]*n[0]<<16,this}},{key:"_plus",value:function(e){var t=this.buffer[0]+e.buffer[0]>>>0;this.buffer[1]+=e.buffer[1],t<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=t}},{key:"lessThan",value:function(e){return this.buffer[1]<e.buffer[1]||this.buffer[1]===e.buffer[1]&&this.buffer[0]<e.buffer[0]}},{key:"equals",value:function(e){return this.buffer[1]===e.buffer[1]&&this.buffer[0]==e.buffer[0]}},{key:"greaterThan",value:function(e){return e.lessThan(this)}},{key:"hex",value:function(){return"".concat(eo(this.buffer[1])," ").concat(eo(this.buffer[0]))}}]),e}(),ro=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"times",value:function(e){return this._times(e),this}},{key:"plus",value:function(e){return this._plus(e),this}}],[{key:"from",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return n.fromString("string"===typeof e?e:e.toString(),t)}},{key:"fromNumber",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return n.fromString(e.toString(),t)}},{key:"fromString",value:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2),r=e.length,i=new n(t),a=0;a<r;){var o=8<r-a?8:r-a,u=new n(new Uint32Array([parseInt(e.substr(a,o),10),0])),s=new n(new Uint32Array([to[o],0]));i.times(s),i.plus(u),a+=o}return i}},{key:"convertArray",value:function(e){for(var t=new Uint32Array(2*e.length),r=-1,i=e.length;++r<i;)n.from(e[r],new Uint32Array(t.buffer,t.byteOffset+2*r*4,2));return t}},{key:"multiply",value:function(e,t){return new n(new Uint32Array(e.buffer)).times(t)}},{key:"add",value:function(e,t){return new n(new Uint32Array(e.buffer)).plus(t)}}]),n}(no),io=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"negate",value:function(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],0==this.buffer[0]&&++this.buffer[1],this}},{key:"times",value:function(e){return this._times(e),this}},{key:"plus",value:function(e){return this._plus(e),this}},{key:"lessThan",value:function(e){var t=this.buffer[1]<<0,n=e.buffer[1]<<0;return t<n||t===n&&this.buffer[0]<e.buffer[0]}}],[{key:"from",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return n.fromString("string"===typeof e?e:e.toString(),t)}},{key:"fromNumber",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return n.fromString(e.toString(),t)}},{key:"fromString",value:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2),r=e.startsWith("-"),i=e.length,a=new n(t),o=r?1:0;o<i;){var u=8<i-o?8:i-o,s=new n(new Uint32Array([parseInt(e.substr(o,u),10),0])),l=new n(new Uint32Array([to[u],0]));a.times(l),a.plus(s),o+=u}return r?a.negate():a}},{key:"convertArray",value:function(e){for(var t=new Uint32Array(2*e.length),r=-1,i=e.length;++r<i;)n.from(e[r],new Uint32Array(t.buffer,t.byteOffset+2*r*4,2));return t}},{key:"multiply",value:function(e,t){return new n(new Uint32Array(e.buffer)).times(t)}},{key:"add",value:function(e,t){return new n(new Uint32Array(e.buffer)).plus(t)}}]),n}(no),ao=function(){function e(t){C(this,e),this.buffer=t}return M(e,[{key:"high",value:function(){return new io(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}},{key:"low",value:function(){return new io(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}},{key:"negate",value:function(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],0==this.buffer[0]&&++this.buffer[1],0==this.buffer[1]&&++this.buffer[2],0==this.buffer[2]&&++this.buffer[3],this}},{key:"times",value:function(e){var t=new ro(new Uint32Array([this.buffer[3],0])),n=new ro(new Uint32Array([this.buffer[2],0])),r=new ro(new Uint32Array([this.buffer[1],0])),i=new ro(new Uint32Array([this.buffer[0],0])),a=new ro(new Uint32Array([e.buffer[3],0])),o=new ro(new Uint32Array([e.buffer[2],0])),u=new ro(new Uint32Array([e.buffer[1],0])),s=new ro(new Uint32Array([e.buffer[0],0])),l=ro.multiply(i,s);this.buffer[0]=l.low();var c=new ro(new Uint32Array([l.high(),0]));return l=ro.multiply(r,s),c.plus(l),l=ro.multiply(i,u),c.plus(l),this.buffer[1]=c.low(),this.buffer[3]=c.lessThan(l)?1:0,this.buffer[2]=c.high(),new ro(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(ro.multiply(n,s)).plus(ro.multiply(r,u)).plus(ro.multiply(i,o)),this.buffer[3]+=ro.multiply(t,s).plus(ro.multiply(n,u)).plus(ro.multiply(r,o)).plus(ro.multiply(i,a)).low(),this}},{key:"plus",value:function(e){var t=new Uint32Array(4);return t[3]=this.buffer[3]+e.buffer[3]>>>0,t[2]=this.buffer[2]+e.buffer[2]>>>0,t[1]=this.buffer[1]+e.buffer[1]>>>0,t[0]=this.buffer[0]+e.buffer[0]>>>0,t[0]<this.buffer[0]>>>0&&++t[1],t[1]<this.buffer[1]>>>0&&++t[2],t[2]<this.buffer[2]>>>0&&++t[3],this.buffer[3]=t[3],this.buffer[2]=t[2],this.buffer[1]=t[1],this.buffer[0]=t[0],this}},{key:"hex",value:function(){return"".concat(eo(this.buffer[3])," ").concat(eo(this.buffer[2])," ").concat(eo(this.buffer[1])," ").concat(eo(this.buffer[0]))}}],[{key:"multiply",value:function(t,n){return new e(new Uint32Array(t.buffer)).times(n)}},{key:"add",value:function(t,n){return new e(new Uint32Array(t.buffer)).plus(n)}},{key:"from",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return e.fromString("string"===typeof t?t:t.toString(),n)}},{key:"fromNumber",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return e.fromString(t.toString(),n)}},{key:"fromString",value:function(t){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4),r=t.startsWith("-"),i=t.length,a=new e(n),o=r?1:0;o<i;){var u=8<i-o?8:i-o,s=new e(new Uint32Array([parseInt(t.substr(o,u),10),0,0,0])),l=new e(new Uint32Array([to[u],0,0,0]));a.times(l),a.plus(s),o+=u}return r?a.negate():a}},{key:"convertArray",value:function(t){for(var n=new Uint32Array(4*t.length),r=-1,i=t.length;++r<i;)e.from(t[r],new Uint32Array(n.buffer,n.byteOffset+16*r,4));return n}}]),e}(),oo=function(e){ie(n,e);var t=ce(n);function n(e,r,i,a){var o;return C(this,n),(o=t.call(this)).nodesIndex=-1,o.buffersIndex=-1,o.bytes=e,o.nodes=r,o.buffers=i,o.dictionaries=a,o}return M(n,[{key:"visit",value:function(e){return jt(ae(n.prototype),"visit",this).call(this,e instanceof oa?e.type:e)}},{key:"visitNull",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length;return hr.Null(e,0,n)}},{key:"visitBool",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Bool(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitInt",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Int(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitFloat",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Float(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitUtf8",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Utf8(e,0,n,r,this.readNullBitmap(e,r),this.readOffsets(e),this.readData(e))}},{key:"visitBinary",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Binary(e,0,n,r,this.readNullBitmap(e,r),this.readOffsets(e),this.readData(e))}},{key:"visitFixedSizeBinary",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.FixedSizeBinary(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitDate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Date(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitTimestamp",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Timestamp(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitTime",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Time(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitDecimal",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Decimal(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitList",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.List(e,0,n,r,this.readNullBitmap(e,r),this.readOffsets(e),this.visit(e.children[0]))}},{key:"visitStruct",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Struct(e,0,n,r,this.readNullBitmap(e,r),this.visitMany(e.children))}},{key:"visitUnion",value:function(e){return e.mode===en.Sparse?this.visitSparseUnion(e):this.visitDenseUnion(e)}},{key:"visitDenseUnion",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Union(e,0,n,r,this.readNullBitmap(e,r),this.readTypeIds(e),this.readOffsets(e),this.visitMany(e.children))}},{key:"visitSparseUnion",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Union(e,0,n,r,this.readNullBitmap(e,r),this.readTypeIds(e),this.visitMany(e.children))}},{key:"visitDictionary",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Dictionary(e,0,n,r,this.readNullBitmap(e,r),this.readData(e.indices),this.readDictionary(e))}},{key:"visitInterval",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Interval(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitFixedSizeList",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.FixedSizeList(e,0,n,r,this.readNullBitmap(e,r),this.visit(e.children[0]))}},{key:"visitMap",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return hr.Map(e,0,n,r,this.readNullBitmap(e,r),this.readOffsets(e),this.visit(e.children[0]))}},{key:"nextFieldNode",value:function(){return this.nodes[++this.nodesIndex]}},{key:"nextBufferRange",value:function(){return this.buffers[++this.buffersIndex]}},{key:"readNullBitmap",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange();return t>0&&this.readData(e,n)||new Uint8Array(0)}},{key:"readOffsets",value:function(e,t){return this.readData(e,t)}},{key:"readTypeIds",value:function(e,t){return this.readData(e,t)}},{key:"readData",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange(),n=t.length,r=t.offset;return this.bytes.subarray(r,r+n)}},{key:"readDictionary",value:function(e){return this.dictionaries.get(e.id)}}]),n}(bn),uo=function(e){ie(n,e);var t=ce(n);function n(e,r,i,a){var o;return C(this,n),(o=t.call(this,new Uint8Array(0),r,i,a)).sources=e,o}return M(n,[{key:"readNullBitmap",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange(),r=n.offset;return t<=0?new Uint8Array(0):cn(this.sources[r])}},{key:"readOffsets",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange(),n=t.offset;return Ye(Uint8Array,Ye(Int32Array,this.sources[n]))}},{key:"readTypeIds",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange(),n=t.offset;return Ye(Uint8Array,Ye(e.ArrayType,this.sources[n]))}},{key:"readData",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange(),n=t.offset,r=this.sources;return Dn.isTimestamp(e)||(Dn.isInt(e)||Dn.isTime(e))&&64===e.bitWidth||Dn.isDate(e)&&e.unit===Xt.MILLISECOND?Ye(Uint8Array,io.convertArray(r[n])):Dn.isDecimal(e)?Ye(Uint8Array,ao.convertArray(r[n])):Dn.isBinary(e)||Dn.isFixedSizeBinary(e)?so(r[n]):Dn.isBool(e)?cn(r[n]):Dn.isUtf8(e)?ne(r[n].join("")):Ye(Uint8Array,Ye(e.ArrayType,r[n].map((function(e){return+e}))))}}]),n}(oo);function so(e){for(var t=e.join(""),n=new Uint8Array(t.length/2),r=0;r<t.length;r+=2)n[r>>1]=parseInt(t.substr(r,2),16);return n}var lo=H.Long,co=Vt.apache.arrow.flatbuf.Null,fo=Vt.apache.arrow.flatbuf.Int,ho=Vt.apache.arrow.flatbuf.FloatingPoint,po=Vt.apache.arrow.flatbuf.Binary,yo=Vt.apache.arrow.flatbuf.Bool,vo=Vt.apache.arrow.flatbuf.Utf8,bo=Vt.apache.arrow.flatbuf.Decimal,mo=Vt.apache.arrow.flatbuf.Date,go=Vt.apache.arrow.flatbuf.Time,ko=Vt.apache.arrow.flatbuf.Timestamp,wo=Vt.apache.arrow.flatbuf.Interval,_o=Vt.apache.arrow.flatbuf.List,xo=Vt.apache.arrow.flatbuf.Struct_,So=Vt.apache.arrow.flatbuf.Union,To=Vt.apache.arrow.flatbuf.DictionaryEncoding,Io=Vt.apache.arrow.flatbuf.FixedSizeBinary,Eo=Vt.apache.arrow.flatbuf.FixedSizeList,Ao=Vt.apache.arrow.flatbuf.Map,Fo=new(function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"visit",value:function(e,t){return null==e||null==t?void 0:jt(ae(n.prototype),"visit",this).call(this,e,t)}},{key:"visitNull",value:function(e,t){return co.startNull(t),co.endNull(t)}},{key:"visitInt",value:function(e,t){return fo.startInt(t),fo.addBitWidth(t,e.bitWidth),fo.addIsSigned(t,e.isSigned),fo.endInt(t)}},{key:"visitFloat",value:function(e,t){return ho.startFloatingPoint(t),ho.addPrecision(t,e.precision),ho.endFloatingPoint(t)}},{key:"visitBinary",value:function(e,t){return po.startBinary(t),po.endBinary(t)}},{key:"visitBool",value:function(e,t){return yo.startBool(t),yo.endBool(t)}},{key:"visitUtf8",value:function(e,t){return vo.startUtf8(t),vo.endUtf8(t)}},{key:"visitDecimal",value:function(e,t){return bo.startDecimal(t),bo.addScale(t,e.scale),bo.addPrecision(t,e.precision),bo.endDecimal(t)}},{key:"visitDate",value:function(e,t){return mo.startDate(t),mo.addUnit(t,e.unit),mo.endDate(t)}},{key:"visitTime",value:function(e,t){return go.startTime(t),go.addUnit(t,e.unit),go.addBitWidth(t,e.bitWidth),go.endTime(t)}},{key:"visitTimestamp",value:function(e,t){var n=e.timezone&&t.createString(e.timezone)||void 0;return ko.startTimestamp(t),ko.addUnit(t,e.unit),void 0!==n&&ko.addTimezone(t,n),ko.endTimestamp(t)}},{key:"visitInterval",value:function(e,t){return wo.startInterval(t),wo.addUnit(t,e.unit),wo.endInterval(t)}},{key:"visitList",value:function(e,t){return _o.startList(t),_o.endList(t)}},{key:"visitStruct",value:function(e,t){return xo.startStruct_(t),xo.endStruct_(t)}},{key:"visitUnion",value:function(e,t){So.startTypeIdsVector(t,e.typeIds.length);var n=So.createTypeIdsVector(t,e.typeIds);return So.startUnion(t),So.addMode(t,e.mode),So.addTypeIds(t,n),So.endUnion(t)}},{key:"visitDictionary",value:function(e,t){var n=this.visit(e.indices,t);return To.startDictionaryEncoding(t),To.addId(t,new lo(e.id,0)),To.addIsOrdered(t,e.isOrdered),void 0!==n&&To.addIndexType(t,n),To.endDictionaryEncoding(t)}},{key:"visitFixedSizeBinary",value:function(e,t){return Io.startFixedSizeBinary(t),Io.addByteWidth(t,e.byteWidth),Io.endFixedSizeBinary(t)}},{key:"visitFixedSizeList",value:function(e,t){return Eo.startFixedSizeList(t),Eo.addListSize(t,e.listSize),Eo.endFixedSizeList(t)}},{key:"visitMap",value:function(e,t){return Ao.startMap(t),Ao.addKeysSorted(t,e.keysSorted),Ao.endMap(t)}}]),n}(bn));function Oo(e){return new qo(e.count,function e(t){return(t||[]).reduce((function(t,n){return[].concat(vn(t),[new Jo(n.count,(r=n.VALIDITY,(r||[]).reduce((function(e,t){return e+ +(0===t)}),0)))],vn(e(n.children)));var r}),[])}(e.columns),function e(t){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=-1,i=(t||[]).length;++r<i;){var a=t[r];a.VALIDITY&&n.push(new Zo(n.length,a.VALIDITY.length)),a.TYPE&&n.push(new Zo(n.length,a.TYPE.length)),a.OFFSET&&n.push(new Zo(n.length,a.OFFSET.length)),a.DATA&&n.push(new Zo(n.length,a.DATA.length)),n=e(a.children,n)}return n}(e.columns))}function Bo(e,t){return(e.fields||[]).filter(Boolean).map((function(e){return oa.fromJSON(e,t)}))}function Do(e,t){return(e.children||[]).filter(Boolean).map((function(e){return oa.fromJSON(e,t)}))}function Co(e){return new Map(Object.entries(e||{}))}function Lo(e){return new Ln(e.isSigned,e.bitWidth)}function Mo(e,t){var n=e.type.name;switch(n){case"NONE":case"null":return new Cn;case"binary":return new Kn;case"utf8":return new Qn;case"bool":return new Gn;case"list":return new rr((t||[])[0]);case"struct":case"struct_":return new ir(t||[])}switch(n){case"int":var r=e.type;return new Ln(r.isSigned,r.bitWidth);case"floatingpoint":var i=e.type;return new Wn(Jt[i.precision]);case"decimal":var a=e.type;return new qn(a.scale,a.precision);case"date":var o=e.type;return new Xn(Xt[o.unit]);case"time":var u=e.type;return new er(Zt[u.unit],u.bitWidth);case"timestamp":var s=e.type;return new tr(Zt[s.unit],s.timezone);case"interval":var l=e.type;return new nr(tn[l.unit]);case"union":var c=e.type;return new ar(en[c.mode],c.typeIds||[],t||[]);case"fixedsizebinary":var f=e.type;return new or(f.byteWidth);case"fixedsizelist":var d=e.type;return new ur(d.listSize,(t||[])[0]);case"map":var h=e.type;return new sr((t||[])[0],h.keysSorted)}throw new Error('Unrecognized type: "'.concat(n,'"'))}var No=H.Long,Po=H.Builder,Uo=H.ByteBuffer,Ro=Vt.apache.arrow.flatbuf.Type,zo=Vt.apache.arrow.flatbuf.Field,jo=Vt.apache.arrow.flatbuf.Schema,Vo=Vt.apache.arrow.flatbuf.Buffer,Wo=Kt.apache.arrow.flatbuf.Message,Ho=Vt.apache.arrow.flatbuf.KeyValue,Yo=Kt.apache.arrow.flatbuf.FieldNode,$o=Vt.apache.arrow.flatbuf.Endianness,Ko=Kt.apache.arrow.flatbuf.RecordBatch,Qo=Kt.apache.arrow.flatbuf.DictionaryBatch,Go=function(){function e(t,n,r,i){C(this,e),this._version=n,this._headerType=r,this.body=new Uint8Array(0),i&&(this._createHeader=function(){return i}),this._bodyLength="number"===typeof t?t:t.low}return M(e,[{key:"type",get:function(){return this.headerType}},{key:"version",get:function(){return this._version}},{key:"headerType",get:function(){return this._headerType}},{key:"bodyLength",get:function(){return this._bodyLength}},{key:"header",value:function(){return this._createHeader()}},{key:"isSchema",value:function(){return this.headerType===nn.Schema}},{key:"isRecordBatch",value:function(){return this.headerType===nn.RecordBatch}},{key:"isDictionaryBatch",value:function(){return this.headerType===nn.DictionaryBatch}}],[{key:"fromJSON",value:function(t,n){var r=new e(0,rn.V4,n);return r._createHeader=function(e,t){return function(){switch(t){case nn.Schema:return aa.fromJSON(e);case nn.RecordBatch:return qo.fromJSON(e);case nn.DictionaryBatch:return Xo.fromJSON(e)}throw new Error("Unrecognized Message type: { name: ".concat(nn[t],", type: ").concat(t," }"))}}(t,n),r}},{key:"decode",value:function(t){t=new Uo(qe(t));var n=Wo.getRootAsMessage(t),r=n.bodyLength(),i=n.version(),a=n.headerType(),o=new e(r,i,a);return o._createHeader=function(e,t){return function(){switch(t){case nn.Schema:return aa.decode(e.header(new jo));case nn.RecordBatch:return qo.decode(e.header(new Ko),e.version());case nn.DictionaryBatch:return Xo.decode(e.header(new Qo),e.version())}throw new Error("Unrecognized Message type: { name: ".concat(nn[t],", type: ").concat(t," }"))}}(n,a),o}},{key:"encode",value:function(e){var t=new Po,n=-1;return e.isSchema()?n=aa.encode(t,e.header()):e.isRecordBatch()?n=qo.encode(t,e.header()):e.isDictionaryBatch()&&(n=Xo.encode(t,e.header())),Wo.startMessage(t),Wo.addVersion(t,rn.V4),Wo.addHeader(t,n),Wo.addHeaderType(t,e.headerType),Wo.addBodyLength(t,new No(e.bodyLength,0)),Wo.finishMessageBuffer(t,Wo.endMessage(t)),t.asUint8Array()}},{key:"from",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t instanceof aa)return new e(0,rn.V4,nn.Schema,t);if(t instanceof qo)return new e(n,rn.V4,nn.RecordBatch,t);if(t instanceof Xo)return new e(n,rn.V4,nn.DictionaryBatch,t);throw new Error("Unrecognized Message header: ".concat(t))}}]),e}(),qo=function(){function e(t,n,r){C(this,e),this._nodes=n,this._buffers=r,this._length="number"===typeof t?t:t.low}return M(e,[{key:"nodes",get:function(){return this._nodes}},{key:"length",get:function(){return this._length}},{key:"buffers",get:function(){return this._buffers}}]),e}(),Xo=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];C(this,e),this._data=t,this._isDelta=r,this._id="number"===typeof n?n:n.low}return M(e,[{key:"id",get:function(){return this._id}},{key:"data",get:function(){return this._data}},{key:"isDelta",get:function(){return this._isDelta}},{key:"length",get:function(){return this.data.length}},{key:"nodes",get:function(){return this.data.nodes}},{key:"buffers",get:function(){return this.data.buffers}}]),e}(),Zo=function e(t,n){C(this,e),this.offset="number"===typeof t?t:t.low,this.length="number"===typeof n?n:n.low},Jo=function e(t,n){C(this,e),this.length="number"===typeof t?t:t.low,this.nullCount="number"===typeof n?n:n.low};function eu(e){for(var t,n=[],r=-1,i=-1,a=e.nodesLength();++r<a;)(t=e.nodes(r))&&(n[++i]=Jo.decode(t));return n}function tu(e,t){for(var n,r=[],i=-1,a=-1,o=e.buffersLength();++i<o;)(n=e.buffers(i))&&(t<rn.V4&&(n.bb_pos+=8*(i+1)),r[++a]=Zo.decode(n));return r}function nu(e,t){for(var n,r=[],i=-1,a=-1,o=e.fieldsLength();++i<o;)(n=e.fields(i))&&(r[++a]=oa.decode(n,t));return r}function ru(e,t){for(var n,r=[],i=-1,a=-1,o=e.childrenLength();++i<o;)(n=e.children(i))&&(r[++a]=oa.decode(n,t));return r}function iu(e){var t=new Map;if(e)for(var n,r,i=-1,a=0|e.customMetadataLength();++i<a;)(n=e.customMetadata(i))&&null!=(r=n.key())&&t.set(r,n.value());return t}function au(e){return new Ln(e.isSigned(),e.bitWidth())}function ou(e,t){var n=e.typeType();switch(n){case Ro.NONE:case Ro.Null:return new Cn;case Ro.Binary:return new Kn;case Ro.Utf8:return new Qn;case Ro.Bool:return new Gn;case Ro.List:return new rr((t||[])[0]);case Ro.Struct_:return new ir(t||[])}switch(n){case Ro.Int:var r=e.type(new Vt.apache.arrow.flatbuf.Int);return new Ln(r.isSigned(),r.bitWidth());case Ro.FloatingPoint:var i=e.type(new Vt.apache.arrow.flatbuf.FloatingPoint);return new Wn(i.precision());case Ro.Decimal:var a=e.type(new Vt.apache.arrow.flatbuf.Decimal);return new qn(a.scale(),a.precision());case Ro.Date:var o=e.type(new Vt.apache.arrow.flatbuf.Date);return new Xn(o.unit());case Ro.Time:var u=e.type(new Vt.apache.arrow.flatbuf.Time);return new er(u.unit(),u.bitWidth());case Ro.Timestamp:var s=e.type(new Vt.apache.arrow.flatbuf.Timestamp);return new tr(s.unit(),s.timezone());case Ro.Interval:var l=e.type(new Vt.apache.arrow.flatbuf.Interval);return new nr(l.unit());case Ro.Union:var c=e.type(new Vt.apache.arrow.flatbuf.Union);return new ar(c.mode(),c.typeIdsArray()||[],t||[]);case Ro.FixedSizeBinary:var f=e.type(new Vt.apache.arrow.flatbuf.FixedSizeBinary);return new or(f.byteWidth());case Ro.FixedSizeList:var d=e.type(new Vt.apache.arrow.flatbuf.FixedSizeList);return new ur(d.listSize(),(t||[])[0]);case Ro.Map:var h=e.type(new Vt.apache.arrow.flatbuf.Map);return new sr((t||[])[0],h.keysSorted())}throw new Error('Unrecognized type: "'.concat(Ro[n],'" (').concat(n,")"))}oa.encode=function(e,t){var n=-1,r=-1,i=-1,a=t.type,o=t.typeId;Dn.isDictionary(a)?(o=a.dictionary.typeId,i=Fo.visit(a,e),r=Fo.visit(a.dictionary,e)):r=Fo.visit(a,e);var u=(a.children||[]).map((function(t){return oa.encode(e,t)})),s=zo.createChildrenVector(e,u),l=t.metadata&&t.metadata.size>0?zo.createCustomMetadataVector(e,vn(t.metadata).map((function(t){var n=Object(P.a)(t,2),r=n[0],i=n[1],a=e.createString("".concat(r)),o=e.createString("".concat(i));return Ho.startKeyValue(e),Ho.addKey(e,a),Ho.addValue(e,o),Ho.endKeyValue(e)}))):-1;t.name&&(n=e.createString(t.name));zo.startField(e),zo.addType(e,r),zo.addTypeType(e,o),zo.addChildren(e,s),zo.addNullable(e,!!t.nullable),-1!==n&&zo.addName(e,n);-1!==i&&zo.addDictionary(e,i);-1!==l&&zo.addCustomMetadata(e,l);return zo.endField(e)},oa.decode=function(e,t){var n,r,i,a,o,u;t&&(u=e.dictionary())?t.has(n=u.id().low)?(a=(a=u.indexType())?au(a):new Pn,o=new fr(t.get(n),a,n,u.isOrdered()),r=new oa(e.name(),o,e.nullable(),iu(e))):(a=(a=u.indexType())?au(a):new Pn,t.set(n,i=ou(e,ru(e,t))),o=new fr(i,a,n,u.isOrdered()),r=new oa(e.name(),o,e.nullable(),iu(e))):(i=ou(e,ru(e,t)),r=new oa(e.name(),i,e.nullable(),iu(e)));return r||null},oa.fromJSON=function(e,t){var n,r,i,a,o,u;return t&&(a=e.dictionary)?t.has(n=a.id)?(r=(r=a.indexType)?Lo(r):new Pn,u=new fr(t.get(n),r,n,a.isOrdered),i=new oa(e.name,u,e.nullable,Co(e.customMetadata))):(r=(r=a.indexType)?Lo(r):new Pn,t.set(n,o=Mo(e,Do(e,t))),u=new fr(o,r,n,a.isOrdered),i=new oa(e.name,u,e.nullable,Co(e.customMetadata))):(o=Mo(e,Do(e,t)),i=new oa(e.name,o,e.nullable,Co(e.customMetadata))),i||null},aa.encode=function(e,t){var n=t.fields.map((function(t){return oa.encode(e,t)}));jo.startFieldsVector(e,n.length);var r=jo.createFieldsVector(e,n),i=t.metadata&&t.metadata.size>0?jo.createCustomMetadataVector(e,vn(t.metadata).map((function(t){var n=Object(P.a)(t,2),r=n[0],i=n[1],a=e.createString("".concat(r)),o=e.createString("".concat(i));return Ho.startKeyValue(e),Ho.addKey(e,a),Ho.addValue(e,o),Ho.endKeyValue(e)}))):-1;jo.startSchema(e),jo.addFields(e,r),jo.addEndianness(e,uu?$o.Little:$o.Big),-1!==i&&jo.addCustomMetadata(e,i);return jo.endSchema(e)},aa.decode=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map,n=nu(e,t);return new aa(n,iu(e),t)},aa.fromJSON=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;return new aa(Bo(e,t),Co(e.customMetadata),t)},qo.encode=function(e,t){var n=t.nodes||[],r=t.buffers||[];Ko.startNodesVector(e,n.length),n.slice().reverse().forEach((function(t){return Jo.encode(e,t)}));var i=e.endVector();Ko.startBuffersVector(e,r.length),r.slice().reverse().forEach((function(t){return Zo.encode(e,t)}));var a=e.endVector();return Ko.startRecordBatch(e),Ko.addLength(e,new No(t.length,0)),Ko.addNodes(e,i),Ko.addBuffers(e,a),Ko.endRecordBatch(e)},qo.decode=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rn.V4;return new qo(e.length(),eu(e),tu(e,t))},qo.fromJSON=Oo,Xo.encode=function(e,t){var n=qo.encode(e,t.data);return Qo.startDictionaryBatch(e),Qo.addId(e,new No(t.id,0)),Qo.addIsDelta(e,t.isDelta),Qo.addData(e,n),Qo.endDictionaryBatch(e)},Xo.decode=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rn.V4;return new Xo(qo.decode(e.data(),t),e.id(),e.isDelta())},Xo.fromJSON=function(e){return new Xo(Oo(e.data),e.id,e.isDelta)},Jo.encode=function(e,t){return Yo.createFieldNode(e,new No(t.length,0),new No(t.nullCount,0))},Jo.decode=function(e){return new Jo(e.length(),e.nullCount())},Zo.encode=function(e,t){return Vo.createBuffer(e,new No(t.offset,0),new No(t.length,0))},Zo.decode=function(e){return new Zo(e.offset(),e.length())};for(var uu=function(){var e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}(),su=H.ByteBuffer,lu=function(e){return"Expected ".concat(nn[e]," Message in stream, but was null or length 0.")},cu=function(e){return"Header pointer of flatbuffer-encoded ".concat(nn[e]," Message is null or length 0.")},fu=function(e,t){return"Expected to read ".concat(e," metadata bytes, but only read ").concat(t,".")},du=function(e,t){return"Expected to read ".concat(e," bytes for message body, but only read ").concat(t,".")},hu=function(){function e(t){C(this,e),this.source=t instanceof Qa?t:new Qa(t)}return M(e,[{key:Symbol.iterator,value:function(){return this}},{key:"next",value:function(){var e;return(e=this.readMetadataLength()).done||-1===e.value&&(e=this.readMetadataLength()).done||(e=this.readMetadata(e.value)).done?fe:e}},{key:"throw",value:function(e){return this.source.throw(e)}},{key:"return",value:function(e){return this.source.return(e)}},{key:"readMessage",value:function(e){var t;if((t=this.next()).done)return null;if(null!=e&&t.value.headerType!==e)throw new Error(lu(e));return t.value}},{key:"readMessageBody",value:function(e){if(e<=0)return new Uint8Array(0);var t=qe(this.source.read(e));if(t.byteLength<e)throw new Error(du(e,t.byteLength));return t.byteOffset%8===0&&t.byteOffset+t.byteLength<=t.buffer.byteLength?t:t.slice()}},{key:"readSchema",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=nn.Schema,n=this.readMessage(t),r=n&&n.header();if(e&&!r)throw new Error(cu(t));return r}},{key:"readMetadataLength",value:function(){var e=this.source.read(vu),t=e&&new su(e),n=t&&t.readInt32(0)||0;return{done:0===n,value:n}}},{key:"readMetadata",value:function(e){var t=this.source.read(e);if(!t)return fe;if(t.byteLength<e)throw new Error(fu(e,t.byteLength));return{done:!1,value:Go.decode(t)}}}]),e}(),pu=function(){function e(t,n){C(this,e),this.source=t instanceof Ga?t:Le(t)?new Ja(t,n):new Ga(t)}return M(e,[{key:Symbol.asyncIterator,value:function(){return this}},{key:"next",value:function(){var e=D(N.mark((function e(){var t;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.readMetadataLength();case 2:if(!(t=e.sent).done){e.next=4;break}return e.abrupt("return",fe);case 4:if(e.t0=-1===t.value,!e.t0){e.next=9;break}return e.next=8,this.readMetadataLength();case 8:e.t0=(t=e.sent).done;case 9:if(!e.t0){e.next=11;break}return e.abrupt("return",fe);case 11:return e.next=13,this.readMetadata(t.value);case 13:if(!(t=e.sent).done){e.next=15;break}return e.abrupt("return",fe);case 15:return e.abrupt("return",t);case 16:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"throw",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.source.throw(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.source.return(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"readMessage",value:function(){var e=D(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next();case 2:if(!(n=e.sent).done){e.next=4;break}return e.abrupt("return",null);case 4:if(null==t||n.value.headerType===t){e.next=6;break}throw new Error(lu(t));case 6:return e.abrupt("return",n.value);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"readMessageBody",value:function(){var e=D(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t<=0)){e.next=2;break}return e.abrupt("return",new Uint8Array(0));case 2:return e.t0=qe,e.next=5,this.source.read(t);case 5:if(e.t1=e.sent,!((n=(0,e.t0)(e.t1)).byteLength<t)){e.next=9;break}throw new Error(du(t,n.byteLength));case 9:return e.abrupt("return",n.byteOffset%8===0&&n.byteOffset+n.byteLength<=n.buffer.byteLength?n:n.slice());case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"readSchema",value:function(){var e=D(N.mark((function e(){var t,n,r,i,a=arguments;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=a.length>0&&void 0!==a[0]&&a[0],n=nn.Schema,e.next=4,this.readMessage(n);case 4:if(r=e.sent,i=r&&r.header(),!t||i){e.next=8;break}throw new Error(cu(n));case 8:return e.abrupt("return",i);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"readMetadataLength",value:function(){var e=D(N.mark((function e(){var t,n,r;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.source.read(vu);case 2:return t=e.sent,n=t&&new su(t),r=n&&n.readInt32(0)||0,e.abrupt("return",{done:0===r,value:r});case 6:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"readMetadata",value:function(){var e=D(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.source.read(t);case 2:if(n=e.sent){e.next=5;break}return e.abrupt("return",fe);case 5:if(!(n.byteLength<t)){e.next=7;break}throw new Error(fu(t,n.byteLength));case 7:return e.abrupt("return",{done:!1,value:Go.decode(n)});case 8:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),yu=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,new Uint8Array(0)))._schema=!1,r._body=[],r._batchIndex=0,r._dictionaryIndex=0,r._json=e instanceof de?e:new de(e),r}return M(n,[{key:"next",value:function(){var e=this._json;if(!this._schema)return this._schema=!0,{done:!1,value:Go.fromJSON(e.schema,nn.Schema)};if(this._dictionaryIndex<e.dictionaries.length){var t=e.dictionaries[this._dictionaryIndex++];return this._body=t.data.columns,{done:!1,value:Go.fromJSON(t,nn.DictionaryBatch)}}if(this._batchIndex<e.batches.length){var n=e.batches[this._batchIndex++];return this._body=n.columns,{done:!1,value:Go.fromJSON(n,nn.RecordBatch)}}return this._body=[],fe}},{key:"readMessageBody",value:function(e){return function e(t){return(t||[]).reduce((function(t,n){return[].concat(vn(t),vn(n.VALIDITY&&[n.VALIDITY]||[]),vn(n.TYPE&&[n.TYPE]||[]),vn(n.OFFSET&&[n.OFFSET]||[]),vn(n.DATA&&[n.DATA]||[]),vn(e(n.children)))}),[])}(this._body)}},{key:"readMessage",value:function(e){var t;if((t=this.next()).done)return null;if(null!=e&&t.value.headerType!==e)throw new Error(lu(e));return t.value}},{key:"readSchema",value:function(){var e=nn.Schema,t=this.readMessage(e),n=t&&t.header();if(!t||!n)throw new Error(cu(e));return n}}]),n}(hu),vu=4,bu=new Uint8Array("ARROW1".length),mu=0;mu<"ARROW1".length;mu+=1)bu[mu]="ARROW1".charCodeAt(mu);function gu(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=-1,r=bu.length;++n<r;)if(bu[n]!==e[t+n])return!1;return!0}var ku=bu.length,wu=ku+vu,_u=2*ku+vu,xu=function(e){ie(n,e);var t=ce(n);function n(){var e;return C(this,n),(e=t.call(this))._byteLength=0,e._nodes=[],e._buffers=[],e._bufferRegions=[],e}return M(n,[{key:"visit",value:function(e){if(!Dn.isDictionary(e.type)){var t=e.data,r=e.length,i=e.nullCount;if(r>2147483647)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");Dn.isNull(e.type)||Su.call(this,i<=0?new Uint8Array(0):ln(t.offset,r,t.nullBitmap)),this.nodes.push(new Jo(r,i))}return jt(ae(n.prototype),"visit",this).call(this,e)}},{key:"visitNull",value:function(e){return this}},{key:"visitDictionary",value:function(e){return this.visit(e.indices)}},{key:"nodes",get:function(){return this._nodes}},{key:"buffers",get:function(){return this._buffers}},{key:"byteLength",get:function(){return this._byteLength}},{key:"bufferRegions",get:function(){return this._bufferRegions}}],[{key:"assemble",value:function(){for(var e=new n,t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];var a=ta(Sl,r),o=e.visitMany(a),u=Object(P.a)(o,1),s=u[0],l=void 0===s?e:s;return l}}]),n}(bn);function Su(e){var t=e.byteLength+7&-8;return this.buffers.push(e),this.bufferRegions.push(new Zo(this._byteLength,t)),this._byteLength+=t,this}function Tu(e){return Su.call(this,e.values.subarray(0,e.length*e.stride))}function Iu(e){var t=e.length,n=e.values,r=e.valueOffsets,i=r[0],a=r[t],o=Math.min(a-i,n.byteLength-i);return Su.call(this,Tt(-r[0],t,r)),Su.call(this,n.subarray(i,i+o)),this}function Eu(e){var t=e.length,n=e.valueOffsets;return n&&Su.call(this,Tt(n[0],t,n)),this.visit(e.getChildAt(0))}function Au(e){return this.visitMany(e.type.children.map((function(t,n){return e.getChildAt(n)})).filter(Boolean))[0]}xu.prototype.visitBool=function(e){var t;return e.nullCount>=e.length?Su.call(this,new Uint8Array(0)):(t=e.values)instanceof Uint8Array?Su.call(this,ln(e.offset,e.length,t)):Su.call(this,cn(e))},xu.prototype.visitInt=Tu,xu.prototype.visitFloat=Tu,xu.prototype.visitUtf8=Iu,xu.prototype.visitBinary=Iu,xu.prototype.visitFixedSizeBinary=Tu,xu.prototype.visitDate=Tu,xu.prototype.visitTimestamp=Tu,xu.prototype.visitTime=Tu,xu.prototype.visitDecimal=Tu,xu.prototype.visitList=Eu,xu.prototype.visitStruct=Au,xu.prototype.visitUnion=function(e){var t=e.type,n=e.length,r=e.typeIds,i=e.valueOffsets;if(Su.call(this,r),t.mode===en.Sparse)return Au.call(this,e);if(t.mode===en.Dense){if(e.offset<=0)return Su.call(this,i),Au.call(this,e);for(var a,o,u=r.reduce((function(e,t){return Math.max(e,t)}),r[0]),s=new Int32Array(u+1),l=new Int32Array(u+1).fill(-1),c=new Int32Array(n),f=Tt(-i[0],n,i),d=-1;++d<n;)-1===(o=l[a=r[d]])&&(o=l[a]=f[a]),c[d]=f[d]-o,++s[a];Su.call(this,c);for(var h,p=-1,y=t.children.length;++p<y;)if(h=e.getChildAt(p)){var v=t.typeIds[p],b=Math.min(n,s[v]);this.visit(h.slice(l[v],b))}}return this},xu.prototype.visitInterval=Tu,xu.prototype.visitFixedSizeList=Eu,xu.prototype.visitMap=Eu;var Fu=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this))._position=0,r._started=!1,r._sink=new Ka,r._schema=null,r._dictionaryBlocks=[],r._recordBatchBlocks=[],r._dictionaryDeltaOffsets=new Map,Ae(e)||(e={autoDestroy:!0,writeLegacyIpcFormat:!1}),r._autoDestroy="boolean"!==typeof e.autoDestroy||e.autoDestroy,r._writeLegacyIpcFormat="boolean"===typeof e.writeLegacyIpcFormat&&e.writeLegacyIpcFormat,r}return M(n,[{key:"toString",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toString(e)}},{key:"toUint8Array",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toUint8Array(e)}},{key:"writeAll",value:function(e){var t=this;return Fe(e)?e.then((function(e){return t.writeAll(e)})):Be(e)?Cu(this,e):Du(this,e)}},{key:"closed",get:function(){return this._sink.closed}},{key:Symbol.asyncIterator,value:function(){return this._sink[Symbol.asyncIterator]()}},{key:"toDOMStream",value:function(e){return this._sink.toDOMStream(e)}},{key:"toNodeStream",value:function(e){return this._sink.toNodeStream(e)}},{key:"close",value:function(){return this.reset()._sink.close()}},{key:"abort",value:function(e){return this.reset()._sink.abort(e)}},{key:"finish",value:function(){return this._autoDestroy?this.close():this.reset(this._sink,this._schema),this}},{key:"reset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._sink,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e===this._sink||e instanceof Ka?this._sink=e:(this._sink=new Ka,e&&Ne(e)?this.toDOMStream({type:"bytes"}).pipeTo(e):e&&Ue(e)&&this.toNodeStream({objectMode:!1}).pipe(e)),this._started&&this._schema&&this._writeFooter(this._schema),this._started=!1,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,t&&t.compareTo(this._schema)||(null===t?(this._position=0,this._schema=null):(this._started=!0,this._schema=t,this._writeSchema(t))),this}},{key:"write",value:function(e){var t=null;if(!this._sink)throw new Error("RecordBatchWriter is closed");if(null===e||void 0===e)return this.finish()&&void 0;if(e instanceof xl&&!(t=e.schema))return this.finish()&&void 0;if(e instanceof Sl&&!(t=e.schema))return this.finish()&&void 0;if(t&&!t.compareTo(this._schema)){if(this._started&&this._autoDestroy)return this.close();this.reset(this._sink,t)}e instanceof Sl?e instanceof Tl||this._writeRecordBatch(e):e instanceof xl?this.writeAll(e.chunks):Oe(e)&&this.writeAll(e)}},{key:"_writeMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8,n=t-1,r=Go.encode(e),i=r.byteLength,a=this._writeLegacyIpcFormat?4:8,o=i+a+n&~n,u=o-i-a;return e.headerType===nn.RecordBatch?this._recordBatchBlocks.push(new $a(o,e.bodyLength,this._position)):e.headerType===nn.DictionaryBatch&&this._dictionaryBlocks.push(new $a(o,e.bodyLength,this._position)),this._writeLegacyIpcFormat||this._write(Int32Array.of(-1)),this._write(Int32Array.of(o-a)),i>0&&this._write(r),this._writePadding(u)}},{key:"_write",value:function(e){if(this._started){var t=qe(e);t&&t.byteLength>0&&(this._sink.write(t),this._position+=t.byteLength)}return this}},{key:"_writeSchema",value:function(e){return this._writeMessage(Go.from(e))}},{key:"_writeFooter",value:function(e){return this._writeLegacyIpcFormat?this._write(Int32Array.of(0)):this._write(Int32Array.of(-1,0))}},{key:"_writeMagic",value:function(){return this._write(bu)}},{key:"_writePadding",value:function(e){return e>0?this._write(new Uint8Array(e)):this}},{key:"_writeRecordBatch",value:function(e){var t=xu.assemble(e),n=t.byteLength,r=t.nodes,i=t.bufferRegions,a=t.buffers,o=new qo(e.length,r,i),u=Go.from(o,n);return this._writeDictionaries(e)._writeMessage(u)._writeBodyBuffers(a)}},{key:"_writeDictionaryBatch",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._dictionaryDeltaOffsets.set(t,e.length+(this._dictionaryDeltaOffsets.get(t)||0));var r=xu.assemble(e),i=r.byteLength,a=r.nodes,o=r.bufferRegions,u=r.buffers,s=new qo(e.length,a,o),l=new Xo(s,t,n),c=Go.from(l,i);return this._writeMessage(c)._writeBodyBuffers(u)}},{key:"_writeBodyBuffers",value:function(e){for(var t,n,r,i=-1,a=e.length;++i<a;)(t=e[i])&&(n=t.byteLength)>0&&(this._write(t),(r=(n+7&-8)-n)>0&&this._writePadding(r));return this}},{key:"_writeDictionaries",value:function(e){var t,n=O(e.dictionaries);try{for(n.s();!(t=n.n()).done;){var r=Object(P.a)(t.value,2),i=r[0],a=r[1],o=this._dictionaryDeltaOffsets.get(i)||0;if(0===o||(a=a.slice(o)).length>0){var u,s=O("chunks"in a?a.chunks:[a]);try{for(s.s();!(u=s.n()).done;){var l=u.value;this._writeDictionaryBatch(l,i,o>0),o+=l.length}}catch(c){s.e(c)}finally{s.f()}}}}catch(c){n.e(c)}finally{n.f()}return this}}],[{key:"throughNode",value:function(e){throw new Error('"throughNode" not available in this environment')}},{key:"throughDOM",value:function(e,t){throw new Error('"throughDOM" not available in this environment')}}]),n}(he),Ou=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,null,[{key:"writeAll",value:function(e,t){var r=new n(t);return Fe(e)?e.then((function(e){return r.writeAll(e)})):Be(e)?Cu(r,e):Du(r,e)}}]),n}(Fu),Bu=function(e){ie(n,e);var t=ce(n);function n(){var e;return C(this,n),(e=t.call(this))._autoDestroy=!0,e}return M(n,[{key:"_writeSchema",value:function(e){return this._writeMagic()._writePadding(2)}},{key:"_writeFooter",value:function(e){var t=Ha.encode(new Ha(e,rn.V4,this._recordBatchBlocks,this._dictionaryBlocks));return jt(ae(n.prototype),"_writeFooter",this).call(this,e)._write(t)._write(Int32Array.of(t.byteLength))._writeMagic()}}],[{key:"writeAll",value:function(e){var t=new n;return Fe(e)?e.then((function(e){return t.writeAll(e)})):Be(e)?Cu(t,e):Du(t,e)}}]),n}(Fu);function Du(e,t){var n=t;t instanceof xl&&(n=t.chunks,e.reset(void 0,t.schema));var r,i=O(n);try{for(i.s();!(r=i.n()).done;){var a=r.value;e.write(a)}}catch(o){i.e(o)}finally{i.f()}return e.finish()}function Cu(e,t){var n,r,i,a,o,u,s;return N.async((function(l){for(;;)switch(l.prev=l.next){case 0:n=!0,r=!1,l.prev=2,a=V(t);case 4:return l.next=6,N.awrap(a.next());case 6:return o=l.sent,n=o.done,l.next=10,N.awrap(o.value);case 10:if(u=l.sent,n){l.next=17;break}s=u,e.write(s);case 14:n=!0,l.next=4;break;case 17:l.next=23;break;case 19:l.prev=19,l.t0=l.catch(2),r=!0,i=l.t0;case 23:if(l.prev=23,l.prev=24,n||null==a.return){l.next=28;break}return l.next=28,N.awrap(a.return());case 28:if(l.prev=28,!r){l.next=31;break}throw i;case 31:return l.finish(28);case 32:return l.finish(23);case 33:return l.abrupt("return",e.finish());case 34:case"end":return l.stop()}}),null,null,[[2,19,23,33],[24,,28,32]],Promise)}var Lu=new Uint8Array(0),Mu=function(e){return[Lu,Lu,new Uint8Array(e),Lu]};function Nu(e,t){for(var n,r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.reduce((function(e,t){return Math.max(e,t.length)}),0),a=-1,o=t.length,u=vn(e.fields),s=[],l=(i+63&-64)>>3;++a<o;)(n=t[a])&&n.length===i?s[a]=n:((r=u[a]).nullable||(u[a]=u[a].clone({nullable:!0})),s[a]=n?n._changeLengthAndBackfillNullBitmap(i):hr.new(r.type,0,i,i,Mu(l)));return[new aa(u),i,s]}function Pu(e){return Uu(new aa(e.map((function(e){return e.field}))),e)}function Uu(e,t){return function(e,t){var n,r=vn(e.fields),i=[],a={numBatches:t.reduce((function(e,t){return Math.max(e,t.length)}),0)},o=0,u=0,s=-1,l=t.length,c=[];for(;a.numBatches-- >0;){for(u=Number.POSITIVE_INFINITY,s=-1;++s<l;)c[s]=n=t[s].shift(),u=Math.min(u,n?n.length:u);isFinite(u)&&(c=Ru(r,u,c,t,a),u>0&&(i[o++]=[u,c.slice()]))}return[e=new aa(r,e.metadata),i.map((function(t){return Vr(Sl,[e].concat(vn(t)))}))]}(e,t.map((function(e){return e instanceof Hi?e.chunks.map((function(e){return e.data})):[e.data]})))}function Ru(e,t,n,r,i){for(var a,o,u=0,s=-1,l=r.length,c=(t+63&-64)>>3;++s<l;)(a=n[s])&&(u=a.length)>=t?u===t?n[s]=a:(n[s]=a.slice(0,t),a=a.slice(t,u-t),i.numBatches=Math.max(i.numBatches,r[s].unshift(a))):((o=e[s]).nullable||(e[s]=o.clone({nullable:!0})),n[s]=a?a._changeLengthAndBackfillNullBitmap(t):hr.new(o.type,0,t,t,Mu(c)));return n}function zu(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var ju=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this))._children=r,i.numChildren=e.childData.length,i._bindDataAccessors(i.data=e),i}return M(n,[{key:"type",get:function(){return this.data.type}},{key:"typeId",get:function(){return this.data.typeId}},{key:"length",get:function(){return this.data.length}},{key:"offset",get:function(){return this.data.offset}},{key:"stride",get:function(){return this.data.stride}},{key:"nullCount",get:function(){return this.data.nullCount}},{key:"byteLength",get:function(){return this.data.byteLength}},{key:"VectorName",get:function(){return"".concat(Gt[this.typeId],"Vector")}},{key:"ArrayType",get:function(){return this.type.ArrayType}},{key:"values",get:function(){return this.data.values}},{key:"typeIds",get:function(){return this.data.typeIds}},{key:"nullBitmap",get:function(){return this.data.nullBitmap}},{key:"valueOffsets",get:function(){return this.data.valueOffsets}},{key:Symbol.toStringTag,get:function(){return"".concat(this.VectorName,"<").concat(this.type[Symbol.toStringTag],">")}},{key:"clone",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._children;return Qt.new(e,t)}},{key:"concat",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Hi.concat.apply(Hi,[this].concat(t))}},{key:"slice",value:function(e,t){return Ui(this,e,t,this._sliceInternal)}},{key:"isValid",value:function(e){if(this.nullCount>0){var t=this.offset+e;return 0!==(this.nullBitmap[t>>3]&1<<t%8)}return!0}},{key:"getChildAt",value:function(e){return e<0||e>=this.numChildren?null:(this._children||(this._children=[]))[e]||(this._children[e]=Qt.new(this.data.childData[e]))}},{key:"toJSON",value:function(){return vn(this)}},{key:"_sliceInternal",value:function(e,t,n){return e.clone(e.data.slice(t,n-t),null)}},{key:"_bindDataAccessors",value:function(e){}}]),n}(Qt);ju.prototype[Symbol.isConcatSpreadable]=!0;var Vu=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"asUtf8",value:function(){return Qt.new(this.data.clone(new Qn))}}]),n}(ju),Wu=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,null,[{key:"from",value:function(e){return _l((function(){return new Gn}),e)}}]),n}(ju),Hu=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,null,[{key:"from",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 2===t.length?_l((function(){return t[1]===Xt.DAY?new Zn:new Jn}),t[0]):_l((function(){return new Jn}),t[0])}}]),n}(ju),Yu=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Hu),$u=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Hu),Ku=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ju),Qu=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e)).indices=Qt.new(e.clone(r.type.indices)),r}return M(n,[{key:"dictionary",get:function(){return this.data.dictionary}},{key:"reverseLookup",value:function(e){return this.dictionary.indexOf(e)}},{key:"getKey",value:function(e){return this.indices.get(e)}},{key:"getValue",value:function(e){return this.dictionary.get(e)}},{key:"setKey",value:function(e,t){return this.indices.set(e,t)}},{key:"setValue",value:function(e,t){return this.dictionary.set(e,t)}}],[{key:"from",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(3===t.length){var r=t[0],i=t[1],a=t[2],o=new fr(r.type,i,null,null);return Qt.new(hr.Dictionary(o,0,a.length,0,null,a,r))}return _l((function(){return t[0].type}),t[0])}}]),n}(ju);Qu.prototype.indices=null;var Gu=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ju),qu=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ju),Xu=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,null,[{key:"from",value:function(e){var t=rs(this);if(e instanceof ArrayBuffer||ArrayBuffer.isView(e)){var n=ns(e.constructor)||t;if(null===t&&(t=n),t&&t===n){var r=new t,i=e.byteLength/r.ArrayType.BYTES_PER_ELEMENT;if(!ts(t,e.constructor))return Qt.new(hr.Float(r,0,i,0,null,e))}}if(t)return _l((function(){return new t}),e);if(e instanceof DataView||e instanceof ArrayBuffer)throw new TypeError("Cannot infer float type from instance of ".concat(e.constructor.name));throw new TypeError("Unrecognized FloatVector input")}}]),n}(ju),Zu=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"toFloat32Array",value:function(){return new Float32Array(this)}},{key:"toFloat64Array",value:function(){return new Float64Array(this)}}]),n}(Xu),Ju=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Xu),es=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Xu),ts=function(e,t){return e===Hn&&t!==Uint16Array},ns=function(e){switch(e){case Uint16Array:return Hn;case Float32Array:return Yn;case Float64Array:return $n;default:return null}},rs=function(e){switch(e){case Zu:return Hn;case Ju:return Yn;case es:return $n;default:return null}},is=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ju),as=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(is),os=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(is),us=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,null,[{key:"from",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t[0],i=t[1],a=void 0!==i&&i,o=ms(this,a);if(r instanceof ArrayBuffer||ArrayBuffer.isView(r)){var u=bs(r.constructor,a)||o;if(null===o&&(o=u),o&&o===u){var s=new o,l=r.byteLength/s.ArrayType.BYTES_PER_ELEMENT;return vs(o,r.constructor)&&(l*=.5),Qt.new(hr.Int(s,0,l,0,null,r))}}if(o)return _l((function(){return new o}),r);if(r instanceof DataView||r instanceof ArrayBuffer)throw new TypeError("Cannot infer integer type from instance of ".concat(r.constructor.name));throw new TypeError("Unrecognized IntVector input")}}]),n}(ju),ss=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(us),ls=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(us),cs=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(us),fs=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"toBigInt64Array",value:function(){return Ge(this.values)}},{key:"values64",get:function(){return this._values64||(this._values64=this.toBigInt64Array())}}]),n}(us),ds=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(us),hs=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(us),ps=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(us),ys=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"toBigUint64Array",value:function(){return Je(this.values)}},{key:"values64",get:function(){return this._values64||(this._values64=this.toBigUint64Array())}}]),n}(us),vs=function(e,t){return(e===Un||e===Vn)&&(t===Int32Array||t===Uint32Array)},bs=function(e,t){switch(e){case Int8Array:return Mn;case Int16Array:return Nn;case Int32Array:return t?Un:Pn;case we:return Un;case Uint8Array:return Rn;case Uint16Array:return zn;case Uint32Array:return t?Vn:jn;case Se:return Vn;default:return null}},ms=function(e,t){switch(e){case ss:return Mn;case ls:return Nn;case cs:return t?Un:Pn;case fs:return Un;case ds:return Rn;case hs:return zn;case ps:return t?Vn:jn;case ys:return Vn;default:return null}},gs=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ju),ks=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"asList",value:function(){var e=this.type.children[0];return Qt.new(this.data.clone(new rr(e)))}},{key:"bind",value:function(e){var t=this.getChildAt(0),n=this.valueOffsets,r=n[e],i=n[e+1];return new Di(t.slice(r,i))}}]),n}(ju),ws=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ju),_s=Symbol.for("rowIndex"),xs=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"bind",value:function(e){var t=this._row||(this._row=new Ci(this)),n=Object.create(t);return n[_s]=e,n}}]),n}(ju),Ss=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ju),Ts=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Ss),Is=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Ss),Es=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Ss),As=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Ss),Fs=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(ju),Os=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Fs),Bs=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Fs),Ds=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Fs),Cs=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Fs),Ls=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"typeIdToChildIndex",get:function(){return this.data.type.typeIdToChildIndex}}]),n}(ju),Ms=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"valueOffsets",get:function(){return this.data.valueOffsets}}]),n}(Ls),Ns=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(Ls),Ps=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"asBinary",value:function(){return Qt.new(this.data.clone(new Kn))}}],[{key:"from",value:function(e){return _l((function(){return new Qn}),e)}}]),n}(ju);function Us(e){return function(){return e(this)}}function Rs(e){return function(t,n){return e(this,t,n)}}var zs=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(bn),js=function(e,t){return 4294967296*e[t+1]+(e[t]>>>0)},Vs=function(e){return new Date(e)},Ws=function(e,t,n){var r=t[n],i=t[n+1];return null!=r&&null!=i?e.subarray(r,i):null},Hs=function(e,t){return function(e,t){return Vs(function(e,t){return 864e5*e[t]}(e,t))}(e.values,t)},Ys=function(e,t){return function(e,t){return Vs(js(e,t))}(e.values,2*t)},$s=function(e,t){var n=e.stride;return e.values[n*t]},Ks=function(e,t){var n=e.stride;return Nr(e.values[n*t])},Qs=function(e,t){var n=e.stride,r=e.values,i=e.type;return Jr.new(r.subarray(n*t,n*(t+1)),i.isSigned)},Gs=function(e,t){var n=e.values;return 1e3*js(n,2*t)},qs=function(e,t){var n=e.values;return js(n,2*t)},Xs=function(e,t){return function(e,t){return e[t+1]/1e3*4294967296+(e[t]>>>0)/1e3}(e.values,2*t)},Zs=function(e,t){return function(e,t){return e[t+1]/1e6*4294967296+(e[t]>>>0)/1e6}(e.values,2*t)},Js=function(e,t){return e.values[e.stride*t]},el=function(e,t){return e.values[e.stride*t]},tl=function(e,t){var n=e.values;return Jr.signed(n.subarray(2*t,2*(t+1)))},nl=function(e,t){var n=e.values;return Jr.signed(n.subarray(2*t,2*(t+1)))},rl=function(e,t){var n=e.typeIdToChildIndex[e.typeIds[t]],r=e.getChildAt(n);return r?r.get(e.valueOffsets[t]):null},il=function(e,t){var n=e.typeIdToChildIndex[e.typeIds[t]],r=e.getChildAt(n);return r?r.get(t):null},al=function(e,t){return e.values.subarray(2*t,2*(t+1))},ol=function(e,t){var n=e.values[t],r=new Int32Array(2);return r[0]=n/12|0,r[1]=n%12|0,r};zs.prototype.visitNull=function(e,t){return null},zs.prototype.visitBool=function(e,t){var n=e.offset+t;return 0!==(e.values[n>>3]&1<<n%8)},zs.prototype.visitInt=function(e,t){return e.type.bitWidth<64?$s(e,t):Qs(e,t)},zs.prototype.visitInt8=$s,zs.prototype.visitInt16=$s,zs.prototype.visitInt32=$s,zs.prototype.visitInt64=Qs,zs.prototype.visitUint8=$s,zs.prototype.visitUint16=$s,zs.prototype.visitUint32=$s,zs.prototype.visitUint64=Qs,zs.prototype.visitFloat=function(e,t){return e.type.precision!==Jt.HALF?$s(e,t):Ks(e,t)},zs.prototype.visitFloat16=Ks,zs.prototype.visitFloat32=$s,zs.prototype.visitFloat64=$s,zs.prototype.visitUtf8=function(e,t){var n=e.values,r=e.valueOffsets,i=Ws(n,r,t);return null!==i?te(i):null},zs.prototype.visitBinary=function(e,t){var n=e.values,r=e.valueOffsets;return Ws(n,r,t)},zs.prototype.visitFixedSizeBinary=function(e,t){var n=e.stride;return e.values.subarray(n*t,n*(t+1))},zs.prototype.visitDate=function(e,t){return e.type.unit===Xt.DAY?Hs(e,t):Ys(e,t)},zs.prototype.visitDateDay=Hs,zs.prototype.visitDateMillisecond=Ys,zs.prototype.visitTimestamp=function(e,t){switch(e.type.unit){case Zt.SECOND:return Gs(e,t);case Zt.MILLISECOND:return qs(e,t);case Zt.MICROSECOND:return Xs(e,t);case Zt.NANOSECOND:return Zs(e,t)}},zs.prototype.visitTimestampSecond=Gs,zs.prototype.visitTimestampMillisecond=qs,zs.prototype.visitTimestampMicrosecond=Xs,zs.prototype.visitTimestampNanosecond=Zs,zs.prototype.visitTime=function(e,t){switch(e.type.unit){case Zt.SECOND:return Js(e,t);case Zt.MILLISECOND:return el(e,t);case Zt.MICROSECOND:return tl(e,t);case Zt.NANOSECOND:return nl(e,t)}},zs.prototype.visitTimeSecond=Js,zs.prototype.visitTimeMillisecond=el,zs.prototype.visitTimeMicrosecond=tl,zs.prototype.visitTimeNanosecond=nl,zs.prototype.visitDecimal=function(e,t){var n=e.values;return Jr.decimal(n.subarray(4*t,4*(t+1)))},zs.prototype.visitList=function(e,t){var n=e.getChildAt(0),r=e.valueOffsets,i=e.stride;return n.slice(r[t*i],r[t*i+1])},zs.prototype.visitStruct=function(e,t){return e.bind(t)},zs.prototype.visitUnion=function(e,t){return e.type.mode===en.Dense?rl(e,t):il(e,t)},zs.prototype.visitDenseUnion=rl,zs.prototype.visitSparseUnion=il,zs.prototype.visitDictionary=function(e,t){return e.getValue(e.getKey(t))},zs.prototype.visitInterval=function(e,t){return e.type.unit===tn.DAY_TIME?al(e,t):ol(e,t)},zs.prototype.visitIntervalDayTime=al,zs.prototype.visitIntervalYearMonth=ol,zs.prototype.visitFixedSizeList=function(e,t){var n=e.getChildAt(0),r=e.stride;return n.slice(t*r,(t+1)*r)},zs.prototype.visitMap=function(e,t){return e.bind(t)};var ul=new zs,sl=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(bn);function ll(e,t,n){if(void 0===t)return-1;if(null===t)return function(e,t){var n=e.nullBitmap;if(!n||e.nullCount<=0)return-1;var r,i=0,a=O(fn(n,e.data.offset+(t||0),e.length,n,on));try{for(a.s();!(r=a.n()).done;){if(!r.value)return i;++i}}catch(o){a.e(o)}finally{a.f()}return-1}(e,n);for(var r=ji(t),i=(n||0)-1,a=e.length;++i<a;)if(r(e.get(i)))return i;return-1}function cl(e,t,n){for(var r=ji(t),i=(n||0)-1,a=e.length;++i<a;)if(r(e.get(i)))return i;return-1}sl.prototype.visitNull=function(e,t){return null===t&&e.length>0?0:-1},sl.prototype.visitBool=ll,sl.prototype.visitInt=ll,sl.prototype.visitInt8=ll,sl.prototype.visitInt16=ll,sl.prototype.visitInt32=ll,sl.prototype.visitInt64=ll,sl.prototype.visitUint8=ll,sl.prototype.visitUint16=ll,sl.prototype.visitUint32=ll,sl.prototype.visitUint64=ll,sl.prototype.visitFloat=ll,sl.prototype.visitFloat16=ll,sl.prototype.visitFloat32=ll,sl.prototype.visitFloat64=ll,sl.prototype.visitUtf8=ll,sl.prototype.visitBinary=ll,sl.prototype.visitFixedSizeBinary=ll,sl.prototype.visitDate=ll,sl.prototype.visitDateDay=ll,sl.prototype.visitDateMillisecond=ll,sl.prototype.visitTimestamp=ll,sl.prototype.visitTimestampSecond=ll,sl.prototype.visitTimestampMillisecond=ll,sl.prototype.visitTimestampMicrosecond=ll,sl.prototype.visitTimestampNanosecond=ll,sl.prototype.visitTime=ll,sl.prototype.visitTimeSecond=ll,sl.prototype.visitTimeMillisecond=ll,sl.prototype.visitTimeMicrosecond=ll,sl.prototype.visitTimeNanosecond=ll,sl.prototype.visitDecimal=ll,sl.prototype.visitList=ll,sl.prototype.visitStruct=ll,sl.prototype.visitUnion=ll,sl.prototype.visitDenseUnion=cl,sl.prototype.visitSparseUnion=cl,sl.prototype.visitDictionary=ll,sl.prototype.visitInterval=ll,sl.prototype.visitIntervalDayTime=ll,sl.prototype.visitIntervalYearMonth=ll,sl.prototype.visitFixedSizeList=ll,sl.prototype.visitMap=ll;var fl=new sl,dl=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(bn);function hl(e){if(e.nullCount>0)return function(e){var t=ul.getVisitFn(e);return fn(e.nullBitmap,e.offset,e.length,e,(function(e,n,r,i){return 0!==(r&1<<i)?t(e,n):null}))}(e);var t=e.type,n=e.typeId,r=e.length;return 1===e.stride&&(n===Gt.Timestamp||n===Gt.Int&&64!==t.bitWidth||n===Gt.Time&&64!==t.bitWidth||n===Gt.Float&&t.precision>0)?e.values.subarray(0,r)[Symbol.iterator]():N.mark((function t(n){var i;return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=-1;case 1:if(!(++i<r)){t.next=6;break}return t.next=4,n(e,i);case 4:t.next=1;break;case 6:case"end":return t.stop()}}),t)}))(ul.getVisitFn(e))}dl.prototype.visitNull=hl,dl.prototype.visitBool=hl,dl.prototype.visitInt=hl,dl.prototype.visitInt8=hl,dl.prototype.visitInt16=hl,dl.prototype.visitInt32=hl,dl.prototype.visitInt64=hl,dl.prototype.visitUint8=hl,dl.prototype.visitUint16=hl,dl.prototype.visitUint32=hl,dl.prototype.visitUint64=hl,dl.prototype.visitFloat=hl,dl.prototype.visitFloat16=hl,dl.prototype.visitFloat32=hl,dl.prototype.visitFloat64=hl,dl.prototype.visitUtf8=hl,dl.prototype.visitBinary=hl,dl.prototype.visitFixedSizeBinary=hl,dl.prototype.visitDate=hl,dl.prototype.visitDateDay=hl,dl.prototype.visitDateMillisecond=hl,dl.prototype.visitTimestamp=hl,dl.prototype.visitTimestampSecond=hl,dl.prototype.visitTimestampMillisecond=hl,dl.prototype.visitTimestampMicrosecond=hl,dl.prototype.visitTimestampNanosecond=hl,dl.prototype.visitTime=hl,dl.prototype.visitTimeSecond=hl,dl.prototype.visitTimeMillisecond=hl,dl.prototype.visitTimeMicrosecond=hl,dl.prototype.visitTimeNanosecond=hl,dl.prototype.visitDecimal=hl,dl.prototype.visitList=hl,dl.prototype.visitStruct=hl,dl.prototype.visitUnion=hl,dl.prototype.visitDenseUnion=hl,dl.prototype.visitSparseUnion=hl,dl.prototype.visitDictionary=hl,dl.prototype.visitInterval=hl,dl.prototype.visitIntervalDayTime=hl,dl.prototype.visitIntervalYearMonth=hl,dl.prototype.visitFixedSizeList=hl,dl.prototype.visitMap=hl;var pl=new dl,yl=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return n}(bn);function vl(e){var t=e.type,n=e.length,r=e.stride;switch(t.typeId){case Gt.Int:case Gt.Float:case Gt.Decimal:case Gt.Time:case Gt.Timestamp:return e.values.subarray(0,n*r)}return vn(pl.visit(e))}yl.prototype.visitNull=vl,yl.prototype.visitBool=vl,yl.prototype.visitInt=vl,yl.prototype.visitInt8=vl,yl.prototype.visitInt16=vl,yl.prototype.visitInt32=vl,yl.prototype.visitInt64=vl,yl.prototype.visitUint8=vl,yl.prototype.visitUint16=vl,yl.prototype.visitUint32=vl,yl.prototype.visitUint64=vl,yl.prototype.visitFloat=vl,yl.prototype.visitFloat16=vl,yl.prototype.visitFloat32=vl,yl.prototype.visitFloat64=vl,yl.prototype.visitUtf8=vl,yl.prototype.visitBinary=vl,yl.prototype.visitFixedSizeBinary=vl,yl.prototype.visitDate=vl,yl.prototype.visitDateDay=vl,yl.prototype.visitDateMillisecond=vl,yl.prototype.visitTimestamp=vl,yl.prototype.visitTimestampSecond=vl,yl.prototype.visitTimestampMillisecond=vl,yl.prototype.visitTimestampMicrosecond=vl,yl.prototype.visitTimestampNanosecond=vl,yl.prototype.visitTime=vl,yl.prototype.visitTimeSecond=vl,yl.prototype.visitTimeMillisecond=vl,yl.prototype.visitTimeMicrosecond=vl,yl.prototype.visitTimeNanosecond=vl,yl.prototype.visitDecimal=vl,yl.prototype.visitList=vl,yl.prototype.visitStruct=vl,yl.prototype.visitUnion=vl,yl.prototype.visitDenseUnion=vl,yl.prototype.visitSparseUnion=vl,yl.prototype.visitDictionary=vl,yl.prototype.visitInterval=vl,yl.prototype.visitIntervalDayTime=vl,yl.prototype.visitIntervalYearMonth=vl,yl.prototype.visitFixedSizeList=vl,yl.prototype.visitMap=vl;var bl=new yl,ml=function(e,t){return e+t},gl=function(e){return"Cannot compute the byte width of variable-width column ".concat(e)},kl=new(function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"visitNull",value:function(e){return 0}},{key:"visitInt",value:function(e){return e.bitWidth/8}},{key:"visitFloat",value:function(e){return e.ArrayType.BYTES_PER_ELEMENT}},{key:"visitBinary",value:function(e){throw new Error(gl(e))}},{key:"visitUtf8",value:function(e){throw new Error(gl(e))}},{key:"visitBool",value:function(e){return 1/8}},{key:"visitDecimal",value:function(e){return 16}},{key:"visitDate",value:function(e){return 4*(e.unit+1)}},{key:"visitTime",value:function(e){return e.bitWidth/8}},{key:"visitTimestamp",value:function(e){return e.unit===Zt.SECOND?4:8}},{key:"visitInterval",value:function(e){return 4*(e.unit+1)}},{key:"visitList",value:function(e){throw new Error(gl(e))}},{key:"visitStruct",value:function(e){return this.visitFields(e.children).reduce(ml,0)}},{key:"visitUnion",value:function(e){return this.visitFields(e.children).reduce(ml,0)}},{key:"visitFixedSizeBinary",value:function(e){return e.byteWidth}},{key:"visitFixedSizeList",value:function(e){return e.listSize*this.visitFields(e.children).reduce(ml,0)}},{key:"visitMap",value:function(e){return this.visitFields(e.children).reduce(ml,0)}},{key:"visitDictionary",value:function(e){return this.visit(e.indices)}},{key:"visitFields",value:function(e){var t=this;return(e||[]).map((function(e){return t.visit(e.type)}))}},{key:"visitSchema",value:function(e){return this.visitFields(e.fields).reduce(ml,0)}}]),n}(bn)),wl=new(function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"visitNull",value:function(){return ws}},{key:"visitBool",value:function(){return Wu}},{key:"visitInt",value:function(){return us}},{key:"visitInt8",value:function(){return ss}},{key:"visitInt16",value:function(){return ls}},{key:"visitInt32",value:function(){return cs}},{key:"visitInt64",value:function(){return fs}},{key:"visitUint8",value:function(){return ds}},{key:"visitUint16",value:function(){return hs}},{key:"visitUint32",value:function(){return ps}},{key:"visitUint64",value:function(){return ys}},{key:"visitFloat",value:function(){return Xu}},{key:"visitFloat16",value:function(){return Zu}},{key:"visitFloat32",value:function(){return Ju}},{key:"visitFloat64",value:function(){return es}},{key:"visitUtf8",value:function(){return Ps}},{key:"visitBinary",value:function(){return Vu}},{key:"visitFixedSizeBinary",value:function(){return Gu}},{key:"visitDate",value:function(){return Hu}},{key:"visitDateDay",value:function(){return Yu}},{key:"visitDateMillisecond",value:function(){return $u}},{key:"visitTimestamp",value:function(){return Ss}},{key:"visitTimestampSecond",value:function(){return Ts}},{key:"visitTimestampMillisecond",value:function(){return Is}},{key:"visitTimestampMicrosecond",value:function(){return Es}},{key:"visitTimestampNanosecond",value:function(){return As}},{key:"visitTime",value:function(){return Fs}},{key:"visitTimeSecond",value:function(){return Os}},{key:"visitTimeMillisecond",value:function(){return Bs}},{key:"visitTimeMicrosecond",value:function(){return Ds}},{key:"visitTimeNanosecond",value:function(){return Cs}},{key:"visitDecimal",value:function(){return Ku}},{key:"visitList",value:function(){return gs}},{key:"visitStruct",value:function(){return xs}},{key:"visitUnion",value:function(){return Ls}},{key:"visitDenseUnion",value:function(){return Ms}},{key:"visitSparseUnion",value:function(){return Ns}},{key:"visitDictionary",value:function(){return Qu}},{key:"visitInterval",value:function(){return is}},{key:"visitIntervalDayTime",value:function(){return as}},{key:"visitIntervalYearMonth",value:function(){return os}},{key:"visitFixedSizeList",value:function(){return qu}},{key:"visitMap",value:function(){return ks}}]),n}(bn));function _l(e,t){if(Oe(t))return Qt.from({nullValues:[null,void 0],type:e(),values:t});if(Be(t))return Qt.from({nullValues:[null,void 0],type:e(),values:t});var n=zt({},t),r=n.values,i=void 0===r?[]:r,a=n.type,o=void 0===a?e():a,u=n.nullValues,s=void 0===u?[null,void 0]:u;return Oe(i),Qt.from(zt(zt({nullValues:s},t),{},{type:o}))}Qt.new=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Vr(wl.getVisitFn(e)(),[e].concat(n))},Qt.from=function(e){var t=zt({nullValues:[null,void 0]},e),n=t.values,r=void 0===n?[]:n,i=zu(t,["values"]);if(Oe(r)){var a=vn(xr.throughIterable(i)(r));return 1===a.length?a[0]:Hi.concat(a)}return function(){var e=D(N.mark((function e(t){var n,a,o,u,s,l,c,f;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=xr.throughAsyncIterable(i),a=!0,o=!1,e.prev=3,s=V(n(r));case 5:return e.next=7,s.next();case 7:return l=e.sent,a=l.done,e.next=11,l.value;case 11:if(c=e.sent,a){e.next=18;break}f=c,t.push(f);case 15:a=!0,e.next=5;break;case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(3),o=!0,u=e.t0;case 24:if(e.prev=24,e.prev=25,a||null==s.return){e.next=29;break}return e.next=29,s.return();case 29:if(e.prev=29,!o){e.next=32;break}throw u;case 32:return e.finish(29);case 33:return e.finish(24);case 34:return e.abrupt("return",1===t.length?t[0]:Hi.concat(t));case 35:case"end":return e.stop()}}),e,null,[[3,20,24,34],[25,,29,33]])})));return function(t){return e.apply(this,arguments)}}()([])},ju.prototype.get=function(e){return ul.visit(this,e)},ju.prototype.set=function(e,t){return Pa.visit(this,e,t)},ju.prototype.indexOf=function(e,t){return fl.visit(this,e,t)},ju.prototype.toArray=function(){return bl.visit(this)},ju.prototype.getByteWidth=function(){return kl.visit(this.type)},ju.prototype[Symbol.iterator]=function(){return pl.visit(this)},ju.prototype._bindDataAccessors=function(){var e=this.nullBitmap;e&&e.byteLength>0&&(this.get=(t=this.get,function(e){return this.isValid(e)?t.call(this,e):null}),this.set=function(e){return function(t,n){sn(this.nullBitmap,this.offset+t,!(null===n||void 0===n))&&e.call(this,t,n)}}(this.set));var t},Object.keys(Gt).map((function(e){return Gt[e]})).filter((function(e){return"number"===typeof e})).filter((function(e){return e!==Gt.NONE})).forEach((function(e){var t,n=wl.visit(e);n.prototype.get=(t=ul.getVisitFn(e),function(e){return t(this,e)}),n.prototype.set=Rs(Pa.getVisitFn(e)),n.prototype.indexOf=Rs(fl.getVisitFn(e)),n.prototype.toArray=Us(bl.getVisitFn(e)),n.prototype.getByteWidth=function(e){return function(){return e(this.type)}}(kl.getVisitFn(e)),n.prototype[Symbol.iterator]=Us(pl.getVisitFn(e))}));var xl=function(e){ie(n,e);var t=ce(n);function n(){var e;C(this,n);for(var r=null,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];a[0]instanceof aa&&(r=a.shift());var u=Xi(Sl,a);if(!r&&!(r=u[0]&&u[0].schema))throw new TypeError("Table must be initialized with a Schema or at least one RecordBatch");return u[0]||(u[0]=new Tl(r)),(e=t.call(this,new ir(r.fields),u))._schema=r,e._chunks=u,e}return M(n,[{key:"schema",get:function(){return this._schema}},{key:"length",get:function(){return this._length}},{key:"chunks",get:function(){return this._chunks}},{key:"numCols",get:function(){return this._numChildren}},{key:"clone",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._chunks;return new n(this._schema,e)}},{key:"getColumn",value:function(e){return this.getColumnAt(this.getColumnIndex(e))}},{key:"getColumnAt",value:function(e){return this.getChildAt(e)}},{key:"getColumnIndex",value:function(e){return this._schema.fields.findIndex((function(t){return t.name===e}))}},{key:"getChildAt",value:function(e){if(e<0||e>=this.numChildren)return null;var t,n,r=this._schema.fields,i=this._children||(this._children=[]);if(n=i[e])return n;if(t=r[e]){var a=this._chunks.map((function(t){return t.getChildAt(e)})).filter((function(e){return null!=e}));if(a.length>0)return i[e]=new Qi(t,a)}return null}},{key:"serialize",value:function(){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],t=e?Ou:Bu;return t.writeAll(this).toUint8Array(!0)}},{key:"count",value:function(){return this._length}},{key:"select",value:function(){for(var e=this._schema.fields.reduce((function(e,t,n){return e.set(t.name,n)}),new Map),t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.selectAt.apply(this,vn(n.map((function(t){return e.get(t)})).filter((function(e){return e>-1}))))}},{key:"selectAt",value:function(){for(var e,t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];var a=(e=this._schema).selectAt.apply(e,r);return new n(a,this._chunks.map((function(e){var t=e.length,n=e.data.childData;return new Sl(a,t,r.map((function(e){return n[e]})).filter(Boolean))})))}},{key:"assign",value:function(e){var t=this,r=this._schema.fields,i=e.schema.fields.reduce((function(e,t,n){var i=Object(P.a)(e,2),a=i[0],o=i[1],u=r.findIndex((function(e){return e.name===t.name}));return~u?o[u]=n:a.push(n),e}),[[],[]]),a=Object(P.a)(i,2),o=a[0],u=a[1],s=this._schema.assign(e.schema),l=[].concat(vn(r.map((function(n,r,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:u[r];return void 0===a?t.getColumnAt(r):e.getColumnAt(a)}))),vn(o.map((function(t){return e.getColumnAt(t)})))).filter(Boolean);return Vr(n,vn(Uu(s,l)))}}],[{key:"empty",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new aa([]);return new n(e,[])}},{key:"from",value:function(e){if(!e)return n.empty();if("object"===typeof e){var t=Oe(e.values)?function(e){if(e.type instanceof ir)return xl.fromStruct(xs.from(e));return null}(e):Be(e.values)?function(e){if(e.type instanceof ir)return xs.from(e).then((function(e){return xl.fromStruct(e)}));return null}(e):null;if(null!==t)return t}var r=Al.from(e);return Fe(r)?D(N.mark((function e(){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.t0=n,e.next=3,r;case 3:return e.t1=e.sent,e.next=6,e.t0.from.call(e.t0,e.t1);case 6:return e.abrupt("return",e.sent);case 7:case"end":return e.stop()}}),e)})))():r.isSync()&&(r=r.open())?r.schema?new n(r.schema,vn(r)):n.empty():function(){var e=D(N.mark((function e(t){var r,i,a,o,u,s,l,c,f,d;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t;case 2:if(r=e.sent,i=r.schema,a=[],!i){e.next=40;break}o=!0,u=!1,e.prev=8,l=V(r);case 10:return e.next=12,l.next();case 12:return c=e.sent,o=c.done,e.next=16,c.value;case 16:if(f=e.sent,o){e.next=23;break}d=f,a.push(d);case 20:o=!0,e.next=10;break;case 23:e.next=29;break;case 25:e.prev=25,e.t0=e.catch(8),u=!0,s=e.t0;case 29:if(e.prev=29,e.prev=30,o||null==l.return){e.next=34;break}return e.next=34,l.return();case 34:if(e.prev=34,!u){e.next=37;break}throw s;case 37:return e.finish(34);case 38:return e.finish(29);case 39:return e.abrupt("return",new n(i,a));case 40:return e.abrupt("return",n.empty());case 41:case"end":return e.stop()}}),e,null,[[8,25,29,39],[30,,34,38]])})));return function(t){return e.apply(this,arguments)}}()(r.open())}},{key:"fromAsync",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.from(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"fromStruct",value:function(e){return n.new(e.data.childData,e.type.children)}},{key:"new",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return Vr(n,vn(Pu(Zi(t))))}}]),n}(Hi);var Sl=function(e){ie(n,e);var t=ce(n);function n(){var e,r;C(this,n);for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];var u,s=a[0];if(a[1]instanceof hr)r=a[1],u=a[2];else{var l=s.fields,c=a[1],f=a[2];r=hr.Struct(new ir(l),0,c,0,null,f)}return(e=t.call(this,r,u))._schema=s,e}return M(n,[{key:"clone",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._children;return new n(this._schema,e,t)}},{key:"concat",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var i=this._schema,a=Hi.flatten.apply(Hi,[this].concat(t));return new xl(i,a.map((function(e){var t=e.data;return new n(i,t)})))}},{key:"schema",get:function(){return this._schema}},{key:"numCols",get:function(){return this._schema.fields.length}},{key:"dictionaries",get:function(){return this._dictionaries||(this._dictionaries=Il.collect(this))}},{key:"select",value:function(){for(var e=this._schema.fields.reduce((function(e,t,n){return e.set(t.name,n)}),new Map),t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.selectAt.apply(this,vn(n.map((function(t){return e.get(t)})).filter((function(e){return e>-1}))))}},{key:"selectAt",value:function(){for(var e,t=this,r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];var o=(e=this._schema).selectAt.apply(e,i),u=i.map((function(e){return t.data.childData[e]})).filter(Boolean);return new n(o,this.length,u)}}],[{key:"from",value:function(e){return Oe(e.values),xl.from(e)}},{key:"new",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var i=Ji(t),a=Object(P.a)(i,2),o=a[0],u=a[1],s=u.filter((function(e){return e instanceof Qt}));return Vr(n,vn(Nu(new aa(o),s.map((function(e){return e.data})))))}}]),n}(xs),Tl=function(e){ie(n,e);var t=ce(n);function n(e){return C(this,n),t.call(this,e,0,e.fields.map((function(e){return hr.new(e.type,0,0,0)})))}return n}(Sl),Il=function(e){ie(n,e);var t=ce(n);function n(){var e;return C(this,n),(e=t.apply(this,arguments)).dictionaries=new Map,e}return M(n,[{key:"visit",value:function(e,t){var n=this;return Dn.isDictionary(t)?this.visitDictionary(e,t):(e.childData.forEach((function(e,r){return n.visit(e,t.children[r].type)})),this)}},{key:"visitDictionary",value:function(e,t){var n=e.dictionary;return n&&n.length>0&&this.dictionaries.set(t.id,n),this}}],[{key:"collect",value:function(e){return(new n).visit(e.data,new ir(e.schema.fields)).dictionaries}}]),n}(bn),El=N.mark(zl),Al=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this))._impl=e,r}return M(n,[{key:"closed",get:function(){return this._impl.closed}},{key:"schema",get:function(){return this._impl.schema}},{key:"autoDestroy",get:function(){return this._impl.autoDestroy}},{key:"dictionaries",get:function(){return this._impl.dictionaries}},{key:"numDictionaries",get:function(){return this._impl.numDictionaries}},{key:"numRecordBatches",get:function(){return this._impl.numRecordBatches}},{key:"footer",get:function(){return this._impl.isFile()?this._impl.footer:null}},{key:"isSync",value:function(){return this._impl.isSync()}},{key:"isAsync",value:function(){return this._impl.isAsync()}},{key:"isFile",value:function(){return this._impl.isFile()}},{key:"isStream",value:function(){return this._impl.isStream()}},{key:"next",value:function(){return this._impl.next()}},{key:"throw",value:function(e){return this._impl.throw(e)}},{key:"return",value:function(e){return this._impl.return(e)}},{key:"cancel",value:function(){return this._impl.cancel()}},{key:"reset",value:function(e){return this._impl.reset(e),this._DOMStream=void 0,this._nodeStream=void 0,this}},{key:"open",value:function(e){var t=this,n=this._impl.open(e);return Fe(n)?n.then((function(){return t})):this}},{key:"readRecordBatch",value:function(e){return this._impl.isFile()?this._impl.readRecordBatch(e):null}},{key:Symbol.iterator,value:function(){return this._impl[Symbol.iterator]()}},{key:Symbol.asyncIterator,value:function(){return this._impl[Symbol.asyncIterator]()}},{key:"toDOMStream",value:function(){var e=this;return At.toDOMStream(this.isSync()?Object(Ut.a)({},Symbol.iterator,(function(){return e})):Object(Ut.a)({},Symbol.asyncIterator,(function(){return e})))}},{key:"toNodeStream",value:function(){var e=this;return At.toNodeStream(this.isSync()?Object(Ut.a)({},Symbol.iterator,(function(){return e})):Object(Ut.a)({},Symbol.asyncIterator,(function(){return e})),{objectMode:!0})}}],[{key:"throughNode",value:function(e){throw new Error('"throughNode" not available in this environment')}},{key:"throughDOM",value:function(e,t){throw new Error('"throughDOM" not available in this environment')}},{key:"from",value:function(e){return e instanceof n?e:De(e)?function(e){return new Fl(new Ul(e))}(e):Le(e)?function(e){return Hl.apply(this,arguments)}(e):Fe(e)?D(N.mark((function t(){return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.t0=n,t.next=3,e;case 3:return t.t1=t.sent,t.next=6,t.t0.from.call(t.t0,t.t1);case 6:return t.abrupt("return",t.sent);case 7:case"end":return t.stop()}}),t)})))():Me(e)||Pe(e)||Re(e)||Be(e)?function(e){return Wl.apply(this,arguments)}(new Ga(e)):function(e){var t=e.peek(ku+7&-8);return t&&t.byteLength>=4?gu(t)?new Bl(new Nl(e.read())):new Fl(new Ll(e)):new Fl(new Ll(N.mark((function e(){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)}))()))}(new Qa(e))}},{key:"readAll",value:function(e){return e instanceof n?e.isSync()?zl(e):jl(e):De(e)||ArrayBuffer.isView(e)||Oe(e)||Ce(e)?zl(e):jl(e)}}]),n}(he),Fl=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._impl=e,r}return M(n,[{key:Symbol.iterator,value:function(){return this._impl[Symbol.iterator]()}},{key:Symbol.asyncIterator,value:function(){var e=this;return j(N.mark((function t(){return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.delegateYield(W(V(e[Symbol.iterator]()),R),"t0",1);case 1:case"end":return t.stop()}}),t)})))()}}]),n}(Al),Ol=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._impl=e,r}return M(n,[{key:Symbol.iterator,value:function(){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}},{key:Symbol.asyncIterator,value:function(){return this._impl[Symbol.asyncIterator]()}}]),n}(Al),Bl=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._impl=e,r}return n}(Fl),Dl=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this,e))._impl=e,r}return n}(Ol),Cl=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Map;C(this,e),this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}return M(e,[{key:"numDictionaries",get:function(){return this._dictionaryIndex}},{key:"numRecordBatches",get:function(){return this._recordBatchIndex}},{key:"isSync",value:function(){return!1}},{key:"isAsync",value:function(){return!1}},{key:"isFile",value:function(){return!1}},{key:"isStream",value:function(){return!1}},{key:"reset",value:function(e){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=e,this.dictionaries=new Map,this}},{key:"_loadRecordBatch",value:function(e,t){return new Sl(this.schema,e.length,this._loadVectors(e,t,this.schema.fields))}},{key:"_loadDictionaryBatch",value:function(e,t){var n=e.id,r=e.isDelta,i=e.data,a=this.dictionaries,o=this.schema,u=a.get(n);if(r||!u){var s=o.dictionaries.get(n);return u&&r?u.concat(Qt.new(this._loadVectors(i,t,[s])[0])):Qt.new(this._loadVectors(i,t,[s])[0])}return u}},{key:"_loadVectors",value:function(e,t,n){return new oo(t,e.nodes,e.buffers,this.dictionaries).visitMany(n)}}]),e}(),Ll=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this,r))._reader=De(e)?new yu(i._handle=e):new hu(i._handle=e),i}return M(n,[{key:"isSync",value:function(){return!0}},{key:"isStream",value:function(){return!0}},{key:Symbol.iterator,value:function(){return this}},{key:"cancel",value:function(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}},{key:"open",value:function(e){return this.closed||(this.autoDestroy=Rl(this,e),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}},{key:"throw",value:function(e){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(e):fe}},{key:"return",value:function(e){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(e):fe}},{key:"next",value:function(){if(this.closed)return fe;for(var e,t=this._reader;e=this._readNextMessageAndValidate();)if(e.isSchema())this.reset(e.header());else{if(e.isRecordBatch()){this._recordBatchIndex++;var n=e.header(),r=t.readMessageBody(e.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(e.isDictionaryBatch()){this._dictionaryIndex++;var i=e.header(),a=t.readMessageBody(e.bodyLength),o=this._loadDictionaryBatch(i,a);this.dictionaries.set(i.id,o)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new Tl(this.schema)}):this.return()}},{key:"_readNextMessageAndValidate",value:function(e){return this._reader.readMessage(e)}}]),n}(Cl),Ml=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this,r))._reader=new pu(i._handle=e),i}return M(n,[{key:"isAsync",value:function(){return!0}},{key:"isStream",value:function(){return!0}},{key:Symbol.asyncIterator,value:function(){return this}},{key:"cancel",value:function(){var e=D(N.mark((function e(){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed||!(this.closed=!0)){e.next=5;break}return e.next=3,this.reset()._reader.return();case 3:this._reader=null,this.dictionaries=null;case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"open",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed){e.next=10;break}if(this.autoDestroy=Rl(this,t),e.t0=this.schema,e.t0){e.next=7;break}return e.next=6,this._reader.readSchema();case 6:e.t0=this.schema=e.sent;case 7:if(e.t0){e.next=10;break}return e.next=10,this.cancel();case 10:return e.abrupt("return",this);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"throw",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed||!this.autoDestroy||!(this.closed=!0)){e.next=4;break}return e.next=3,this.reset()._reader.throw(t);case 3:return e.abrupt("return",e.sent);case 4:return e.abrupt("return",fe);case 5:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed||!this.autoDestroy||!(this.closed=!0)){e.next=4;break}return e.next=3,this.reset()._reader.return(t);case 3:return e.abrupt("return",e.sent);case 4:return e.abrupt("return",fe);case 5:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"next",value:function(){var e=D(N.mark((function e(){var t,n,r,i,a,o,u,s;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.closed){e.next=2;break}return e.abrupt("return",fe);case 2:n=this._reader;case 3:return e.next=5,this._readNextMessageAndValidate();case 5:if(!(t=e.sent)){e.next=31;break}if(!t.isSchema()){e.next=11;break}return e.next=9,this.reset(t.header());case 9:e.next=29;break;case 11:if(!t.isRecordBatch()){e.next=21;break}return this._recordBatchIndex++,r=t.header(),e.next=16,n.readMessageBody(t.bodyLength);case 16:return i=e.sent,a=this._loadRecordBatch(r,i),e.abrupt("return",{done:!1,value:a});case 21:if(!t.isDictionaryBatch()){e.next=29;break}return this._dictionaryIndex++,o=t.header(),e.next=26,n.readMessageBody(t.bodyLength);case 26:u=e.sent,s=this._loadDictionaryBatch(o,u),this.dictionaries.set(o.id,s);case 29:e.next=3;break;case 31:if(!this.schema||0!==this._recordBatchIndex){e.next=34;break}return this._recordBatchIndex++,e.abrupt("return",{done:!1,value:new Tl(this.schema)});case 34:return e.next=36,this.return();case 36:return e.abrupt("return",e.sent);case 37:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_readNextMessageAndValidate",value:function(){var e=D(N.mark((function e(t){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._reader.readMessage(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(Cl),Nl=function(e){ie(n,e);var t=ce(n);function n(e,r){return C(this,n),t.call(this,e instanceof Za?e:new Za(e),r)}return M(n,[{key:"footer",get:function(){return this._footer}},{key:"numDictionaries",get:function(){return this._footer?this._footer.numDictionaries:0}},{key:"numRecordBatches",get:function(){return this._footer?this._footer.numRecordBatches:0}},{key:"isSync",value:function(){return!0}},{key:"isFile",value:function(){return!0}},{key:"open",value:function(e){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;var t,r=O(this._footer.dictionaryBatches());try{for(r.s();!(t=r.n()).done;){t.value&&this._readDictionaryBatch(this._dictionaryIndex++)}}catch(i){r.e(i)}finally{r.f()}}return jt(ae(n.prototype),"open",this).call(this,e)}},{key:"readRecordBatch",value:function(e){if(this.closed)return null;this._footer||this.open();var t=this._footer&&this._footer.getRecordBatch(e);if(t&&this._handle.seek(t.offset)){var n=this._reader.readMessage(nn.RecordBatch);if(n&&n.isRecordBatch()){var r=n.header(),i=this._reader.readMessageBody(n.bodyLength);return this._loadRecordBatch(r,i)}}return null}},{key:"_readDictionaryBatch",value:function(e){var t=this._footer&&this._footer.getDictionaryBatch(e);if(t&&this._handle.seek(t.offset)){var n=this._reader.readMessage(nn.DictionaryBatch);if(n&&n.isDictionaryBatch()){var r=n.header(),i=this._reader.readMessageBody(n.bodyLength),a=this._loadDictionaryBatch(r,i);this.dictionaries.set(r.id,a)}}}},{key:"_readFooter",value:function(){var e=this._handle,t=e.size-wu,n=e.readInt32(t),r=e.readAt(t-n,n);return Ha.decode(r)}},{key:"_readNextMessageAndValidate",value:function(e){if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){var t=this._footer&&this._footer.getRecordBatch(this._recordBatchIndex);if(t&&this._handle.seek(t.offset))return this._reader.readMessage(e)}return null}}]),n}(Ll),Pl=function(e){ie(n,e);var t=ce(n);function n(e){C(this,n);for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];var o="number"!==typeof i[0]?i.shift():void 0,u=i[0]instanceof Map?i.shift():void 0;return t.call(this,e instanceof Ja?e:new Ja(e,o),u)}return M(n,[{key:"footer",get:function(){return this._footer}},{key:"numDictionaries",get:function(){return this._footer?this._footer.numDictionaries:0}},{key:"numRecordBatches",get:function(){return this._footer?this._footer.numRecordBatches:0}},{key:"isFile",value:function(){return!0}},{key:"isAsync",value:function(){return!0}},{key:"open",value:function(){var e=D(N.mark((function e(t){var r,i,a;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed||this._footer){e.next=23;break}return e.next=3,this._readFooter();case 3:this.schema=(this._footer=e.sent).schema,r=O(this._footer.dictionaryBatches()),e.prev=5,r.s();case 7:if((i=r.n()).done){e.next=15;break}if(a=i.value,e.t0=a,!e.t0){e.next=13;break}return e.next=13,this._readDictionaryBatch(this._dictionaryIndex++);case 13:e.next=7;break;case 15:e.next=20;break;case 17:e.prev=17,e.t1=e.catch(5),r.e(e.t1);case 20:return e.prev=20,r.f(),e.finish(20);case 23:return e.next=25,jt(ae(n.prototype),"open",this).call(this,t);case 25:return e.abrupt("return",e.sent);case 26:case"end":return e.stop()}}),e,this,[[5,17,20,23]])})));return function(t){return e.apply(this,arguments)}}()},{key:"readRecordBatch",value:function(){var e=D(N.mark((function e(t){var n,r,i,a,o;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.closed){e.next=2;break}return e.abrupt("return",null);case 2:if(this._footer){e.next=5;break}return e.next=5,this.open();case 5:if(n=this._footer&&this._footer.getRecordBatch(t),e.t0=n,!e.t0){e.next=11;break}return e.next=10,this._handle.seek(n.offset);case 10:e.t0=e.sent;case 11:if(!e.t0){e.next=22;break}return e.next=14,this._reader.readMessage(nn.RecordBatch);case 14:if(!(r=e.sent)||!r.isRecordBatch()){e.next=22;break}return i=r.header(),e.next=19,this._reader.readMessageBody(r.bodyLength);case 19:return a=e.sent,o=this._loadRecordBatch(i,a),e.abrupt("return",o);case 22:return e.abrupt("return",null);case 23:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_readDictionaryBatch",value:function(){var e=D(N.mark((function e(t){var n,r,i,a,o;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this._footer&&this._footer.getDictionaryBatch(t),e.t0=n,!e.t0){e.next=6;break}return e.next=5,this._handle.seek(n.offset);case 5:e.t0=e.sent;case 6:if(!e.t0){e.next=17;break}return e.next=9,this._reader.readMessage(nn.DictionaryBatch);case 9:if(!(r=e.sent)||!r.isDictionaryBatch()){e.next=17;break}return i=r.header(),e.next=14,this._reader.readMessageBody(r.bodyLength);case 14:a=e.sent,o=this._loadDictionaryBatch(i,a),this.dictionaries.set(i.id,o);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_readFooter",value:function(){var e=D(N.mark((function e(){var t,n,r,i;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this._handle,e.t0=t._pending,!e.t0){e.next=5;break}return e.next=5,t._pending;case 5:return n=t.size-wu,e.next=8,t.readInt32(n);case 8:return r=e.sent,e.next=11,t.readAt(n-r,r);case 11:return i=e.sent,e.abrupt("return",Ha.decode(i));case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_readNextMessageAndValidate",value:function(){var e=D(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._footer){e.next=3;break}return e.next=3,this.open();case 3:if(!(this._footer&&this._recordBatchIndex<this.numRecordBatches)){e.next=14;break}if(n=this._footer.getRecordBatch(this._recordBatchIndex),e.t0=n,!e.t0){e.next=10;break}return e.next=9,this._handle.seek(n.offset);case 9:e.t0=e.sent;case 10:if(!e.t0){e.next=14;break}return e.next=13,this._reader.readMessage(t);case 13:return e.abrupt("return",e.sent);case 14:return e.abrupt("return",null);case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(Ml),Ul=function(e){ie(n,e);var t=ce(n);function n(e,r){return C(this,n),t.call(this,e,r)}return M(n,[{key:"_loadVectors",value:function(e,t,n){return new uo(t,e.nodes,e.buffers,this.dictionaries).visitMany(n)}}]),n}(Ll);function Rl(e,t){return t&&"boolean"===typeof t.autoDestroy?t.autoDestroy:e.autoDestroy}function zl(e){var t;return N.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(t=Al.from(e),n.prev=1,t.open({autoDestroy:!1}).closed){n.next=6;break}case 3:return n.next=5,t;case 5:if(!t.reset().open().closed){n.next=3;break}case 6:return n.prev=6,t.cancel(),n.finish(6);case 9:case"end":return n.stop()}}),El,null,[[1,,6,9]])}function jl(e){return Vl.apply(this,arguments)}function Vl(){return(Vl=j(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,R(Al.from(t));case 2:return n=e.sent,e.prev=3,e.next=6,R(n.open({autoDestroy:!1}));case 6:if(e.sent.closed){e.next=12;break}case 7:return e.next=9,n;case 9:return e.next=11,R(n.reset().open());case 11:if(!e.sent.closed){e.next=7;break}case 12:return e.prev=12,e.next=15,R(n.cancel());case 15:return e.finish(12);case 16:case"end":return e.stop()}}),e,null,[[3,,12,16]])})))).apply(this,arguments)}function Wl(){return(Wl=D(N.mark((function e(t){var n;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.peek(ku+7&-8);case 2:if(!((n=e.sent)&&n.byteLength>=4)){e.next=18;break}if(gu(n)){e.next=8;break}e.t1=new Ol(new Ml(t)),e.next=15;break;case 8:return e.t2=Bl,e.t3=Nl,e.next=12,t.read();case 12:e.t4=e.sent,e.t5=new e.t3(e.t4),e.t1=new e.t2(e.t5);case 15:e.t0=e.t1,e.next=19;break;case 18:e.t0=new Ol(new Ml(j(N.mark((function e(){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))()));case 19:return e.abrupt("return",e.t0);case 20:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function Hl(){return(Hl=D(N.mark((function e(t){var n,r,i;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.stat();case 2:if(n=e.sent,r=n.size,i=new Ja(t,r),!(r>=_u)){e.next=12;break}return e.t0=gu,e.next=9,i.readAt(0,ku+7&-8);case 9:if(e.t1=e.sent,!(0,e.t0)(e.t1)){e.next=12;break}return e.abrupt("return",new Dl(new Pl(i)));case 12:return e.abrupt("return",new Ol(new Ml(i)));case 13:case"end":return e.stop()}}),e)})))).apply(this,arguments)}var Yl=function(){function e(t){var n,r,i=this;C(this,e),this._numChunks=0,this._finished=!1,this._bufferedSize=0;var a=t.readableStrategy,o=t.writableStrategy,u=t.queueingStrategy,s=void 0===u?"count":u,l=zu(t,["readableStrategy","writableStrategy","queueingStrategy"]);this._controller=null,this._builder=xr.new(l),this._getSize="bytes"!==s?$l:Kl;var c=zt({},a).highWaterMark,f=void 0===c?"bytes"===s?Math.pow(2,14):1e3:c,d=zt({},o).highWaterMark,h=void 0===d?"bytes"===s?Math.pow(2,14):1e3:d;this.readable=new ReadableStream((n={},Object(Ut.a)(n,"cancel",(function(){i._builder.clear()})),Object(Ut.a)(n,"pull",(function(e){i._maybeFlush(i._builder,i._controller=e)})),Object(Ut.a)(n,"start",(function(e){i._maybeFlush(i._builder,i._controller=e)})),n),{highWaterMark:f,size:"bytes"!==s?$l:Kl}),this.writable=new WritableStream((r={},Object(Ut.a)(r,"abort",(function(){i._builder.clear()})),Object(Ut.a)(r,"write",(function(){i._maybeFlush(i._builder,i._controller)})),Object(Ut.a)(r,"close",(function(){i._maybeFlush(i._builder.finish(),i._controller)})),r),{highWaterMark:h,size:function(e){return i._writeValueAndReturnChunkSize(e)}})}return M(e,[{key:"_writeValueAndReturnChunkSize",value:function(e){var t=this._bufferedSize;return this._bufferedSize=this._getSize(this._builder.append(e)),this._bufferedSize-t}},{key:"_maybeFlush",value:function(e,t){null!==t&&(this._bufferedSize>=t.desiredSize&&++this._numChunks&&this._enqueue(t,e.toVector()),e.finished&&((e.length>0||0===this._numChunks)&&++this._numChunks&&this._enqueue(t,e.toVector()),!this._finished&&(this._finished=!0)&&this._enqueue(t,null)))}},{key:"_enqueue",value:function(e,t){this._bufferedSize=0,this._controller=null,null===t?e.close():e.enqueue(t)}}]),e}(),$l=function(e){return e.length},Kl=function(e){return e.byteLength};var Ql=function(){function e(){C(this,e)}return M(e,[{key:"eq",value:function(t){return t instanceof e||(t=new Gl(t)),new nc(this,t)}},{key:"le",value:function(t){return t instanceof e||(t=new Gl(t)),new rc(this,t)}},{key:"ge",value:function(t){return t instanceof e||(t=new Gl(t)),new ic(this,t)}},{key:"lt",value:function(e){return new ac(this.ge(e))}},{key:"gt",value:function(e){return new ac(this.le(e))}},{key:"ne",value:function(e){return new ac(this.eq(e))}}]),e}(),Gl=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this)).v=e,r}return n}(Ql),ql=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this)).name=e,r}return M(n,[{key:"bind",value:function(e){if(!this.colidx){this.colidx=-1;for(var t=e.schema.fields,n=-1;++n<t.length;)if(t[n].name===this.name){this.colidx=n;break}if(this.colidx<0)throw new Error('Failed to bind Col "'.concat(this.name,'"'))}var r=this.vector=e.getChildAt(this.colidx);return function(e){return r.get(e)}}}]),n}(Ql),Xl=function(){function e(){C(this,e)}return M(e,[{key:"and",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Vr(ec,[this].concat(t))}},{key:"or",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Vr(tc,[this].concat(t))}},{key:"not",value:function(){return new ac(this)}}]),e}(),Zl=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this)).left=e,i.right=r,i}return M(n,[{key:"bind",value:function(e){return this.left instanceof Gl?this.right instanceof Gl?this._bindLitLit(e,this.left,this.right):this._bindLitCol(e,this.left,this.right):this.right instanceof Gl?this._bindColLit(e,this.left,this.right):this._bindColCol(e,this.left,this.right)}}]),n}(Xl),Jl=function(e){ie(n,e);var t=ce(n);function n(){var e;C(this,n),e=t.call(this);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return e.children=i,e}return n}(Xl);Jl.prototype.children=Object.freeze([]);var ec=function(e){ie(n,e);var t=ce(n);function n(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return C(this,n),r=r.reduce((function(e,t){return e.concat(t instanceof n?t.children:t)}),[]),t.call.apply(t,[this].concat(vn(r)))}return M(n,[{key:"bind",value:function(e){var t=this.children.map((function(t){return t.bind(e)}));return function(e,n){return t.every((function(t){return t(e,n)}))}}}]),n}(Jl),tc=function(e){ie(n,e);var t=ce(n);function n(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return C(this,n),r=r.reduce((function(e,t){return e.concat(t instanceof n?t.children:t)}),[]),t.call.apply(t,[this].concat(vn(r)))}return M(n,[{key:"bind",value:function(e){var t=this.children.map((function(t){return t.bind(e)}));return function(e,n){return t.some((function(t){return t(e,n)}))}}}]),n}(Jl),nc=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"_bindLitLit",value:function(e,t,n){var r=t.v==n.v;return function(){return r}}},{key:"_bindColCol",value:function(e,t,n){var r=t.bind(e),i=n.bind(e);return function(e,t){return r(e,t)==i(e,t)}}},{key:"_bindColLit",value:function(e,t,n){var r=t.bind(e);if(t.vector instanceof Qu){var i,a=t.vector;return a.dictionary!==this.lastDictionary?(i=a.reverseLookup(n.v),this.lastDictionary=a.dictionary,this.lastKey=i):i=this.lastKey,-1===i?function(){return!1}:function(e){return a.getKey(e)===i}}return function(e,t){return r(e,t)==n.v}}},{key:"_bindLitCol",value:function(e,t,n){return this._bindColLit(e,n,t)}}]),n}(Zl),rc=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"_bindLitLit",value:function(e,t,n){var r=t.v<=n.v;return function(){return r}}},{key:"_bindColCol",value:function(e,t,n){var r=t.bind(e),i=n.bind(e);return function(e,t){return r(e,t)<=i(e,t)}}},{key:"_bindColLit",value:function(e,t,n){var r=t.bind(e);return function(e,t){return r(e,t)<=n.v}}},{key:"_bindLitCol",value:function(e,t,n){var r=n.bind(e);return function(e,n){return t.v<=r(e,n)}}}]),n}(Zl),ic=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"_bindLitLit",value:function(e,t,n){var r=t.v>=n.v;return function(){return r}}},{key:"_bindColCol",value:function(e,t,n){var r=t.bind(e),i=n.bind(e);return function(e,t){return r(e,t)>=i(e,t)}}},{key:"_bindColLit",value:function(e,t,n){var r=t.bind(e);return function(e,t){return r(e,t)>=n.v}}},{key:"_bindLitCol",value:function(e,t,n){var r=n.bind(e);return function(e,n){return t.v>=r(e,n)}}}]),n}(Zl),ac=function(e){ie(n,e);var t=ce(n);function n(e){var r;return C(this,n),(r=t.call(this)).child=e,r}return M(n,[{key:"bind",value:function(e){var t=this.child.bind(e);return function(e,n){return!t(e,n)}}}]),n}(Xl);xl.prototype.countBy=function(e){return new oc(this.chunks).countBy(e)},xl.prototype.scan=function(e,t){return new oc(this.chunks).scan(e,t)},xl.prototype.scanReverse=function(e,t){return new oc(this.chunks).scanReverse(e,t)},xl.prototype.filter=function(e){return new oc(this.chunks).filter(e)};var oc=function(e){ie(n,e);var t=ce(n);function n(){return C(this,n),t.apply(this,arguments)}return M(n,[{key:"filter",value:function(e){return new sc(this.chunks,e)}},{key:"scan",value:function(e,t){for(var n=this.chunks,r=n.length,i=-1;++i<r;){var a=n[i];t&&t(a);for(var o=-1,u=a.length;++o<u;)e(o,a)}}},{key:"scanReverse",value:function(e,t){for(var n=this.chunks,r=n.length;--r>=0;){var i=n[r];t&&t(i);for(var a=i.length;--a>=0;)e(a,i)}}},{key:"countBy",value:function(e){var t=this.chunks,n=t.length,r="string"===typeof e?new ql(e):e;r.bind(t[n-1]);var i=r.vector;if(!Dn.isDictionary(i.type))throw new Error("countBy currently only supports dictionary-encoded columns");for(var a=Math.ceil(Math.log(i.length)/Math.log(256)),o=new(4==a?Uint32Array:a>=2?Uint16Array:Uint8Array)(i.dictionary.length),u=-1;++u<n;){var s=t[u];r.bind(s);for(var l=r.vector.indices,c=-1,f=s.length;++c<f;){var d=l.get(c);null!==d&&o[d]++}}return new uc(i.dictionary,us.from(o))}}]),n}(xl),uc=function(e){ie(n,e);var t=ce(n);function n(e,r){C(this,n);var i=new aa([new oa("values",e.type),new oa("counts",r.type)]);return t.call(this,new Sl(i,r.length,[e,r]))}return M(n,[{key:"toJSON",value:function(){for(var e=this.getColumnAt(0),t=this.getColumnAt(1),n={},r=-1;++r<this.length;)n[e.get(r)]=t.get(r);return n}}]),n}(xl),sc=function(e){ie(n,e);var t=ce(n);function n(e,r){var i;return C(this,n),(i=t.call(this,e))._predicate=r,i}return M(n,[{key:"scan",value:function(e,t){for(var n=this._chunks,r=n.length,i=-1;++i<r;)for(var a=n[i],o=this._predicate.bind(a),u=!1,s=-1,l=a.length;++s<l;)o(s,a)&&(t&&!u&&(t(a),u=!0),e(s,a))}},{key:"scanReverse",value:function(e,t){for(var n=this._chunks,r=n.length;--r>=0;)for(var i=n[r],a=this._predicate.bind(i),o=!1,u=i.length;--u>=0;)a(u,i)&&(t&&!o&&(t(i),o=!0),e(u,i))}},{key:"count",value:function(){for(var e=0,t=this._chunks,n=t.length,r=-1;++r<n;)for(var i=t[r],a=this._predicate.bind(i),o=-1,u=i.length;++o<u;)a(o,i)&&++e;return e}},{key:Symbol.iterator,value:N.mark((function e(){var t,n,r,i,a,o,u;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this._chunks,n=t.length,r=-1;case 3:if(!(++r<n)){e.next=15;break}i=t[r],a=this._predicate.bind(i),o=-1,u=i.length;case 7:if(!(++o<u)){e.next=13;break}if(!a(o,i)){e.next=11;break}return e.next=11,i.get(o);case 11:e.next=7;break;case 13:e.next=3;break;case 15:case"end":return e.stop()}}),e,this)}))},{key:"filter",value:function(e){return new n(this._chunks,this._predicate.and(e))}},{key:"countBy",value:function(e){var t=this._chunks,n=t.length,r="string"===typeof e?new ql(e):e;r.bind(t[n-1]);var i=r.vector;if(!Dn.isDictionary(i.type))throw new Error("countBy currently only supports dictionary-encoded columns");for(var a=Math.ceil(Math.log(i.length)/Math.log(256)),o=new(4==a?Uint32Array:a>=2?Uint16Array:Uint8Array)(i.dictionary.length),u=-1;++u<n;){var s=t[u],l=this._predicate.bind(s);r.bind(s);for(var c=r.vector.indices,f=-1,d=s.length;++f<d;){var h=c.get(f);null!==h&&l(f,s)&&o[h]++}}return new uc(i.dictionary,us.from(o))}}]),n}(oc);zt(zt(zt(zt(zt(zt({},o),s),i),a),r),u);At.toDOMStream=function(e,t){if(Be(e))return function(e,t){var n=null,r=t&&"bytes"===t.type||!1,i=t&&t.highWaterMark||Math.pow(2,24);return new ReadableStream(zt(zt({},t),{},{start:function(t){return D(N.mark((function r(){return N.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,a(t,n||(n=e[Symbol.asyncIterator]()));case 2:case"end":return r.stop()}}),r)})))()},pull:function(e){return D(N.mark((function t(){return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!n){t.next=5;break}return t.next=3,a(e,n);case 3:t.next=6;break;case 5:e.close();case 6:case"end":return t.stop()}}),t)})))()},cancel:function(){return D(N.mark((function e(){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t1=n,!e.t1){e.next=8;break}if(e.t2=n.return,!e.t2){e.next=7;break}return e.next=6,n.return();case 6:e.t2=e.sent;case 7:e.t1=e.t2;case 8:if(e.t0=e.t1,e.t0){e.next=11;break}e.t0=!0;case 11:if(e.t3=e.t0,!e.t3){e.next=14;break}n=null;case 14:case"end":return e.stop()}}),e)})))()}}),zt({highWaterMark:r?i:void 0},t));function a(e,t){return o.apply(this,arguments)}function o(){return(o=D(N.mark((function e(t,n){var i,a,o;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:a=null,o=t.desiredSize||null;case 2:return e.next=4,n.next(r?o:null);case 4:if((a=e.sent).done){e.next=11;break}if(ArrayBuffer.isView(a.value)&&(i=qe(a.value))&&(null!=o&&r&&(o=o-i.byteLength+1),a.value=i),t.enqueue(a.value),!(null!=o&&--o<=0)){e.next=9;break}return e.abrupt("return");case 9:e.next=2;break;case 11:t.close();case 12:case"end":return e.stop()}}),e)})))).apply(this,arguments)}}(e,t);if(Oe(e))return function(e,t){var n=null,r=t&&"bytes"===t.type||!1,i=t&&t.highWaterMark||Math.pow(2,24);return new ReadableStream(zt(zt({},t),{},{start:function(t){a(t,n||(n=e[Symbol.iterator]()))},pull:function(e){n?a(e,n):e.close()},cancel:function(){n&&n.return&&n.return(),n=null}}),zt({highWaterMark:r?i:void 0},t));function a(e,t){for(var n,i=null,a=e.desiredSize||null;!(i=t.next(r?a:null)).done;)if(ArrayBuffer.isView(i.value)&&(n=qe(i.value))&&(null!=a&&r&&(a=a-n.byteLength+1),i.value=n),e.enqueue(i.value),null!=a&&--a<=0)return;e.close()}}(e,t);throw new Error("toDOMStream() must be called with an Iterable or AsyncIterable")},xr.throughDOM=function(e){return new Yl(e)},Al.throughDOM=function(e,t){var n=new Ka,r=null,i=new ReadableStream({cancel:function(){return D(N.mark((function e(){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.close();case 2:case"end":return e.stop()}}),e)})))()},start:function(e){return D(N.mark((function t(){return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.t0=u,t.t1=e,t.t2=r,t.t2){t.next=7;break}return t.next=6,a();case 6:t.t2=r=t.sent;case 7:return t.t3=t.t2,t.next=10,(0,t.t0)(t.t1,t.t3);case 10:case"end":return t.stop()}}),t)})))()},pull:function(e){return D(N.mark((function t(){return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!r){t.next=5;break}return t.next=3,u(e,r);case 3:t.next=6;break;case 5:e.close();case 6:case"end":return t.stop()}}),t)})))()}});return{writable:new WritableStream(n,zt({highWaterMark:Math.pow(2,14)},e)),readable:i};function a(){return o.apply(this,arguments)}function o(){return(o=D(N.mark((function e(){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Al.from(n);case 2:return e.next=4,e.sent.open(t);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function u(e,t){return s.apply(this,arguments)}function s(){return(s=D(N.mark((function e(t,n){var r,i;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=t.desiredSize,i=null;case 2:return e.next=4,n.next();case 4:if((i=e.sent).done){e.next=10;break}if(t.enqueue(i.value),!(null!=r&&--r<=0)){e.next=8;break}return e.abrupt("return");case 8:e.next=2;break;case 10:t.close();case 11:case"end":return e.stop()}}),e)})))).apply(this,arguments)}},Fu.throughDOM=function(e,t){var n=new this(e),r=new Ga(n),i=new ReadableStream({type:"bytes",cancel:function(){return D(N.mark((function e(){return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.cancel();case 2:case"end":return e.stop()}}),e)})))()},pull:function(e){return D(N.mark((function t(){return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,a(e);case 2:case"end":return t.stop()}}),t)})))()},start:function(e){return D(N.mark((function t(){return N.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,a(e);case 2:case"end":return t.stop()}}),t)})))()}},zt({highWaterMark:Math.pow(2,14)},t));return{writable:new WritableStream(n,e),readable:i};function a(e){return o.apply(this,arguments)}function o(){return(o=D(N.mark((function e(t){var n,i;return N.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=null,i=t.desiredSize;case 2:return e.next=4,r.read(i||null);case 4:if(!(n=e.sent)){e.next=10;break}if(t.enqueue(n),!(null!=i&&(i-=n.byteLength)<=0)){e.next=8;break}return e.abrupt("return");case 8:e.next=2;break;case 10:t.close();case 11:case"end":return e.stop()}}),e)})))).apply(this,arguments)}};var lc,cc=function(){function e(e,t,n,r){var i=this;this.getCell=function(e,t){var n=e<i.headerRows&&t<i.headerColumns,r=e>=i.headerRows&&t<i.headerColumns,a=e<i.headerRows&&t>=i.headerColumns;if(n){var o=["blank"];return t>0&&o.push("level"+e),{type:"blank",classNames:o.join(" "),content:""}}if(a)return{type:"columns",classNames:(o=["col_heading","level"+e,"col"+(s=t-i.headerColumns)]).join(" "),content:i.getContent(i.columnsTable,s,e)};if(r){o=["row_heading","level"+t,"row"+(u=e-i.headerRows)];return{type:"index",id:"T_"+i.uuid+"level"+t+"_row"+u,classNames:o.join(" "),content:i.getContent(i.indexTable,u,t)}}o=["data","row"+(u=e-i.headerRows),"col"+(s=t-i.headerColumns)];var u,s,l=i.styler?i.getContent(i.styler.displayValuesTable,u,s):i.getContent(i.dataTable,u,s);return{type:"data",id:"T_"+i.uuid+"row"+u+"_col"+s,classNames:o.join(" "),content:l}},this.getContent=function(e,t,n){var r=e.getColumnAt(n);if(null===r)return"";switch(i.getColumnTypeId(e,n)){case Gt.Timestamp:return i.nanosToDate(r.get(t));default:return r.get(t)}},this.dataTable=xl.from(e),this.indexTable=xl.from(t),this.columnsTable=xl.from(n),this.styler=r?{caption:r.caption,displayValuesTable:xl.from(r.displayValues),styles:r.styles,uuid:r.uuid}:void 0}return Object.defineProperty(e.prototype,"rows",{get:function(){return this.indexTable.length+this.columnsTable.numCols},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"columns",{get:function(){return this.indexTable.numCols+this.columnsTable.length},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"headerRows",{get:function(){return this.rows-this.dataRows},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"headerColumns",{get:function(){return this.columns-this.dataColumns},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dataRows",{get:function(){return this.dataTable.length},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dataColumns",{get:function(){return this.dataTable.numCols},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"uuid",{get:function(){return this.styler&&this.styler.uuid},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"caption",{get:function(){return this.styler&&this.styler.caption},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"styles",{get:function(){return this.styler&&this.styler.styles},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"table",{get:function(){return this.dataTable},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"index",{get:function(){return this.indexTable},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"columnTable",{get:function(){return this.columnsTable},enumerable:!0,configurable:!0}),e.prototype.serialize=function(){return{data:this.dataTable.serialize(),index:this.indexTable.serialize(),columns:this.columnsTable.serialize()}},e.prototype.getColumnTypeId=function(e,t){return e.schema.fields[t].type.typeId},e.prototype.nanosToDate=function(e){return new Date(e/1e6)},e}(),fc=function(){return(fc=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};!function(e){e.COMPONENT_READY="streamlit:componentReady",e.SET_COMPONENT_VALUE="streamlit:setComponentValue",e.SET_FRAME_HEIGHT="streamlit:setFrameHeight"}(lc||(lc={}));var dc=function(){function e(){}return e.API_VERSION=1,e.RENDER_EVENT="streamlit:render",e.events=new A,e.registeredMessageListener=!1,e.setComponentReady=function(){e.registeredMessageListener||(window.addEventListener("message",e.onMessageEvent),e.registeredMessageListener=!0),e.sendBackMsg(lc.COMPONENT_READY,{apiVersion:e.API_VERSION})},e.setFrameHeight=function(t){void 0===t&&(t=document.body.scrollHeight),t!==e.lastFrameHeight&&(e.lastFrameHeight=t,e.sendBackMsg(lc.SET_FRAME_HEIGHT,{height:t}))},e.setComponentValue=function(t){var n;t instanceof cc?(n="dataframe",t=t.serialize()):!function(e){var t=!1;try{t=e instanceof BigInt64Array||e instanceof BigUint64Array}catch(n){}return e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array||t}(t)?t instanceof ArrayBuffer?(n="bytes",t=new Uint8Array(t)):n="json":(n="bytes",t=new Uint8Array(t.buffer)),e.sendBackMsg(lc.SET_COMPONENT_VALUE,{value:t,dataType:n})},e.onMessageEvent=function(t){switch(t.data.type){case e.RENDER_EVENT:e.onRenderMessage(t.data)}},e.onRenderMessage=function(t){var n=t.args;null==n&&(console.error("Got null args in onRenderMessage. This should never happen"),n={});var r=t.dfs&&t.dfs.length>0?e.argsDataframeToObject(t.dfs):{};n=fc(fc({},n),r);var i=Boolean(t.disabled),a=t.theme;a&&hc(a);var o={disabled:i,args:n,theme:a},u=new CustomEvent(e.RENDER_EVENT,{detail:o});e.events.dispatchEvent(u)},e.argsDataframeToObject=function(t){var n=t.map((function(t){var n=t.key,r=t.value;return[n,e.toArrowTable(r)]}));return Object.fromEntries(n)},e.toArrowTable=function(e){var t=e.data,n=t.data,r=t.index,i=t.columns,a=t.styler;return new cc(n,r,i,a)},e.sendBackMsg=function(e,t){window.parent.postMessage(fc({isStreamlitMessage:!0,type:e},t),"*")},e}(),hc=function(e){var t=document.createElement("style");document.head.appendChild(t),t.innerHTML="\n    :root {\n      --primary-color: "+e.primaryColor+";\n      --background-color: "+e.backgroundColor+";\n      --secondary-background-color: "+e.secondaryBackgroundColor+";\n      --text-color: "+e.textColor+";\n      --font: "+e.font+";\n    }\n\n    body {\n      background-color: var(--background-color);\n      color: var(--text-color);\n    }\n  "};var pc=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(e){function t(){return null!==e&&e.apply(this,arguments)||this}pc(t,e),t.prototype.componentDidMount=function(){dc.setFrameHeight()},t.prototype.componentDidUpdate=function(){dc.setFrameHeight()}}(d.a.PureComponent);function yc(e){var t=function(t){function n(n){var r=t.call(this,n)||this;return r.componentDidMount=function(){dc.events.addEventListener(dc.RENDER_EVENT,r.onRenderEvent),dc.setComponentReady()},r.componentDidUpdate=function(){null!=r.state.componentError&&dc.setFrameHeight()},r.componentWillUnmount=function(){dc.events.removeEventListener(dc.RENDER_EVENT,r.onRenderEvent)},r.onRenderEvent=function(e){var t=e;r.setState({renderData:t.detail})},r.render=function(){return null!=r.state.componentError?d.a.createElement("div",null,d.a.createElement("h1",null,"Component Error"),d.a.createElement("span",null,r.state.componentError.message)):null==r.state.renderData?null:d.a.createElement(e,{width:window.innerWidth,disabled:r.state.renderData.disabled,args:r.state.renderData.args,theme:r.state.renderData.theme})},r.state={renderData:void 0,componentError:void 0},r}return pc(n,t),n.getDerivedStateFromError=function(e){return{componentError:e}},n}(d.a.PureComponent);return c()(t,e)}},function(e,t,n){"use strict";var r=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable;function o(e){if(null===e||void 0===e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(i){return!1}}()?Object.assign:function(e,t){for(var n,u,s=o(e),l=1;l<arguments.length;l++){for(var c in n=Object(arguments[l]))i.call(n,c)&&(s[c]=n[c]);if(r){u=r(n);for(var f=0;f<u.length;f++)a.call(n,u[f])&&(s[u[f]]=n[u[f]])}}return s}},function(e,t,n){"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE){0;try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}}(),e.exports=n(17)},function(e,t,n){"use strict";var r=n(20),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},u={};function s(e){return r.isMemo(e)?o:u[e.$$typeof]||i}u[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u[r.Memo]=o;var l=Object.defineProperty,c=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,d=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,p=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(p){var i=h(n);i&&i!==p&&e(t,i,r)}var o=c(n);f&&(o=o.concat(f(n)));for(var u=s(t),y=s(n),v=0;v<o.length;++v){var b=o[v];if(!a[b]&&(!r||!r[b])&&(!y||!y[b])&&(!u||!u[b])){var m=d(n,b);try{l(t,b,m)}catch(g){}}}}return t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return(0,a.default)(e)};var r,i=n(24),a=(r=i)&&r.__esModule?r:{default:r};e.exports=t.default},function(e,t,n){"use strict";function r(e){return e.charAt(0).toUpperCase()+e.slice(1)}function i(e,t,n){if(e.hasOwnProperty(t)){for(var i={},a=e[t],o=r(t),u=Object.keys(n),s=0;s<u.length;s++){var l=u[s];if(l===t)for(var c=0;c<a.length;c++)i[a[c]+o]=n[t];i[l]=n[l]}return i}return n}function a(e,t,n,r,i){for(var a=0,o=e.length;a<o;++a){var u=e[a](t,n,r,i);if(u)return u}}function o(e,t){-1===e.indexOf(t)&&e.push(t)}function u(e,t){if(Array.isArray(t))for(var n=0,r=t.length;n<r;++n)o(e,t[n]);else o(e,t)}function s(e){return e instanceof Object&&!Array.isArray(e)}n.d(t,"a",(function(){return ke}));var l=["Webkit"],c=["ms"],f=["Webkit","ms"],d={plugins:[],prefixMap:{appearance:["Webkit","Moz"],textEmphasisPosition:l,textEmphasis:l,textEmphasisStyle:l,textEmphasisColor:l,boxDecorationBreak:l,maskImage:l,maskMode:l,maskRepeat:l,maskPosition:l,maskClip:l,maskOrigin:l,maskSize:l,maskComposite:l,mask:l,maskBorderSource:l,maskBorderMode:l,maskBorderSlice:l,maskBorderWidth:l,maskBorderOutset:l,maskBorderRepeat:l,maskBorder:l,maskType:l,textDecorationStyle:l,textDecorationSkip:l,textDecorationLine:l,textDecorationColor:l,userSelect:["Webkit","Moz","ms"],backdropFilter:l,fontKerning:l,scrollSnapType:f,scrollSnapPointsX:f,scrollSnapPointsY:f,scrollSnapDestination:f,scrollSnapCoordinate:f,clipPath:l,shapeImageThreshold:l,shapeImageMargin:l,shapeImageOutside:l,filter:l,hyphens:f,flowInto:f,flowFrom:f,breakBefore:f,breakAfter:f,breakInside:f,regionFragment:f,writingMode:f,textOrientation:l,tabSize:["Moz"],fontFeatureSettings:l,columnCount:l,columnFill:l,columnGap:l,columnRule:l,columnRuleColor:l,columnRuleStyle:l,columnRuleWidth:l,columns:l,columnSpan:l,columnWidth:l,wrapFlow:c,wrapThrough:c,wrapMargin:c,textSizeAdjust:f}};var h=["-webkit-","-moz-",""],p={"zoom-in":!0,"zoom-out":!0,grab:!0,grabbing:!0};var y=n(4),v=n.n(y),b=["-webkit-",""];var m=["-webkit-",""];var g={flex:["-webkit-box","-moz-box","-ms-flexbox","-webkit-flex","flex"],"inline-flex":["-webkit-inline-box","-moz-inline-box","-ms-inline-flexbox","-webkit-inline-flex","inline-flex"]};var k={"space-around":"justify","space-between":"justify","flex-start":"start","flex-end":"end","wrap-reverse":"multiple",wrap:"multiple"},w={alignItems:"WebkitBoxAlign",justifyContent:"WebkitBoxPack",flexWrap:"WebkitBoxLines",flexGrow:"WebkitBoxFlex"};var _=["-webkit-","-moz-",""],x=/linear-gradient|radial-gradient|repeating-linear-gradient|repeating-radial-gradient/gi;var S=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],r=!0,i=!1,a=void 0;try{for(var o,u=e[Symbol.iterator]();!(r=(o=u.next()).done)&&(n.push(o.value),!t||n.length!==t);r=!0);}catch(s){i=!0,a=s}finally{try{!r&&u.return&&u.return()}finally{if(i)throw a}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")};function T(e){return"number"===typeof e&&!isNaN(e)}function I(e){return"string"===typeof e&&e.includes("/")}var E=["center","end","start","stretch"],A={"inline-grid":["-ms-inline-grid","inline-grid"],grid:["-ms-grid","grid"]},F={alignSelf:function(e,t){E.indexOf(e)>-1&&(t.msGridRowAlign=e)},gridColumn:function(e,t){if(T(e))t.msGridColumn=e;else if(I(e)){var n=e.split("/"),r=S(n,2),i=r[0],a=r[1];F.gridColumnStart(+i,t);var o=a.split(/ ?span /),u=S(o,2),s=u[0],l=u[1];""===s?F.gridColumnEnd(+i+ +l,t):F.gridColumnEnd(+a,t)}else F.gridColumnStart(e,t)},gridColumnEnd:function(e,t){var n=t.msGridColumn;T(e)&&T(n)&&(t.msGridColumnSpan=e-n)},gridColumnStart:function(e,t){T(e)&&(t.msGridColumn=e)},gridRow:function(e,t){if(T(e))t.msGridRow=e;else if(I(e)){var n=e.split("/"),r=S(n,2),i=r[0],a=r[1];F.gridRowStart(+i,t);var o=a.split(/ ?span /),u=S(o,2),s=u[0],l=u[1];""===s?F.gridRowEnd(+i+ +l,t):F.gridRowEnd(+a,t)}else F.gridRowStart(e,t)},gridRowEnd:function(e,t){var n=t.msGridRow;T(e)&&T(n)&&(t.msGridRowSpan=e-n)},gridRowStart:function(e,t){T(e)&&(t.msGridRow=e)},gridTemplateColumns:function(e,t){t.msGridColumns=e},gridTemplateRows:function(e,t){t.msGridRows=e},justifySelf:function(e,t){E.indexOf(e)>-1&&(t.msGridColumnAlign=e)}};var O=["-webkit-",""];var B={marginBlockStart:["WebkitMarginBefore"],marginBlockEnd:["WebkitMarginAfter"],marginInlineStart:["WebkitMarginStart","MozMarginStart"],marginInlineEnd:["WebkitMarginEnd","MozMarginEnd"],paddingBlockStart:["WebkitPaddingBefore"],paddingBlockEnd:["WebkitPaddingAfter"],paddingInlineStart:["WebkitPaddingStart","MozPaddingStart"],paddingInlineEnd:["WebkitPaddingEnd","MozPaddingEnd"],borderBlockStart:["WebkitBorderBefore"],borderBlockStartColor:["WebkitBorderBeforeColor"],borderBlockStartStyle:["WebkitBorderBeforeStyle"],borderBlockStartWidth:["WebkitBorderBeforeWidth"],borderBlockEnd:["WebkitBorderAfter"],borderBlockEndColor:["WebkitBorderAfterColor"],borderBlockEndStyle:["WebkitBorderAfterStyle"],borderBlockEndWidth:["WebkitBorderAfterWidth"],borderInlineStart:["WebkitBorderStart","MozBorderStart"],borderInlineStartColor:["WebkitBorderStartColor","MozBorderStartColor"],borderInlineStartStyle:["WebkitBorderStartStyle","MozBorderStartStyle"],borderInlineStartWidth:["WebkitBorderStartWidth","MozBorderStartWidth"],borderInlineEnd:["WebkitBorderEnd","MozBorderEnd"],borderInlineEndColor:["WebkitBorderEndColor","MozBorderEndColor"],borderInlineEndStyle:["WebkitBorderEndStyle","MozBorderEndStyle"],borderInlineEndWidth:["WebkitBorderEndWidth","MozBorderEndWidth"]};var D=["-webkit-","-moz-",""],C={maxHeight:!0,maxWidth:!0,width:!0,height:!0,columnWidth:!0,minWidth:!0,minHeight:!0},L={"min-content":!0,"max-content":!0,"fill-available":!0,"fit-content":!0,"contain-floats":!0};var M=n(11),N=n.n(M),P={transition:!0,transitionProperty:!0,WebkitTransition:!0,WebkitTransitionProperty:!0,MozTransition:!0,MozTransitionProperty:!0},U={Webkit:"-webkit-",Moz:"-moz-",ms:"-ms-"};var R=function(e){var t=e.prefixMap,n=e.plugins;return function e(r){for(var o in r){var l=r[o];if(s(l))r[o]=e(l);else if(Array.isArray(l)){for(var c=[],f=0,d=l.length;f<d;++f){u(c,a(n,o,l[f],r,t)||l[f])}c.length>0&&(r[o]=c)}else{var h=a(n,o,l,r,t);h&&(r[o]=h),r=i(t,o,r)}}return r}}({prefixMap:d.prefixMap,plugins:[function(e,t){if("string"===typeof t&&"text"===t)return["-webkit-text","text"]},function(e,t){if("string"===typeof t&&!v()(t)&&t.indexOf("cross-fade(")>-1)return b.map((function(e){return t.replace(/cross-fade\(/g,e+"cross-fade(")}))},function(e,t){if("cursor"===e&&p.hasOwnProperty(t))return h.map((function(e){return e+t}))},function(e,t){if("string"===typeof t&&!v()(t)&&t.indexOf("filter(")>-1)return m.map((function(e){return t.replace(/filter\(/g,e+"filter(")}))},function(e,t,n){"flexDirection"===e&&"string"===typeof t&&(t.indexOf("column")>-1?n.WebkitBoxOrient="vertical":n.WebkitBoxOrient="horizontal",t.indexOf("reverse")>-1?n.WebkitBoxDirection="reverse":n.WebkitBoxDirection="normal"),w.hasOwnProperty(e)&&(n[w[e]]=k[t]||t)},function(e,t){if("string"===typeof t&&!v()(t)&&x.test(t))return _.map((function(e){return t.replace(x,(function(t){return e+t}))}))},function(e,t,n){if("display"===e&&t in A)return A[t];e in F&&(0,F[e])(t,n)},function(e,t){if("string"===typeof t&&!v()(t)&&t.indexOf("image-set(")>-1)return O.map((function(e){return t.replace(/image-set\(/g,e+"image-set(")}))},function(e,t,n){if(Object.prototype.hasOwnProperty.call(B,e))for(var r=B[e],i=0,a=r.length;i<a;++i)n[r[i]]=t},function(e,t){if("position"===e&&"sticky"===t)return["-webkit-sticky","sticky"]},function(e,t){if(C.hasOwnProperty(e)&&L.hasOwnProperty(t))return D.map((function(e){return e+t}))},function(e,t,n,i){if("string"===typeof t&&P.hasOwnProperty(e)){var a=function(e,t){if(v()(e))return e;for(var n=e.split(/,(?![^()]*(?:\([^()]*\))?\))/g),r=0,i=n.length;r<i;++r){var a=n[r],o=[a];for(var u in t){var s=N()(u);if(a.indexOf(s)>-1&&"order"!==s)for(var l=t[u],c=0,f=l.length;c<f;++c)o.unshift(a.replace(s,U[l[c]]+s))}n[r]=o.join(",")}return n.join(",")}(t,i),o=a.split(/,(?![^()]*(?:\([^()]*\))?\))/g).filter((function(e){return!/-moz-|-ms-/.test(e)})).join(",");if(e.indexOf("Webkit")>-1)return o;var u=a.split(/,(?![^()]*(?:\([^()]*\))?\))/g).filter((function(e){return!/-webkit-|-ms-/.test(e)})).join(",");return e.indexOf("Moz")>-1?u:(n["Webkit"+r(e)]=o,n["Moz"+r(e)]=u,a)}},function(e,t){if("display"===e&&g.hasOwnProperty(t))return g[t]}]}),z=function(){function e(e){void 0===e&&(e=""),this.prefix=e,this.count=0,this.offset=374,this.msb=1295,this.power=2}var t=e.prototype;return t.next=function(){var e=this.increment().toString(36);return this.prefix?""+this.prefix+e:e},t.increment=function(){var e=this.count+this.offset;return e===this.msb&&(this.offset+=9*(this.msb+1),this.msb=Math.pow(36,++this.power)-1),this.count++,e},e}(),j=/(!?\(\s*min(-device-)?-width).+\(\s*max(-device)?-width/i,V=/(!?\(\s*max(-device)?-width).+\(\s*min(-device)?-width/i,W=J(j,V,/\(\s*min(-device)?-width/i),H=J(V,j,/\(\s*max(-device)?-width/i),Y=/(!?\(\s*min(-device)?-height).+\(\s*max(-device)?-height/i,$=/(!?\(\s*max(-device)?-height).+\(\s*min(-device)?-height/i,K=J(Y,$,/\(\s*min(-device)?-height/i),Q=J($,Y,/\(\s*max(-device)?-height/i),G=/print/i,q=/^print$/i,X=Number.MAX_VALUE;function Z(e){var t=/(-?\d*\.?\d+)(ch|em|ex|px|rem)/.exec(e);if(null===t)return X;var n=t[1];switch(t[2]){case"ch":n=8.8984375*parseFloat(n);break;case"em":case"rem":n=16*parseFloat(n);break;case"ex":n=8.296875*parseFloat(n);break;case"px":n=parseFloat(n)}return+n}function J(e,t,n){return function(r){return!!e.test(r)||!t.test(r)&&n.test(r)}}function ee(e,t){if(""===e)return-1;if(""===t)return 1;var n=function(e,t){var n=G.test(e),r=q.test(e),i=G.test(t),a=q.test(t);return n&&i?!r&&a?1:r&&!a?-1:e.localeCompare(t):n?1:i?-1:null}(e,t);if(null!==n)return n;var r=W(e)||K(e),i=H(e)||Q(e),a=W(t)||K(t),o=H(t)||Q(t);if(r&&o)return-1;if(i&&a)return 1;var u=Z(e),s=Z(t);return u===X&&s===X?e.localeCompare(t):u===X?1:s===X?-1:u>s?i?-1:1:u<s?i?1:-1:e.localeCompare(t)}var te=function(){function e(e,t,n){this.idGenerator=e,this.onNewCache=t,this.onNewValue=n,this.sortedCacheKeys=[],this.caches={}}var t=e.prototype;return t.getCache=function(e){if(!this.caches[e]){var t=new ne(this.idGenerator,this.onNewValue);t.key=e,this.sortedCacheKeys.push(e),this.sortedCacheKeys.sort(ee);var n=this.sortedCacheKeys.indexOf(e),r=n<this.sortedCacheKeys.length-1?this.sortedCacheKeys[n+1]:void 0;this.caches[e]=t,this.onNewCache(e,t,r)}return this.caches[e]},t.getSortedCacheKeys=function(){return this.sortedCacheKeys},e}(),ne=function(){function e(e,t){this.cache={},this.idGenerator=e,this.onNewValue=t}return e.prototype.addValue=function(e,t){var n=this.cache[e];if(n)return n;var r=this.idGenerator.next();return this.cache[e]=r,this.onNewValue(this,r,t),r},e}(),re=/[A-Z]/g,ie=/^ms-/,ae={};function oe(e){return e in ae?ae[e]:ae[e]=e.replace(re,"-$&").toLowerCase().replace(ie,"-ms-")}function ue(e){return(ue="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function se(e,t,n,r){var i=e.getCache(n),a="";for(var o in t){var u=t[o];if(void 0!==u&&null!==u)if("object"!==ue(u)){0;var s=oe(o)+":"+u,l=""+r+s,c=i.cache[l];if(void 0!==c){a+=" "+c;continue}var f,d="",h=R(((f={})[o]=u,f));for(var p in h){var y=h[p],v=ue(y);if("string"===v||"number"===v){var b=oe(p)+":"+y;b!==s&&(d+=b+";")}else if(Array.isArray(y))for(var m=oe(p),g=0;g<y.length;g++){var k=m+":"+y[g];k!==s&&(d+=k+";")}}d+=s,a+=" "+i.addValue(l,{pseudo:r,block:d})}else":"===o[0]?a+=" "+se(e,u,n,r+o):"@media"===o.substring(0,6)&&(a+=" "+se(e,u,o.substr(7),r))}return a.slice(1)}function le(e,t){var n="."+e;return t&&(n+=t),n}function ce(e){var t="";for(var n in e)t+=n+"{"+fe(e[n])+"}";return t}function fe(e){var t="";for(var n in e){var r=e[n];"string"!==typeof r&&"number"!==typeof r||(t+=oe(n)+":"+r+";")}return t.slice(0,-1)}function de(e,t){return"@keyframes "+e+"{"+t+"}"}function he(e,t){return"@font-face{font-family:"+e+";"+t+"}"}function pe(e,t){return e+"{"+t+"}"}var ye=/\.([^{:]+)(:[^{]+)?{(?:[^}]*;)?([^}]*?)}/g,ve=/@keyframes ([^{]+){((?:(?:from|to|(?:\d+\.?\d*%))\{(?:[^}])*})*)}/g,be=/@font-face\{font-family:([^;]+);([^}]*)\}/g;function me(e,t,n){for(var r;r=t.exec(n);){var i=r,a=i[1],o=i[2],u=i[3];0;var s=o?""+o+u:u;e.cache[s]=a,e.idGenerator.increment()}}function ge(e,t,n){for(var r;r=t.exec(n);){var i=r,a=i[1],o=i[2];0,e.cache[o]=a,e.idGenerator.increment()}}var ke=function(){function e(e){var t=this;void 0===e&&(e={}),this.styleElements={};var n=new z(e.prefix),r=function(e,n,r){var i=r.pseudo,a=r.block,o=t.styleElements[e.key].sheet,u=pe(le(n,i),a);try{o.insertRule(u,o.cssRules.length)}catch(s){0}};if(this.styleCache=new te(n,(function(e,n,r){var i=document.createElement("style");if(i.media=e,void 0===r)t.container.appendChild(i);else{var a=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if("STYLE"===r.tagName&&r.media===t)return n}return-1}(t.container.children,r);t.container.insertBefore(i,t.container.children[a])}t.styleElements[e]=i}),r),this.keyframesCache=new ne(new z(e.prefix),(function(e,n,r){t.styleCache.getCache("");var i=t.styleElements[""].sheet,a=de(n,ce(r));try{i.insertRule(a,i.cssRules.length)}catch(o){0}})),this.fontFaceCache=new ne(new z(e.prefix),(function(e,n,r){t.styleCache.getCache("");var i=t.styleElements[""].sheet,a=he(n,fe(r));try{i.insertRule(a,i.cssRules.length)}catch(o){0}})),e.container&&(this.container=e.container),e.hydrate&&e.hydrate.length>0){if(!this.container){var i=e.hydrate[0].parentElement;null!==i&&void 0!==i&&(this.container=i)}for(var a=0;a<e.hydrate.length;a++){var o=e.hydrate[a],u=o.getAttribute("data-hydrate");if("font-face"!==u)if("keyframes"!==u){var s=o.media?o.media:"";this.styleElements[s]=o;var l=new ne(n,r);l.key=s,me(l,ye,o.textContent),this.styleCache.sortedCacheKeys.push(s),this.styleCache.caches[s]=l}else ge(this.keyframesCache,ve,o.textContent);else ge(this.fontFaceCache,be,o.textContent)}}if(!this.container){if(null===document.head)throw new Error("No container provided and `document.head` was null");this.container=document.head}}var t=e.prototype;return t.renderStyle=function(e){return se(this.styleCache,e,"","")},t.renderFontFace=function(e){var t=fe(e);return this.fontFaceCache.addValue(t,e)},t.renderKeyframes=function(e){var t=ce(e);return this.keyframesCache.addValue(t,e)},e}()},function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var r=n(1);function i(e){return(i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t){var n=function e(t,n){for(var r in t){var a=t[r];if("animationName"!==r||"string"===typeof a)if("fontFamily"!==r||"string"===typeof a)"object"===i(a)&&null!==a&&e(a,n);else{if(Array.isArray(a)){var o="",u=a,s=Array.isArray(u),l=0;for(u=s?u:u[Symbol.iterator]();;){var c;if(s){if(l>=u.length)break;c=u[l++]}else{if((l=u.next()).done)break;c=l.value}var f=c;"object"===i(f)?o+=n.renderFontFace(f)+",":"string"===typeof f&&(o+=f+",")}t.fontFamily=o.slice(0,-1);continue}if(void 0===a)continue;t.fontFamily=n.renderFontFace(a)}else t.animationName=n.renderKeyframes(a)}return t}(e,t);return t.renderStyle(n)}function o(e){return(o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var s={renderStyle:function(){return""},renderKeyframes:function(){return""},renderFontFace:function(){return""}},l=Object(r.createContext)(s),c=Object(r.createContext)(!1),f=Object(r.createContext)(),d=(Object(r.createContext)(),r.Component,l.Provider);function h(e){e===s&&console.warn("Styletron Provider is not set up. Defaulting to no-op.")}!function(e){var t=e.getInitialStyle,n=e.driver,r=e.wrapper}({getInitialStyle:function(){return{}},driver:a,wrapper:function(e){return e}});function p(e){return{reducer:function(t){return k(t,e)},assignmentCommutative:!0,factory:p,style:e}}function y(e,t){if(0===e.reducers.length){var n=t.reducer(e.getInitialStyle());return{reducers:e.reducers,base:e.base,driver:e.driver,wrapper:e.wrapper,getInitialStyle:function(){return n}}}var r=e.reducers[0];if(!0===r.assignmentCommutative&&!0===t.assignmentCommutative){var i=t.reducer(r.style);return{getInitialStyle:e.getInitialStyle,base:e.base,driver:e.driver,wrapper:e.wrapper,reducers:[r.factory(i)].concat(e.reducers.slice(1))}}return v(e,t.reducer)}function v(e,t){return{getInitialStyle:e.getInitialStyle,base:e.base,driver:e.driver,wrapper:e.wrapper,reducers:[{assignmentCommutative:!1,reducer:t}].concat(e.reducers)}}function b(e){var t=e.reducers,n=e.base,i=e.driver,a=e.wrapper,o=e.getInitialStyle,s=(e.ext,a(Object(r.forwardRef)((function(e,a){var s=Object(r.useContext)(l);Object(r.useContext)(f),Object(r.useContext)(c);h(s);var d=function(e){var t={};for(var n in e)"$"!==n[0]&&(t[n]=e[n]);return t}(e),p=function(e,t,n){var r=e(),i=t.length;for(;i--;){var a=t[i].reducer;r=a(r,n)}return r}(o,t,e);e.$style&&(p="function"===typeof e.$style?g(p,e.$style(e)):g(p,e.$style));var y=i(p,s),v=e.$as?e.$as:n;return d.className=e.className?e.className+" "+y:y,e.$ref&&console.warn("The prop `$ref` has been deprecated. Use `ref` instead. Refs are now forwarded with React.forwardRef."),Object(r.createElement)(v,u({},d,{ref:a||e.$ref}))}))));return s.__STYLETRON__={base:n,reducers:t,driver:i,wrapper:a,getInitialStyle:o},s}function m(e){return"object"===o(e)&&null!==e}function g(e,t){var n=w({},e);for(var r in t){var i=t[r];m(i)&&m(e[r])?n[r]=g(e[r],i):n[r]=i}return n}function k(e,t){return w(w({},e),t)}function w(e,t){for(var n in t)e[n]=t[n];return e}},function(e,t,n){"use strict";n.d(t,"a",(function(){return w}));var r=n(3),i={data:""},a=function(e){if("undefined"!=typeof window){var t=e?e.querySelector("#_goober"):window._goober;return t||((t=(e||document.head).appendChild(document.createElement("style"))).innerHTML=" ",t.id="_goober"),t.firstChild}return e||i},o=/(?:([A-Z0-9-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(})/gi,u=/\/\*[\s\S]*?\*\/|\s{2,}|\n/gm,s=function e(t,n){var r,i="",a="",o="",u=function(u){var s=t[u];"object"==typeof s?(r=n?n.replace(/([^,])+/g,(function(e){return u.replace(/([^,])+/g,(function(t){return/&/g.test(t)?t.replace(/&/g,e):e?e+" "+t:t}))})):u,a+="@"==u[0]?"f"==u[1]?e(s,u):u+"{"+e(s,"k"==u[1]?"":n)+"}":e(s,r)):"@"==u[0]&&"i"==u[1]?i=u+" "+s+";":o+=e.p?e.p(u.replace(/[A-Z]/g,"-$&").toLowerCase(),s):u.replace(/[A-Z]/g,"-$&").toLowerCase()+":"+s+";"};for(var s in t)u(s);return o[0]?i+(r=n?n+"{"+o+"}":o)+a:i+a},l={},c=function(e,t,n,i,a){var c="object"==typeof e?function e(t){var n="";for(var r in t)n+=r+("object"==typeof t[r]?e(t[r]):t[r]);return n}(e):e,f=l[c]||(l[c]="go"+c.split("").reduce((function(e,t){return 101*e+t.charCodeAt(0)>>>0}),11));if(!l[f]){var d="object"==typeof e?e:function(e){for(var t,n=[{}];t=o.exec(e.replace(u,""));)t[4]&&n.shift(),t[3]?n.unshift(n[0][t[3]]=n[0][t[3]]||{}):t[4]||(n[0][t[1]]=t[2]);return n[0]}(e);l[f]=s(a?Object(r.a)({},"@keyframes "+f,d):d,n?"":"."+f)}return function(e,t,n){-1==t.data.indexOf(e)&&(t.data=n?e+t.data:t.data+e)}(l[f],t,i),f},f=function(e,t,n){return e.reduce((function(e,r,i){var a=t[i];if(a&&a.call){var o=a(n),u=o&&o.props&&o.props.className||/^go/.test(o)&&o;a=u?"."+u:o&&"object"==typeof o?o.props?"":s(o,""):o}return e+r+(null==a?"":a)}),"")};function d(e){var t=this||{},n=e.call?e(t.p):e;return c(n.unshift?n.raw?f(n,[].slice.call(arguments,1),t.p):n.reduce((function(e,n){return n?Object.assign(e,n.call?n(t.p):n):e}),{}):n,a(t.target),t.g,t.o,t.k)}d.bind({g:1}),d.bind({k:1});var h=n(1),p=n.n(h);function y(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.join(" ")}var v=d({alignItems:"center",background:"var(--rti-tag)",borderRadius:"var(--rti-radius)",display:"inline-flex",justifyContent:"center",paddingLeft:"var(--rti-s)",button:{background:"none",border:0,borderRadius:"50%",cursor:"pointer",lineHeight:"inherit",padding:"0 var(--rti-s)","&:hover":{color:"var(--rti-tag-remove)"}}});function b(e){var t=e.text,n=e.remove;return p.a.createElement("span",{className:y("rti--tag",v)},p.a.createElement("span",null,t),p.a.createElement("button",{type:"button",onClick:function(e){e.stopPropagation(),n(t)},"aria-label":"remove "+t},"\u2715"))}!function(e,t,n,r){s.p=t,e,n,r}(p.a.createElement);var m=d({"--rtiBg":"#fff","--rtiBorder":"#ccc","--rtiMain":"#3182ce","--rtiRadius":"0.375rem","--rtiS":"0.5rem","--rtiTag":"#edf2f7","--rtiTagRemove":"#e53e3e","*":{boxSizing:"border-box",transition:"all 0.2s ease"},alignItems:"center",bg:"var(--rti-bg)",border:"1px solid var(--rti-border)",borderRadius:"var(--rti-radius)",display:"flex",flexWrap:"wrap",gap:"var(--rti-s)",lineHeight:1.4,padding:"var(--rti-s)","&:focus-within":{borderColor:"var(--rti-main)",boxShadow:"var(--rti-main) 0px 0px 0px 1px"}}),g=d({border:0,outline:0,fontSize:"inherit",lineHeight:"inherit",width:"50%"}),k=["Enter"],w=function(e){var t=e.name,n=e.placeHolder,r=e.value,i=e.onChange,a=e.onBlur,o=e.seprators,u=e.onExisting,s=e.onRemoved,l=Object(h.useState)(r||[]),c=l[0],f=l[1];Object(h.useEffect)((function(){i&&i(c)}),[c]);var d=function(e){f(c.filter((function(t){return t!==e}))),s&&s(e)};return p.a.createElement("div",{"aria-labelledby":t,className:y("rti--container",m)},c.map((function(e){return p.a.createElement(b,{key:e,text:e,remove:d})})),p.a.createElement("input",{className:y("rti--input",g),type:"text",name:t,placeholder:n,onKeyDown:function(e){e.stopPropagation();var t=e.target.value;if("Backspace"===e.key&&c.length&&!t&&f(c.slice(0,-1)),t&&(o||k).includes(e.key)){if(c.includes(t))return void(u&&u(t));f([].concat(c,[t])),e.target.value="",e.preventDefault()}},onBlur:a}))}},,function(e,t,n){"use strict";var r=n(8),i="function"===typeof Symbol&&Symbol.for,a=i?Symbol.for("react.element"):60103,o=i?Symbol.for("react.portal"):60106,u=i?Symbol.for("react.fragment"):60107,s=i?Symbol.for("react.strict_mode"):60108,l=i?Symbol.for("react.profiler"):60114,c=i?Symbol.for("react.provider"):60109,f=i?Symbol.for("react.context"):60110,d=i?Symbol.for("react.forward_ref"):60112,h=i?Symbol.for("react.suspense"):60113,p=i?Symbol.for("react.memo"):60115,y=i?Symbol.for("react.lazy"):60116,v="function"===typeof Symbol&&Symbol.iterator;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g={};function k(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function w(){}function _(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}k.prototype.isReactComponent={},k.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error(b(85));this.updater.enqueueSetState(this,e,t,"setState")},k.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},w.prototype=k.prototype;var x=_.prototype=new w;x.constructor=_,r(x,k.prototype),x.isPureReactComponent=!0;var S={current:null},T=Object.prototype.hasOwnProperty,I={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,n){var r,i={},o=null,u=null;if(null!=t)for(r in void 0!==t.ref&&(u=t.ref),void 0!==t.key&&(o=""+t.key),t)T.call(t,r)&&!I.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(1===s)i.children=n;else if(1<s){for(var l=Array(s),c=0;c<s;c++)l[c]=arguments[c+2];i.children=l}if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===i[r]&&(i[r]=s[r]);return{$$typeof:a,type:e,key:o,ref:u,props:i,_owner:S.current}}function A(e){return"object"===typeof e&&null!==e&&e.$$typeof===a}var F=/\/+/g,O=[];function B(e,t,n,r){if(O.length){var i=O.pop();return i.result=e,i.keyPrefix=t,i.func=n,i.context=r,i.count=0,i}return{result:e,keyPrefix:t,func:n,context:r,count:0}}function D(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>O.length&&O.push(e)}function C(e,t,n){return null==e?0:function e(t,n,r,i){var u=typeof t;"undefined"!==u&&"boolean"!==u||(t=null);var s=!1;if(null===t)s=!0;else switch(u){case"string":case"number":s=!0;break;case"object":switch(t.$$typeof){case a:case o:s=!0}}if(s)return r(i,t,""===n?"."+L(t,0):n),1;if(s=0,n=""===n?".":n+":",Array.isArray(t))for(var l=0;l<t.length;l++){var c=n+L(u=t[l],l);s+=e(u,c,r,i)}else if(null===t||"object"!==typeof t?c=null:c="function"===typeof(c=v&&t[v]||t["@@iterator"])?c:null,"function"===typeof c)for(t=c.call(t),l=0;!(u=t.next()).done;)s+=e(u=u.value,c=n+L(u,l++),r,i);else if("object"===u)throw r=""+t,Error(b(31,"[object Object]"===r?"object with keys {"+Object.keys(t).join(", ")+"}":r,""));return s}(e,"",t,n)}function L(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,(function(e){return t[e]}))}(e.key):t.toString(36)}function M(e,t){e.func.call(e.context,t,e.count++)}function N(e,t,n){var r=e.result,i=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?P(e,r,n,(function(e){return e})):null!=e&&(A(e)&&(e=function(e,t){return{$$typeof:a,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(e,i+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(F,"$&/")+"/")+n)),r.push(e))}function P(e,t,n,r,i){var a="";null!=n&&(a=(""+n).replace(F,"$&/")+"/"),C(e,N,t=B(t,a,r,i)),D(t)}var U={current:null};function R(){var e=U.current;if(null===e)throw Error(b(321));return e}var z={ReactCurrentDispatcher:U,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:S,IsSomeRendererActing:{current:!1},assign:r};t.Children={map:function(e,t,n){if(null==e)return e;var r=[];return P(e,r,null,t,n),r},forEach:function(e,t,n){if(null==e)return e;C(e,M,t=B(null,null,t,n)),D(t)},count:function(e){return C(e,(function(){return null}),null)},toArray:function(e){var t=[];return P(e,t,null,(function(e){return e})),t},only:function(e){if(!A(e))throw Error(b(143));return e}},t.Component=k,t.Fragment=u,t.Profiler=l,t.PureComponent=_,t.StrictMode=s,t.Suspense=h,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=z,t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error(b(267,e));var i=r({},e.props),o=e.key,u=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(u=t.ref,s=S.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)T.call(t,c)&&!I.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){l=Array(c);for(var f=0;f<c;f++)l[f]=arguments[f+2];i.children=l}return{$$typeof:a,type:e.type,key:o,ref:u,props:i,_owner:s}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:f,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:c,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:d,render:e}},t.isValidElement=A,t.lazy=function(e){return{$$typeof:y,_ctor:e,_status:-1,_result:null}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return R().useCallback(e,t)},t.useContext=function(e,t){return R().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return R().useEffect(e,t)},t.useImperativeHandle=function(e,t,n){return R().useImperativeHandle(e,t,n)},t.useLayoutEffect=function(e,t){return R().useLayoutEffect(e,t)},t.useMemo=function(e,t){return R().useMemo(e,t)},t.useReducer=function(e,t,n){return R().useReducer(e,t,n)},t.useRef=function(e){return R().useRef(e)},t.useState=function(e){return R().useState(e)},t.version="16.14.0"},function(e,t,n){"use strict";var r=n(1),i=n(8),a=n(18);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}if(!r)throw Error(o(227));function u(e,t,n,r,i,a,o,u,s){var l=Array.prototype.slice.call(arguments,3);try{t.apply(n,l)}catch(c){this.onError(c)}}var s=!1,l=null,c=!1,f=null,d={onError:function(e){s=!0,l=e}};function h(e,t,n,r,i,a,o,c,f){s=!1,l=null,u.apply(d,arguments)}var p=null,y=null,v=null;function b(e,t,n){var r=e.type||"unknown-event";e.currentTarget=v(n),function(e,t,n,r,i,a,u,d,p){if(h.apply(this,arguments),s){if(!s)throw Error(o(198));var y=l;s=!1,l=null,c||(c=!0,f=y)}}(r,t,void 0,e),e.currentTarget=null}var m=null,g={};function k(){if(m)for(var e in g){var t=g[e],n=m.indexOf(e);if(!(-1<n))throw Error(o(96,e));if(!_[n]){if(!t.extractEvents)throw Error(o(97,e));for(var r in _[n]=t,n=t.eventTypes){var i=void 0,a=n[r],u=t,s=r;if(x.hasOwnProperty(s))throw Error(o(99,s));x[s]=a;var l=a.phasedRegistrationNames;if(l){for(i in l)l.hasOwnProperty(i)&&w(l[i],u,s);i=!0}else a.registrationName?(w(a.registrationName,u,s),i=!0):i=!1;if(!i)throw Error(o(98,r,e))}}}}function w(e,t,n){if(S[e])throw Error(o(100,e));S[e]=t,T[e]=t.eventTypes[n].dependencies}var _=[],x={},S={},T={};function I(e){var t,n=!1;for(t in e)if(e.hasOwnProperty(t)){var r=e[t];if(!g.hasOwnProperty(t)||g[t]!==r){if(g[t])throw Error(o(102,t));g[t]=r,n=!0}}n&&k()}var E=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),A=null,F=null,O=null;function B(e){if(e=y(e)){if("function"!==typeof A)throw Error(o(280));var t=e.stateNode;t&&(t=p(t),A(e.stateNode,e.type,t))}}function D(e){F?O?O.push(e):O=[e]:F=e}function C(){if(F){var e=F,t=O;if(O=F=null,B(e),t)for(e=0;e<t.length;e++)B(t[e])}}function L(e,t){return e(t)}function M(e,t,n,r,i){return e(t,n,r,i)}function N(){}var P=L,U=!1,R=!1;function z(){null===F&&null===O||(N(),C())}function j(e,t,n){if(R)return e(t,n);R=!0;try{return P(e,t,n)}finally{R=!1,z()}}var V=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,W=Object.prototype.hasOwnProperty,H={},Y={};function $(e,t,n,r,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a}var K={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){K[e]=new $(e,0,!1,e,null,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];K[t]=new $(t,1,!1,e[1],null,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){K[e]=new $(e,2,!1,e.toLowerCase(),null,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){K[e]=new $(e,2,!1,e,null,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){K[e]=new $(e,3,!1,e.toLowerCase(),null,!1)})),["checked","multiple","muted","selected"].forEach((function(e){K[e]=new $(e,3,!0,e,null,!1)})),["capture","download"].forEach((function(e){K[e]=new $(e,4,!1,e,null,!1)})),["cols","rows","size","span"].forEach((function(e){K[e]=new $(e,6,!1,e,null,!1)})),["rowSpan","start"].forEach((function(e){K[e]=new $(e,5,!1,e.toLowerCase(),null,!1)}));var Q=/[\-:]([a-z])/g;function G(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(Q,G);K[t]=new $(t,1,!1,e,null,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(Q,G);K[t]=new $(t,1,!1,e,"http://www.w3.org/1999/xlink",!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(Q,G);K[t]=new $(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1)})),["tabIndex","crossOrigin"].forEach((function(e){K[e]=new $(e,1,!1,e.toLowerCase(),null,!1)})),K.xlinkHref=new $("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0),["src","href","action","formAction"].forEach((function(e){K[e]=new $(e,1,!1,e.toLowerCase(),null,!0)}));var q=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function X(e,t,n,r){var i=K.hasOwnProperty(t)?K[t]:null;(null!==i?0===i.type:!r&&(2<t.length&&("o"===t[0]||"O"===t[0])&&("n"===t[1]||"N"===t[1])))||(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!W.call(Y,e)||!W.call(H,e)&&(V.test(e)?Y[e]=!0:(H[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}q.hasOwnProperty("ReactCurrentDispatcher")||(q.ReactCurrentDispatcher={current:null}),q.hasOwnProperty("ReactCurrentBatchConfig")||(q.ReactCurrentBatchConfig={suspense:null});var Z=/^(.*)[\\\/]/,J="function"===typeof Symbol&&Symbol.for,ee=J?Symbol.for("react.element"):60103,te=J?Symbol.for("react.portal"):60106,ne=J?Symbol.for("react.fragment"):60107,re=J?Symbol.for("react.strict_mode"):60108,ie=J?Symbol.for("react.profiler"):60114,ae=J?Symbol.for("react.provider"):60109,oe=J?Symbol.for("react.context"):60110,ue=J?Symbol.for("react.concurrent_mode"):60111,se=J?Symbol.for("react.forward_ref"):60112,le=J?Symbol.for("react.suspense"):60113,ce=J?Symbol.for("react.suspense_list"):60120,fe=J?Symbol.for("react.memo"):60115,de=J?Symbol.for("react.lazy"):60116,he=J?Symbol.for("react.block"):60121,pe="function"===typeof Symbol&&Symbol.iterator;function ye(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=pe&&e[pe]||e["@@iterator"])?e:null}function ve(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case ne:return"Fragment";case te:return"Portal";case ie:return"Profiler";case re:return"StrictMode";case le:return"Suspense";case ce:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case oe:return"Context.Consumer";case ae:return"Context.Provider";case se:var t=e.render;return t=t.displayName||t.name||"",e.displayName||(""!==t?"ForwardRef("+t+")":"ForwardRef");case fe:return ve(e.type);case he:return ve(e.render);case de:if(e=1===e._status?e._result:null)return ve(e)}return null}function be(e){var t="";do{e:switch(e.tag){case 3:case 4:case 6:case 7:case 10:case 9:var n="";break e;default:var r=e._debugOwner,i=e._debugSource,a=ve(e.type);n=null,r&&(n=ve(r.type)),r=a,a="",i?a=" (at "+i.fileName.replace(Z,"")+":"+i.lineNumber+")":n&&(a=" (created by "+n+")"),n="\n    in "+(r||"Unknown")+a}t+=n,e=e.return}while(e);return t}function me(e){switch(typeof e){case"boolean":case"number":case"object":case"string":case"undefined":return e;default:return""}}function ge(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function ke(e){e._valueTracker||(e._valueTracker=function(e){var t=ge(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function we(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ge(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function _e(e,t){var n=t.checked;return i({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function xe(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=me(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Se(e,t){null!=(t=t.checked)&&X(e,"checked",t,!1)}function Te(e,t){Se(e,t);var n=me(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?Ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ee(e,t.type,me(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Ie(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function Ee(e,t,n){"number"===t&&e.ownerDocument.activeElement===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}function Ae(e,t){return e=i({children:void 0},t),(t=function(e){var t="";return r.Children.forEach(e,(function(e){null!=e&&(t+=e)})),t}(t.children))&&(e.children=t),e}function Fe(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+me(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function Oe(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return i({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Be(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(Array.isArray(n)){if(!(1>=n.length))throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:me(n)}}function De(e,t){var n=me(t.value),r=me(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function Ce(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}var Le="http://www.w3.org/1999/xhtml",Me="http://www.w3.org/2000/svg";function Ne(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Pe(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?Ne(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var Ue,Re=function(e){return"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction((function(){return e(t,n)}))}:e}((function(e,t){if(e.namespaceURI!==Me||"innerHTML"in e)e.innerHTML=t;else{for((Ue=Ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}}));function ze(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}function je(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ve={animationend:je("Animation","AnimationEnd"),animationiteration:je("Animation","AnimationIteration"),animationstart:je("Animation","AnimationStart"),transitionend:je("Transition","TransitionEnd")},We={},He={};function Ye(e){if(We[e])return We[e];if(!Ve[e])return e;var t,n=Ve[e];for(t in n)if(n.hasOwnProperty(t)&&t in He)return We[e]=n[t];return e}E&&(He=document.createElement("div").style,"AnimationEvent"in window||(delete Ve.animationend.animation,delete Ve.animationiteration.animation,delete Ve.animationstart.animation),"TransitionEvent"in window||delete Ve.transitionend.transition);var $e=Ye("animationend"),Ke=Ye("animationiteration"),Qe=Ye("animationstart"),Ge=Ye("transitionend"),qe="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Xe=new("function"===typeof WeakMap?WeakMap:Map);function Ze(e){var t=Xe.get(e);return void 0===t&&(t=new Map,Xe.set(e,t)),t}function Je(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(1026&(t=e).effectTag)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function et(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function tt(e){if(Je(e)!==e)throw Error(o(188))}function nt(e){if(!(e=function(e){var t=e.alternate;if(!t){if(null===(t=Je(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(r=i.return)){n=r;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===n)return tt(i),e;if(a===r)return tt(i),t;a=a.sibling}throw Error(o(188))}if(n.return!==r.return)n=i,r=a;else{for(var u=!1,s=i.child;s;){if(s===n){u=!0,n=i,r=a;break}if(s===r){u=!0,r=i,n=a;break}s=s.sibling}if(!u){for(s=a.child;s;){if(s===n){u=!0,n=a,r=i;break}if(s===r){u=!0,r=a,n=i;break}s=s.sibling}if(!u)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}function rt(e,t){if(null==t)throw Error(o(30));return null==e?t:Array.isArray(e)?Array.isArray(t)?(e.push.apply(e,t),e):(e.push(t),e):Array.isArray(t)?[e].concat(t):[e,t]}function it(e,t,n){Array.isArray(e)?e.forEach(t,n):e&&t.call(n,e)}var at=null;function ot(e){if(e){var t=e._dispatchListeners,n=e._dispatchInstances;if(Array.isArray(t))for(var r=0;r<t.length&&!e.isPropagationStopped();r++)b(e,t[r],n[r]);else t&&b(e,t,n);e._dispatchListeners=null,e._dispatchInstances=null,e.isPersistent()||e.constructor.release(e)}}function ut(e){if(null!==e&&(at=rt(at,e)),e=at,at=null,e){if(it(e,ot),at)throw Error(o(95));if(c)throw e=f,c=!1,f=null,e}}function st(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}function lt(e){if(!E)return!1;var t=(e="on"+e)in document;return t||((t=document.createElement("div")).setAttribute(e,"return;"),t="function"===typeof t[e]),t}var ct=[];function ft(e){e.topLevelType=null,e.nativeEvent=null,e.targetInst=null,e.ancestors.length=0,10>ct.length&&ct.push(e)}function dt(e,t,n,r){if(ct.length){var i=ct.pop();return i.topLevelType=e,i.eventSystemFlags=r,i.nativeEvent=t,i.targetInst=n,i}return{topLevelType:e,eventSystemFlags:r,nativeEvent:t,targetInst:n,ancestors:[]}}function ht(e){var t=e.targetInst,n=t;do{if(!n){e.ancestors.push(n);break}var r=n;if(3===r.tag)r=r.stateNode.containerInfo;else{for(;r.return;)r=r.return;r=3!==r.tag?null:r.stateNode.containerInfo}if(!r)break;5!==(t=n.tag)&&6!==t||e.ancestors.push(n),n=En(r)}while(n);for(n=0;n<e.ancestors.length;n++){t=e.ancestors[n];var i=st(e.nativeEvent);r=e.topLevelType;var a=e.nativeEvent,o=e.eventSystemFlags;0===n&&(o|=64);for(var u=null,s=0;s<_.length;s++){var l=_[s];l&&(l=l.extractEvents(r,t,a,i,o))&&(u=rt(u,l))}ut(u)}}function pt(e,t,n){if(!n.has(e)){switch(e){case"scroll":Qt(t,"scroll",!0);break;case"focus":case"blur":Qt(t,"focus",!0),Qt(t,"blur",!0),n.set("blur",null),n.set("focus",null);break;case"cancel":case"close":lt(e)&&Qt(t,e,!0);break;case"invalid":case"submit":case"reset":break;default:-1===qe.indexOf(e)&&Kt(e,t)}n.set(e,null)}}var yt,vt,bt,mt=!1,gt=[],kt=null,wt=null,_t=null,xt=new Map,St=new Map,Tt=[],It="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput close cancel copy cut paste click change contextmenu reset submit".split(" "),Et="focus blur dragenter dragleave mouseover mouseout pointerover pointerout gotpointercapture lostpointercapture".split(" ");function At(e,t,n,r,i){return{blockedOn:e,topLevelType:t,eventSystemFlags:32|n,nativeEvent:i,container:r}}function Ft(e,t){switch(e){case"focus":case"blur":kt=null;break;case"dragenter":case"dragleave":wt=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":xt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":St.delete(t.pointerId)}}function Ot(e,t,n,r,i,a){return null===e||e.nativeEvent!==a?(e=At(t,n,r,i,a),null!==t&&(null!==(t=An(t))&&vt(t)),e):(e.eventSystemFlags|=r,e)}function Bt(e){var t=En(e.target);if(null!==t){var n=Je(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=et(n)))return e.blockedOn=t,void a.unstable_runWithPriority(e.priority,(function(){bt(n)}))}else if(3===t&&n.stateNode.hydrate)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Dt(e){if(null!==e.blockedOn)return!1;var t=Zt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);if(null!==t){var n=An(t);return null!==n&&vt(n),e.blockedOn=t,!1}return!0}function Ct(e,t,n){Dt(e)&&n.delete(t)}function Lt(){for(mt=!1;0<gt.length;){var e=gt[0];if(null!==e.blockedOn){null!==(e=An(e.blockedOn))&&yt(e);break}var t=Zt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);null!==t?e.blockedOn=t:gt.shift()}null!==kt&&Dt(kt)&&(kt=null),null!==wt&&Dt(wt)&&(wt=null),null!==_t&&Dt(_t)&&(_t=null),xt.forEach(Ct),St.forEach(Ct)}function Mt(e,t){e.blockedOn===t&&(e.blockedOn=null,mt||(mt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Lt)))}function Nt(e){function t(t){return Mt(t,e)}if(0<gt.length){Mt(gt[0],e);for(var n=1;n<gt.length;n++){var r=gt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==kt&&Mt(kt,e),null!==wt&&Mt(wt,e),null!==_t&&Mt(_t,e),xt.forEach(t),St.forEach(t),n=0;n<Tt.length;n++)(r=Tt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Tt.length&&null===(n=Tt[0]).blockedOn;)Bt(n),null===n.blockedOn&&Tt.shift()}var Pt={},Ut=new Map,Rt=new Map,zt=["abort","abort",$e,"animationEnd",Ke,"animationIteration",Qe,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata","loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",Ge,"transitionEnd","waiting","waiting"];function jt(e,t){for(var n=0;n<e.length;n+=2){var r=e[n],i=e[n+1],a="on"+(i[0].toUpperCase()+i.slice(1));a={phasedRegistrationNames:{bubbled:a,captured:a+"Capture"},dependencies:[r],eventPriority:t},Rt.set(r,t),Ut.set(r,a),Pt[i]=a}}jt("blur blur cancel cancel click click close close contextmenu contextMenu copy copy cut cut auxclick auxClick dblclick doubleClick dragend dragEnd dragstart dragStart drop drop focus focus input input invalid invalid keydown keyDown keypress keyPress keyup keyUp mousedown mouseDown mouseup mouseUp paste paste pause pause play play pointercancel pointerCancel pointerdown pointerDown pointerup pointerUp ratechange rateChange reset reset seeked seeked submit submit touchcancel touchCancel touchend touchEnd touchstart touchStart volumechange volumeChange".split(" "),0),jt("drag drag dragenter dragEnter dragexit dragExit dragleave dragLeave dragover dragOver mousemove mouseMove mouseout mouseOut mouseover mouseOver pointermove pointerMove pointerout pointerOut pointerover pointerOver scroll scroll toggle toggle touchmove touchMove wheel wheel".split(" "),1),jt(zt,2);for(var Vt="change selectionchange textInput compositionstart compositionend compositionupdate".split(" "),Wt=0;Wt<Vt.length;Wt++)Rt.set(Vt[Wt],0);var Ht=a.unstable_UserBlockingPriority,Yt=a.unstable_runWithPriority,$t=!0;function Kt(e,t){Qt(t,e,!1)}function Qt(e,t,n){var r=Rt.get(t);switch(void 0===r?2:r){case 0:r=Gt.bind(null,t,1,e);break;case 1:r=qt.bind(null,t,1,e);break;default:r=Xt.bind(null,t,1,e)}n?e.addEventListener(t,r,!0):e.addEventListener(t,r,!1)}function Gt(e,t,n,r){U||N();var i=Xt,a=U;U=!0;try{M(i,e,t,n,r)}finally{(U=a)||z()}}function qt(e,t,n,r){Yt(Ht,Xt.bind(null,e,t,n,r))}function Xt(e,t,n,r){if($t)if(0<gt.length&&-1<It.indexOf(e))e=At(null,e,t,n,r),gt.push(e);else{var i=Zt(e,t,n,r);if(null===i)Ft(e,r);else if(-1<It.indexOf(e))e=At(i,e,t,n,r),gt.push(e);else if(!function(e,t,n,r,i){switch(t){case"focus":return kt=Ot(kt,e,t,n,r,i),!0;case"dragenter":return wt=Ot(wt,e,t,n,r,i),!0;case"mouseover":return _t=Ot(_t,e,t,n,r,i),!0;case"pointerover":var a=i.pointerId;return xt.set(a,Ot(xt.get(a)||null,e,t,n,r,i)),!0;case"gotpointercapture":return a=i.pointerId,St.set(a,Ot(St.get(a)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r)){Ft(e,r),e=dt(e,r,null,t);try{j(ht,e)}finally{ft(e)}}}}function Zt(e,t,n,r){if(null!==(n=En(n=st(r)))){var i=Je(n);if(null===i)n=null;else{var a=i.tag;if(13===a){if(null!==(n=et(i)))return n;n=null}else if(3===a){if(i.stateNode.hydrate)return 3===i.tag?i.stateNode.containerInfo:null;n=null}else i!==n&&(n=null)}}e=dt(e,r,n,t);try{j(ht,e)}finally{ft(e)}return null}var Jt={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},en=["Webkit","ms","Moz","O"];function tn(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||Jt.hasOwnProperty(e)&&Jt[e]?(""+t).trim():t+"px"}function nn(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=tn(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(Jt).forEach((function(e){en.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Jt[t]=Jt[e]}))}));var rn=i({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function an(e,t){if(t){if(rn[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e,""));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62,""))}}function on(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var un=Le;function sn(e,t){var n=Ze(e=9===e.nodeType||11===e.nodeType?e:e.ownerDocument);t=T[t];for(var r=0;r<t.length;r++)pt(t[r],e,n)}function ln(){}function cn(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function fn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dn(e,t){var n,r=fn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=fn(r)}}function hn(){for(var e=window,t=cn();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=cn((e=t.contentWindow).document)}return t}function pn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var yn=null,vn=null;function bn(e,t){switch(e){case"button":case"input":case"select":case"textarea":return!!t.autoFocus}return!1}function mn(e,t){return"textarea"===e||"option"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var gn="function"===typeof setTimeout?setTimeout:void 0,kn="function"===typeof clearTimeout?clearTimeout:void 0;function wn(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break}return e}function _n(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var xn=Math.random().toString(36).slice(2),Sn="__reactInternalInstance$"+xn,Tn="__reactEventHandlers$"+xn,In="__reactContainere$"+xn;function En(e){var t=e[Sn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[In]||n[Sn]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=_n(e);null!==e;){if(n=e[Sn])return n;e=_n(e)}return t}n=(e=n).parentNode}return null}function An(e){return!(e=e[Sn]||e[In])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function Fn(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function On(e){return e[Tn]||null}function Bn(e){do{e=e.return}while(e&&5!==e.tag);return e||null}function Dn(e,t){var n=e.stateNode;if(!n)return null;var r=p(n);if(!r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}function Cn(e,t,n){(t=Dn(e,n.dispatchConfig.phasedRegistrationNames[t]))&&(n._dispatchListeners=rt(n._dispatchListeners,t),n._dispatchInstances=rt(n._dispatchInstances,e))}function Ln(e){if(e&&e.dispatchConfig.phasedRegistrationNames){for(var t=e._targetInst,n=[];t;)n.push(t),t=Bn(t);for(t=n.length;0<t--;)Cn(n[t],"captured",e);for(t=0;t<n.length;t++)Cn(n[t],"bubbled",e)}}function Mn(e,t,n){e&&n&&n.dispatchConfig.registrationName&&(t=Dn(e,n.dispatchConfig.registrationName))&&(n._dispatchListeners=rt(n._dispatchListeners,t),n._dispatchInstances=rt(n._dispatchInstances,e))}function Nn(e){e&&e.dispatchConfig.registrationName&&Mn(e._targetInst,null,e)}function Pn(e){it(e,Ln)}var Un=null,Rn=null,zn=null;function jn(){if(zn)return zn;var e,t,n=Rn,r=n.length,i="value"in Un?Un.value:Un.textContent,a=i.length;for(e=0;e<r&&n[e]===i[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===i[a-t];t++);return zn=i.slice(e,1<t?1-t:void 0)}function Vn(){return!0}function Wn(){return!1}function Hn(e,t,n,r){for(var i in this.dispatchConfig=e,this._targetInst=t,this.nativeEvent=n,e=this.constructor.Interface)e.hasOwnProperty(i)&&((t=e[i])?this[i]=t(n):"target"===i?this.target=r:this[i]=n[i]);return this.isDefaultPrevented=(null!=n.defaultPrevented?n.defaultPrevented:!1===n.returnValue)?Vn:Wn,this.isPropagationStopped=Wn,this}function Yn(e,t,n,r){if(this.eventPool.length){var i=this.eventPool.pop();return this.call(i,e,t,n,r),i}return new this(e,t,n,r)}function $n(e){if(!(e instanceof this))throw Error(o(279));e.destructor(),10>this.eventPool.length&&this.eventPool.push(e)}function Kn(e){e.eventPool=[],e.getPooled=Yn,e.release=$n}i(Hn.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Vn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Vn)},persist:function(){this.isPersistent=Vn},isPersistent:Wn,destructor:function(){var e,t=this.constructor.Interface;for(e in t)this[e]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null,this.isPropagationStopped=this.isDefaultPrevented=Wn,this._dispatchInstances=this._dispatchListeners=null}}),Hn.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null},Hn.extend=function(e){function t(){}function n(){return r.apply(this,arguments)}var r=this;t.prototype=r.prototype;var a=new t;return i(a,n.prototype),n.prototype=a,n.prototype.constructor=n,n.Interface=i({},r.Interface,e),n.extend=r.extend,Kn(n),n},Kn(Hn);var Qn=Hn.extend({data:null}),Gn=Hn.extend({data:null}),qn=[9,13,27,32],Xn=E&&"CompositionEvent"in window,Zn=null;E&&"documentMode"in document&&(Zn=document.documentMode);var Jn=E&&"TextEvent"in window&&!Zn,er=E&&(!Xn||Zn&&8<Zn&&11>=Zn),tr=String.fromCharCode(32),nr={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["compositionend","keypress","textInput","paste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:"blur compositionend keydown keypress keyup mousedown".split(" ")},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},dependencies:"blur compositionstart keydown keypress keyup mousedown".split(" ")},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:"blur compositionupdate keydown keypress keyup mousedown".split(" ")}},rr=!1;function ir(e,t){switch(e){case"keyup":return-1!==qn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"blur":return!0;default:return!1}}function ar(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var or=!1;var ur={eventTypes:nr,extractEvents:function(e,t,n,r){var i;if(Xn)e:{switch(e){case"compositionstart":var a=nr.compositionStart;break e;case"compositionend":a=nr.compositionEnd;break e;case"compositionupdate":a=nr.compositionUpdate;break e}a=void 0}else or?ir(e,n)&&(a=nr.compositionEnd):"keydown"===e&&229===n.keyCode&&(a=nr.compositionStart);return a?(er&&"ko"!==n.locale&&(or||a!==nr.compositionStart?a===nr.compositionEnd&&or&&(i=jn()):(Rn="value"in(Un=r)?Un.value:Un.textContent,or=!0)),a=Qn.getPooled(a,t,n,r),i?a.data=i:null!==(i=ar(n))&&(a.data=i),Pn(a),i=a):i=null,(e=Jn?function(e,t){switch(e){case"compositionend":return ar(t);case"keypress":return 32!==t.which?null:(rr=!0,tr);case"textInput":return(e=t.data)===tr&&rr?null:e;default:return null}}(e,n):function(e,t){if(or)return"compositionend"===e||!Xn&&ir(e,t)?(e=jn(),zn=Rn=Un=null,or=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return er&&"ko"!==t.locale?null:t.data;default:return null}}(e,n))?((t=Gn.getPooled(nr.beforeInput,t,n,r)).data=e,Pn(t)):t=null,null===i?t:null===t?i:[i,t]}},sr={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function lr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!sr[e.type]:"textarea"===t}var cr={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:"blur change click focus input keydown keyup selectionchange".split(" ")}};function fr(e,t,n){return(e=Hn.getPooled(cr.change,e,t,n)).type="change",D(n),Pn(e),e}var dr=null,hr=null;function pr(e){ut(e)}function yr(e){if(we(Fn(e)))return e}function vr(e,t){if("change"===e)return t}var br=!1;function mr(){dr&&(dr.detachEvent("onpropertychange",gr),hr=dr=null)}function gr(e){if("value"===e.propertyName&&yr(hr))if(e=fr(hr,e,st(e)),U)ut(e);else{U=!0;try{L(pr,e)}finally{U=!1,z()}}}function kr(e,t,n){"focus"===e?(mr(),hr=n,(dr=t).attachEvent("onpropertychange",gr)):"blur"===e&&mr()}function wr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return yr(hr)}function _r(e,t){if("click"===e)return yr(t)}function xr(e,t){if("input"===e||"change"===e)return yr(t)}E&&(br=lt("input")&&(!document.documentMode||9<document.documentMode));var Sr={eventTypes:cr,_isInputEventSupported:br,extractEvents:function(e,t,n,r){var i=t?Fn(t):window,a=i.nodeName&&i.nodeName.toLowerCase();if("select"===a||"input"===a&&"file"===i.type)var o=vr;else if(lr(i))if(br)o=xr;else{o=wr;var u=kr}else(a=i.nodeName)&&"input"===a.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(o=_r);if(o&&(o=o(e,t)))return fr(o,n,r);u&&u(e,i,t),"blur"===e&&(e=i._wrapperState)&&e.controlled&&"number"===i.type&&Ee(i,"number",i.value)}},Tr=Hn.extend({view:null,detail:null}),Ir={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Er(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Ir[e])&&!!t[e]}function Ar(){return Er}var Fr=0,Or=0,Br=!1,Dr=!1,Cr=Tr.extend({screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:Ar,button:null,buttons:null,relatedTarget:function(e){return e.relatedTarget||(e.fromElement===e.srcElement?e.toElement:e.fromElement)},movementX:function(e){if("movementX"in e)return e.movementX;var t=Fr;return Fr=e.screenX,Br?"mousemove"===e.type?e.screenX-t:0:(Br=!0,0)},movementY:function(e){if("movementY"in e)return e.movementY;var t=Or;return Or=e.screenY,Dr?"mousemove"===e.type?e.screenY-t:0:(Dr=!0,0)}}),Lr=Cr.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),Mr={mouseEnter:{registrationName:"onMouseEnter",dependencies:["mouseout","mouseover"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["mouseout","mouseover"]},pointerEnter:{registrationName:"onPointerEnter",dependencies:["pointerout","pointerover"]},pointerLeave:{registrationName:"onPointerLeave",dependencies:["pointerout","pointerover"]}},Nr={eventTypes:Mr,extractEvents:function(e,t,n,r,i){var a="mouseover"===e||"pointerover"===e,o="mouseout"===e||"pointerout"===e;if(a&&0===(32&i)&&(n.relatedTarget||n.fromElement)||!o&&!a)return null;(a=r.window===r?r:(a=r.ownerDocument)?a.defaultView||a.parentWindow:window,o)?(o=t,null!==(t=(t=n.relatedTarget||n.toElement)?En(t):null)&&(t!==Je(t)||5!==t.tag&&6!==t.tag)&&(t=null)):o=null;if(o===t)return null;if("mouseout"===e||"mouseover"===e)var u=Cr,s=Mr.mouseLeave,l=Mr.mouseEnter,c="mouse";else"pointerout"!==e&&"pointerover"!==e||(u=Lr,s=Mr.pointerLeave,l=Mr.pointerEnter,c="pointer");if(e=null==o?a:Fn(o),a=null==t?a:Fn(t),(s=u.getPooled(s,o,n,r)).type=c+"leave",s.target=e,s.relatedTarget=a,(n=u.getPooled(l,t,n,r)).type=c+"enter",n.target=a,n.relatedTarget=e,c=t,(r=o)&&c)e:{for(l=c,o=0,e=u=r;e;e=Bn(e))o++;for(e=0,t=l;t;t=Bn(t))e++;for(;0<o-e;)u=Bn(u),o--;for(;0<e-o;)l=Bn(l),e--;for(;o--;){if(u===l||u===l.alternate)break e;u=Bn(u),l=Bn(l)}u=null}else u=null;for(l=u,u=[];r&&r!==l&&(null===(o=r.alternate)||o!==l);)u.push(r),r=Bn(r);for(r=[];c&&c!==l&&(null===(o=c.alternate)||o!==l);)r.push(c),c=Bn(c);for(c=0;c<u.length;c++)Mn(u[c],"bubbled",s);for(c=r.length;0<c--;)Mn(r[c],"captured",n);return 0===(64&i)?[s]:[s,n]}};var Pr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},Ur=Object.prototype.hasOwnProperty;function Rr(e,t){if(Pr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++)if(!Ur.call(t,n[r])||!Pr(e[n[r]],t[n[r]]))return!1;return!0}var zr=E&&"documentMode"in document&&11>=document.documentMode,jr={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},dependencies:"blur contextmenu dragend focus keydown keyup mousedown mouseup selectionchange".split(" ")}},Vr=null,Wr=null,Hr=null,Yr=!1;function $r(e,t){var n=t.window===t?t.document:9===t.nodeType?t:t.ownerDocument;return Yr||null==Vr||Vr!==cn(n)?null:("selectionStart"in(n=Vr)&&pn(n)?n={start:n.selectionStart,end:n.selectionEnd}:n={anchorNode:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset},Hr&&Rr(Hr,n)?null:(Hr=n,(e=Hn.getPooled(jr.select,Wr,e,t)).type="select",e.target=Vr,Pn(e),e))}var Kr={eventTypes:jr,extractEvents:function(e,t,n,r,i,a){if(!(a=!(i=a||(r.window===r?r.document:9===r.nodeType?r:r.ownerDocument)))){e:{i=Ze(i),a=T.onSelect;for(var o=0;o<a.length;o++)if(!i.has(a[o])){i=!1;break e}i=!0}a=!i}if(a)return null;switch(i=t?Fn(t):window,e){case"focus":(lr(i)||"true"===i.contentEditable)&&(Vr=i,Wr=t,Hr=null);break;case"blur":Hr=Wr=Vr=null;break;case"mousedown":Yr=!0;break;case"contextmenu":case"mouseup":case"dragend":return Yr=!1,$r(n,r);case"selectionchange":if(zr)break;case"keydown":case"keyup":return $r(n,r)}return null}},Qr=Hn.extend({animationName:null,elapsedTime:null,pseudoElement:null}),Gr=Hn.extend({clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),qr=Tr.extend({relatedTarget:null});function Xr(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}var Zr={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jr={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ei=Tr.extend({key:function(e){if(e.key){var t=Zr[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Xr(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Jr[e.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:Ar,charCode:function(e){return"keypress"===e.type?Xr(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Xr(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),ti=Cr.extend({dataTransfer:null}),ni=Tr.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:Ar}),ri=Hn.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),ii=Cr.extend({deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:null,deltaMode:null}),ai={eventTypes:Pt,extractEvents:function(e,t,n,r){var i=Ut.get(e);if(!i)return null;switch(e){case"keypress":if(0===Xr(n))return null;case"keydown":case"keyup":e=ei;break;case"blur":case"focus":e=qr;break;case"click":if(2===n.button)return null;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":e=Cr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":e=ti;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":e=ni;break;case $e:case Ke:case Qe:e=Qr;break;case Ge:e=ri;break;case"scroll":e=Tr;break;case"wheel":e=ii;break;case"copy":case"cut":case"paste":e=Gr;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":e=Lr;break;default:e=Hn}return Pn(t=e.getPooled(i,t,n,r)),t}};if(m)throw Error(o(101));m=Array.prototype.slice.call("ResponderEventPlugin SimpleEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin".split(" ")),k(),p=On,y=An,v=Fn,I({SimpleEventPlugin:ai,EnterLeaveEventPlugin:Nr,ChangeEventPlugin:Sr,SelectEventPlugin:Kr,BeforeInputEventPlugin:ur});var oi=[],ui=-1;function si(e){0>ui||(e.current=oi[ui],oi[ui]=null,ui--)}function li(e,t){ui++,oi[ui]=e.current,e.current=t}var ci={},fi={current:ci},di={current:!1},hi=ci;function pi(e,t){var n=e.type.contextTypes;if(!n)return ci;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,a={};for(i in n)a[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function yi(e){return null!==(e=e.childContextTypes)&&void 0!==e}function vi(){si(di),si(fi)}function bi(e,t,n){if(fi.current!==ci)throw Error(o(168));li(fi,t),li(di,n)}function mi(e,t,n){var r=e.stateNode;if(e=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in e))throw Error(o(108,ve(t)||"Unknown",a));return i({},n,{},r)}function gi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ci,hi=fi.current,li(fi,e),li(di,di.current),!0}function ki(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=mi(e,t,hi),r.__reactInternalMemoizedMergedChildContext=e,si(di),si(fi),li(fi,e)):si(di),li(di,n)}var wi=a.unstable_runWithPriority,_i=a.unstable_scheduleCallback,xi=a.unstable_cancelCallback,Si=a.unstable_requestPaint,Ti=a.unstable_now,Ii=a.unstable_getCurrentPriorityLevel,Ei=a.unstable_ImmediatePriority,Ai=a.unstable_UserBlockingPriority,Fi=a.unstable_NormalPriority,Oi=a.unstable_LowPriority,Bi=a.unstable_IdlePriority,Di={},Ci=a.unstable_shouldYield,Li=void 0!==Si?Si:function(){},Mi=null,Ni=null,Pi=!1,Ui=Ti(),Ri=1e4>Ui?Ti:function(){return Ti()-Ui};function zi(){switch(Ii()){case Ei:return 99;case Ai:return 98;case Fi:return 97;case Oi:return 96;case Bi:return 95;default:throw Error(o(332))}}function ji(e){switch(e){case 99:return Ei;case 98:return Ai;case 97:return Fi;case 96:return Oi;case 95:return Bi;default:throw Error(o(332))}}function Vi(e,t){return e=ji(e),wi(e,t)}function Wi(e,t,n){return e=ji(e),_i(e,t,n)}function Hi(e){return null===Mi?(Mi=[e],Ni=_i(Ei,$i)):Mi.push(e),Di}function Yi(){if(null!==Ni){var e=Ni;Ni=null,xi(e)}$i()}function $i(){if(!Pi&&null!==Mi){Pi=!0;var e=0;try{var t=Mi;Vi(99,(function(){for(;e<t.length;e++){var n=t[e];do{n=n(!0)}while(null!==n)}})),Mi=null}catch(n){throw null!==Mi&&(Mi=Mi.slice(e+1)),_i(Ei,Yi),n}finally{Pi=!1}}}function Ki(e,t,n){return 1073741821-(1+((1073741821-e+t/10)/(n/=10)|0))*n}function Qi(e,t){if(e&&e.defaultProps)for(var n in t=i({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}var Gi={current:null},qi=null,Xi=null,Zi=null;function Ji(){Zi=Xi=qi=null}function ea(e){var t=Gi.current;si(Gi),e.type._context._currentValue=t}function ta(e,t){for(;null!==e;){var n=e.alternate;if(e.childExpirationTime<t)e.childExpirationTime=t,null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t);else{if(!(null!==n&&n.childExpirationTime<t))break;n.childExpirationTime=t}e=e.return}}function na(e,t){qi=e,Zi=Xi=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(e.expirationTime>=t&&(Oo=!0),e.firstContext=null)}function ra(e,t){if(Zi!==e&&!1!==t&&0!==t)if("number"===typeof t&&1073741823!==t||(Zi=e,t=1073741823),t={context:e,observedBits:t,next:null},null===Xi){if(null===qi)throw Error(o(308));Xi=t,qi.dependencies={expirationTime:0,firstContext:t,responders:null}}else Xi=Xi.next=t;return e._currentValue}var ia=!1;function aa(e){e.updateQueue={baseState:e.memoizedState,baseQueue:null,shared:{pending:null},effects:null}}function oa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,baseQueue:e.baseQueue,shared:e.shared,effects:e.effects})}function ua(e,t){return(e={expirationTime:e,suspenseConfig:t,tag:0,payload:null,callback:null,next:null}).next=e}function sa(e,t){if(null!==(e=e.updateQueue)){var n=(e=e.shared).pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}}function la(e,t){var n=e.alternate;null!==n&&oa(n,e),null===(n=(e=e.updateQueue).baseQueue)?(e.baseQueue=t.next=t,t.next=t):(t.next=n.next,n.next=t)}function ca(e,t,n,r){var a=e.updateQueue;ia=!1;var o=a.baseQueue,u=a.shared.pending;if(null!==u){if(null!==o){var s=o.next;o.next=u.next,u.next=s}o=u,a.shared.pending=null,null!==(s=e.alternate)&&(null!==(s=s.updateQueue)&&(s.baseQueue=u))}if(null!==o){s=o.next;var l=a.baseState,c=0,f=null,d=null,h=null;if(null!==s)for(var p=s;;){if((u=p.expirationTime)<r){var y={expirationTime:p.expirationTime,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null};null===h?(d=h=y,f=l):h=h.next=y,u>c&&(c=u)}else{null!==h&&(h=h.next={expirationTime:1073741823,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null}),as(u,p.suspenseConfig);e:{var v=e,b=p;switch(u=t,y=n,b.tag){case 1:if("function"===typeof(v=b.payload)){l=v.call(y,l,u);break e}l=v;break e;case 3:v.effectTag=-4097&v.effectTag|64;case 0:if(null===(u="function"===typeof(v=b.payload)?v.call(y,l,u):v)||void 0===u)break e;l=i({},l,u);break e;case 2:ia=!0}}null!==p.callback&&(e.effectTag|=32,null===(u=a.effects)?a.effects=[p]:u.push(p))}if(null===(p=p.next)||p===s){if(null===(u=a.shared.pending))break;p=o.next=u.next,u.next=s,a.baseQueue=o=u,a.shared.pending=null}}null===h?f=l:h.next=d,a.baseState=f,a.baseQueue=h,os(c),e.expirationTime=c,e.memoizedState=l}}function fa(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=i,i=n,"function"!==typeof r)throw Error(o(191,r));r.call(i)}}}var da=q.ReactCurrentBatchConfig,ha=(new r.Component).refs;function pa(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:i({},t,n),e.memoizedState=n,0===e.expirationTime&&(e.updateQueue.baseState=n)}var ya={isMounted:function(e){return!!(e=e._reactInternalFiber)&&Je(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternalFiber;var r=$u(),i=da.suspense;(i=ua(r=Ku(r,e,i),i)).payload=t,void 0!==n&&null!==n&&(i.callback=n),sa(e,i),Qu(e,r)},enqueueReplaceState:function(e,t,n){e=e._reactInternalFiber;var r=$u(),i=da.suspense;(i=ua(r=Ku(r,e,i),i)).tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),sa(e,i),Qu(e,r)},enqueueForceUpdate:function(e,t){e=e._reactInternalFiber;var n=$u(),r=da.suspense;(r=ua(n=Ku(n,e,r),r)).tag=2,void 0!==t&&null!==t&&(r.callback=t),sa(e,r),Qu(e,n)}};function va(e,t,n,r,i,a,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,o):!t.prototype||!t.prototype.isPureReactComponent||(!Rr(n,r)||!Rr(i,a))}function ba(e,t,n){var r=!1,i=ci,a=t.contextType;return"object"===typeof a&&null!==a?a=ra(a):(i=yi(t)?hi:fi.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?pi(e,i):ci),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ya,e.stateNode=t,t._reactInternalFiber=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=a),t}function ma(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ya.enqueueReplaceState(t,t.state,null)}function ga(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs=ha,aa(e);var a=t.contextType;"object"===typeof a&&null!==a?i.context=ra(a):(a=yi(t)?hi:fi.current,i.context=pi(e,a)),ca(e,n,i,r),i.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(pa(e,t,a,n),i.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(t=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&ya.enqueueReplaceState(i,i.state,null),ca(e,n,i,r),i.state=e.memoizedState),"function"===typeof i.componentDidMount&&(e.effectTag|=4)}var ka=Array.isArray;function wa(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:((t=function(e){var t=r.refs;t===ha&&(t=r.refs={}),null===e?delete t[i]:t[i]=e})._stringRef=i,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function _a(e,t){if("textarea"!==e.type)throw Error(o(31,"[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t,""))}function xa(e){function t(t,n){if(e){var r=t.lastEffect;null!==r?(r.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n,n.nextEffect=null,n.effectTag=8}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Is(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.effectTag=2,n):r:(t.effectTag=2,n):n}function u(t){return e&&null===t.alternate&&(t.effectTag=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Fs(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function l(e,t,n,r){return null!==t&&t.elementType===n.type?((r=i(t,n.props)).ref=wa(e,t,n),r.return=e,r):((r=Es(n.type,n.key,n.props,null,e.mode,r)).ref=wa(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Os(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function f(e,t,n,r,a){return null===t||7!==t.tag?((t=As(n,e.mode,r,a)).return=e,t):((t=i(t,n)).return=e,t)}function d(e,t,n){if("string"===typeof t||"number"===typeof t)return(t=Fs(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case ee:return(n=Es(t.type,t.key,t.props,null,e.mode,n)).ref=wa(e,null,t),n.return=e,n;case te:return(t=Os(t,e.mode,n)).return=e,t}if(ka(t)||ye(t))return(t=As(t,e.mode,n,null)).return=e,t;_a(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"===typeof n||"number"===typeof n)return null!==i?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case ee:return n.key===i?n.type===ne?f(e,t,n.props.children,r,i):l(e,t,n,r):null;case te:return n.key===i?c(e,t,n,r):null}if(ka(n)||ye(n))return null!==i?null:f(e,t,n,r,null);_a(e,n)}return null}function p(e,t,n,r,i){if("string"===typeof r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case ee:return e=e.get(null===r.key?n:r.key)||null,r.type===ne?f(t,e,r.props.children,i,r.key):l(t,e,r,i);case te:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i)}if(ka(r)||ye(r))return f(t,e=e.get(n)||null,r,i,null);_a(t,r)}return null}function y(i,o,u,s){for(var l=null,c=null,f=o,y=o=0,v=null;null!==f&&y<u.length;y++){f.index>y?(v=f,f=null):v=f.sibling;var b=h(i,f,u[y],s);if(null===b){null===f&&(f=v);break}e&&f&&null===b.alternate&&t(i,f),o=a(b,o,y),null===c?l=b:c.sibling=b,c=b,f=v}if(y===u.length)return n(i,f),l;if(null===f){for(;y<u.length;y++)null!==(f=d(i,u[y],s))&&(o=a(f,o,y),null===c?l=f:c.sibling=f,c=f);return l}for(f=r(i,f);y<u.length;y++)null!==(v=p(f,i,y,u[y],s))&&(e&&null!==v.alternate&&f.delete(null===v.key?y:v.key),o=a(v,o,y),null===c?l=v:c.sibling=v,c=v);return e&&f.forEach((function(e){return t(i,e)})),l}function v(i,u,s,l){var c=ye(s);if("function"!==typeof c)throw Error(o(150));if(null==(s=c.call(s)))throw Error(o(151));for(var f=c=null,y=u,v=u=0,b=null,m=s.next();null!==y&&!m.done;v++,m=s.next()){y.index>v?(b=y,y=null):b=y.sibling;var g=h(i,y,m.value,l);if(null===g){null===y&&(y=b);break}e&&y&&null===g.alternate&&t(i,y),u=a(g,u,v),null===f?c=g:f.sibling=g,f=g,y=b}if(m.done)return n(i,y),c;if(null===y){for(;!m.done;v++,m=s.next())null!==(m=d(i,m.value,l))&&(u=a(m,u,v),null===f?c=m:f.sibling=m,f=m);return c}for(y=r(i,y);!m.done;v++,m=s.next())null!==(m=p(y,i,v,m.value,l))&&(e&&null!==m.alternate&&y.delete(null===m.key?v:m.key),u=a(m,u,v),null===f?c=m:f.sibling=m,f=m);return e&&y.forEach((function(e){return t(i,e)})),c}return function(e,r,a,s){var l="object"===typeof a&&null!==a&&a.type===ne&&null===a.key;l&&(a=a.props.children);var c="object"===typeof a&&null!==a;if(c)switch(a.$$typeof){case ee:e:{for(c=a.key,l=r;null!==l;){if(l.key===c){switch(l.tag){case 7:if(a.type===ne){n(e,l.sibling),(r=i(l,a.props.children)).return=e,e=r;break e}break;default:if(l.elementType===a.type){n(e,l.sibling),(r=i(l,a.props)).ref=wa(e,l,a),r.return=e,e=r;break e}}n(e,l);break}t(e,l),l=l.sibling}a.type===ne?((r=As(a.props.children,e.mode,s,a.key)).return=e,e=r):((s=Es(a.type,a.key,a.props,null,e.mode,s)).ref=wa(e,r,a),s.return=e,e=s)}return u(e);case te:e:{for(l=a.key;null!==r;){if(r.key===l){if(4===r.tag&&r.stateNode.containerInfo===a.containerInfo&&r.stateNode.implementation===a.implementation){n(e,r.sibling),(r=i(r,a.children||[])).return=e,e=r;break e}n(e,r);break}t(e,r),r=r.sibling}(r=Os(a,e.mode,s)).return=e,e=r}return u(e)}if("string"===typeof a||"number"===typeof a)return a=""+a,null!==r&&6===r.tag?(n(e,r.sibling),(r=i(r,a)).return=e,e=r):(n(e,r),(r=Fs(a,e.mode,s)).return=e,e=r),u(e);if(ka(a))return y(e,r,a,s);if(ye(a))return v(e,r,a,s);if(c&&_a(e,a),"undefined"===typeof a&&!l)switch(e.tag){case 1:case 0:throw e=e.type,Error(o(152,e.displayName||e.name||"Component"))}return n(e,r)}}var Sa=xa(!0),Ta=xa(!1),Ia={},Ea={current:Ia},Aa={current:Ia},Fa={current:Ia};function Oa(e){if(e===Ia)throw Error(o(174));return e}function Ba(e,t){switch(li(Fa,t),li(Aa,e),li(Ea,Ia),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Pe(null,"");break;default:t=Pe(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}si(Ea),li(Ea,t)}function Da(){si(Ea),si(Aa),si(Fa)}function Ca(e){Oa(Fa.current);var t=Oa(Ea.current),n=Pe(t,e.type);t!==n&&(li(Aa,e),li(Ea,n))}function La(e){Aa.current===e&&(si(Ea),si(Aa))}var Ma={current:0};function Na(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(64&t.effectTag))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Pa(e,t){return{responder:e,props:t}}var Ua=q.ReactCurrentDispatcher,Ra=q.ReactCurrentBatchConfig,za=0,ja=null,Va=null,Wa=null,Ha=!1;function Ya(){throw Error(o(321))}function $a(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Pr(e[n],t[n]))return!1;return!0}function Ka(e,t,n,r,i,a){if(za=a,ja=t,t.memoizedState=null,t.updateQueue=null,t.expirationTime=0,Ua.current=null===e||null===e.memoizedState?bo:mo,e=n(r,i),t.expirationTime===za){a=0;do{if(t.expirationTime=0,!(25>a))throw Error(o(301));a+=1,Wa=Va=null,t.updateQueue=null,Ua.current=go,e=n(r,i)}while(t.expirationTime===za)}if(Ua.current=vo,t=null!==Va&&null!==Va.next,za=0,Wa=Va=ja=null,Ha=!1,t)throw Error(o(300));return e}function Qa(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Wa?ja.memoizedState=Wa=e:Wa=Wa.next=e,Wa}function Ga(){if(null===Va){var e=ja.alternate;e=null!==e?e.memoizedState:null}else e=Va.next;var t=null===Wa?ja.memoizedState:Wa.next;if(null!==t)Wa=t,Va=e;else{if(null===e)throw Error(o(310));e={memoizedState:(Va=e).memoizedState,baseState:Va.baseState,baseQueue:Va.baseQueue,queue:Va.queue,next:null},null===Wa?ja.memoizedState=Wa=e:Wa=Wa.next=e}return Wa}function qa(e,t){return"function"===typeof t?t(e):t}function Xa(e){var t=Ga(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=Va,i=r.baseQueue,a=n.pending;if(null!==a){if(null!==i){var u=i.next;i.next=a.next,a.next=u}r.baseQueue=i=a,n.pending=null}if(null!==i){i=i.next,r=r.baseState;var s=u=a=null,l=i;do{var c=l.expirationTime;if(c<za){var f={expirationTime:l.expirationTime,suspenseConfig:l.suspenseConfig,action:l.action,eagerReducer:l.eagerReducer,eagerState:l.eagerState,next:null};null===s?(u=s=f,a=r):s=s.next=f,c>ja.expirationTime&&(ja.expirationTime=c,os(c))}else null!==s&&(s=s.next={expirationTime:1073741823,suspenseConfig:l.suspenseConfig,action:l.action,eagerReducer:l.eagerReducer,eagerState:l.eagerState,next:null}),as(c,l.suspenseConfig),r=l.eagerReducer===e?l.eagerState:e(r,l.action);l=l.next}while(null!==l&&l!==i);null===s?a=r:s.next=u,Pr(r,t.memoizedState)||(Oo=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=s,n.lastRenderedState=r}return[t.memoizedState,n.dispatch]}function Za(e){var t=Ga(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,a=t.memoizedState;if(null!==i){n.pending=null;var u=i=i.next;do{a=e(a,u.action),u=u.next}while(u!==i);Pr(a,t.memoizedState)||(Oo=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Ja(e){var t=Qa();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e=(e=t.queue={pending:null,dispatch:null,lastRenderedReducer:qa,lastRenderedState:e}).dispatch=yo.bind(null,ja,e),[t.memoizedState,e]}function eo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=ja.updateQueue)?(t={lastEffect:null},ja.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function to(){return Ga().memoizedState}function no(e,t,n,r){var i=Qa();ja.effectTag|=e,i.memoizedState=eo(1|t,n,void 0,void 0===r?null:r)}function ro(e,t,n,r){var i=Ga();r=void 0===r?null:r;var a=void 0;if(null!==Va){var o=Va.memoizedState;if(a=o.destroy,null!==r&&$a(r,o.deps))return void eo(t,n,a,r)}ja.effectTag|=e,i.memoizedState=eo(1|t,n,a,r)}function io(e,t){return no(516,4,e,t)}function ao(e,t){return ro(516,4,e,t)}function oo(e,t){return ro(4,2,e,t)}function uo(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function so(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,ro(4,2,uo.bind(null,t,e),n)}function lo(){}function co(e,t){return Qa().memoizedState=[e,void 0===t?null:t],e}function fo(e,t){var n=Ga();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&$a(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ho(e,t){var n=Ga();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&$a(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function po(e,t,n){var r=zi();Vi(98>r?98:r,(function(){e(!0)})),Vi(97<r?97:r,(function(){var r=Ra.suspense;Ra.suspense=void 0===t?null:t;try{e(!1),n()}finally{Ra.suspense=r}}))}function yo(e,t,n){var r=$u(),i=da.suspense;i={expirationTime:r=Ku(r,e,i),suspenseConfig:i,action:n,eagerReducer:null,eagerState:null,next:null};var a=t.pending;if(null===a?i.next=i:(i.next=a.next,a.next=i),t.pending=i,a=e.alternate,e===ja||null!==a&&a===ja)Ha=!0,i.expirationTime=za,ja.expirationTime=za;else{if(0===e.expirationTime&&(null===a||0===a.expirationTime)&&null!==(a=t.lastRenderedReducer))try{var o=t.lastRenderedState,u=a(o,n);if(i.eagerReducer=a,i.eagerState=u,Pr(u,o))return}catch(s){}Qu(e,r)}}var vo={readContext:ra,useCallback:Ya,useContext:Ya,useEffect:Ya,useImperativeHandle:Ya,useLayoutEffect:Ya,useMemo:Ya,useReducer:Ya,useRef:Ya,useState:Ya,useDebugValue:Ya,useResponder:Ya,useDeferredValue:Ya,useTransition:Ya},bo={readContext:ra,useCallback:co,useContext:ra,useEffect:io,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,no(4,2,uo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return no(4,2,e,t)},useMemo:function(e,t){var n=Qa();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Qa();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e=(e=r.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:t}).dispatch=yo.bind(null,ja,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Qa().memoizedState=e},useState:Ja,useDebugValue:lo,useResponder:Pa,useDeferredValue:function(e,t){var n=Ja(e),r=n[0],i=n[1];return io((function(){var n=Ra.suspense;Ra.suspense=void 0===t?null:t;try{i(e)}finally{Ra.suspense=n}}),[e,t]),r},useTransition:function(e){var t=Ja(!1),n=t[0];return t=t[1],[co(po.bind(null,t,e),[t,e]),n]}},mo={readContext:ra,useCallback:fo,useContext:ra,useEffect:ao,useImperativeHandle:so,useLayoutEffect:oo,useMemo:ho,useReducer:Xa,useRef:to,useState:function(){return Xa(qa)},useDebugValue:lo,useResponder:Pa,useDeferredValue:function(e,t){var n=Xa(qa),r=n[0],i=n[1];return ao((function(){var n=Ra.suspense;Ra.suspense=void 0===t?null:t;try{i(e)}finally{Ra.suspense=n}}),[e,t]),r},useTransition:function(e){var t=Xa(qa),n=t[0];return t=t[1],[fo(po.bind(null,t,e),[t,e]),n]}},go={readContext:ra,useCallback:fo,useContext:ra,useEffect:ao,useImperativeHandle:so,useLayoutEffect:oo,useMemo:ho,useReducer:Za,useRef:to,useState:function(){return Za(qa)},useDebugValue:lo,useResponder:Pa,useDeferredValue:function(e,t){var n=Za(qa),r=n[0],i=n[1];return ao((function(){var n=Ra.suspense;Ra.suspense=void 0===t?null:t;try{i(e)}finally{Ra.suspense=n}}),[e,t]),r},useTransition:function(e){var t=Za(qa),n=t[0];return t=t[1],[fo(po.bind(null,t,e),[t,e]),n]}},ko=null,wo=null,_o=!1;function xo(e,t){var n=Ss(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=t,n.return=e,n.effectTag=8,null!==e.lastEffect?(e.lastEffect.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n}function So(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,!0);case 13:default:return!1}}function To(e){if(_o){var t=wo;if(t){var n=t;if(!So(e,t)){if(!(t=wn(n.nextSibling))||!So(e,t))return e.effectTag=-1025&e.effectTag|2,_o=!1,void(ko=e);xo(ko,n)}ko=e,wo=wn(t.firstChild)}else e.effectTag=-1025&e.effectTag|2,_o=!1,ko=e}}function Io(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ko=e}function Eo(e){if(e!==ko)return!1;if(!_o)return Io(e),_o=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!mn(t,e.memoizedProps))for(t=wo;t;)xo(e,t),t=wn(t.nextSibling);if(Io(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){wo=wn(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}wo=null}}else wo=ko?wn(e.stateNode.nextSibling):null;return!0}function Ao(){wo=ko=null,_o=!1}var Fo=q.ReactCurrentOwner,Oo=!1;function Bo(e,t,n,r){t.child=null===e?Ta(t,null,n,r):Sa(t,e.child,n,r)}function Do(e,t,n,r,i){n=n.render;var a=t.ref;return na(t,i),r=Ka(e,t,n,r,a,i),null===e||Oo?(t.effectTag|=1,Bo(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Qo(e,t,i))}function Co(e,t,n,r,i,a){if(null===e){var o=n.type;return"function"!==typeof o||Ts(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Es(n.type,null,r,null,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Lo(e,t,o,r,i,a))}return o=e.child,i<a&&(i=o.memoizedProps,(n=null!==(n=n.compare)?n:Rr)(i,r)&&e.ref===t.ref)?Qo(e,t,a):(t.effectTag|=1,(e=Is(o,r)).ref=t.ref,e.return=t,t.child=e)}function Lo(e,t,n,r,i,a){return null!==e&&Rr(e.memoizedProps,r)&&e.ref===t.ref&&(Oo=!1,i<a)?(t.expirationTime=e.expirationTime,Qo(e,t,a)):No(e,t,n,r,a)}function Mo(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.effectTag|=128)}function No(e,t,n,r,i){var a=yi(n)?hi:fi.current;return a=pi(t,a),na(t,i),n=Ka(e,t,n,r,a,i),null===e||Oo?(t.effectTag|=1,Bo(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Qo(e,t,i))}function Po(e,t,n,r,i){if(yi(n)){var a=!0;gi(t)}else a=!1;if(na(t,i),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),ba(t,n,r),ga(t,n,r,i),r=!0;else if(null===e){var o=t.stateNode,u=t.memoizedProps;o.props=u;var s=o.context,l=n.contextType;"object"===typeof l&&null!==l?l=ra(l):l=pi(t,l=yi(n)?hi:fi.current);var c=n.getDerivedStateFromProps,f="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;f||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(u!==r||s!==l)&&ma(t,o,r,l),ia=!1;var d=t.memoizedState;o.state=d,ca(t,r,o,i),s=t.memoizedState,u!==r||d!==s||di.current||ia?("function"===typeof c&&(pa(t,n,c,r),s=t.memoizedState),(u=ia||va(t,n,u,r,d,s,l))?(f||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.effectTag|=4)):("function"===typeof o.componentDidMount&&(t.effectTag|=4),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=l,r=u):("function"===typeof o.componentDidMount&&(t.effectTag|=4),r=!1)}else o=t.stateNode,oa(e,t),u=t.memoizedProps,o.props=t.type===t.elementType?u:Qi(t.type,u),s=o.context,"object"===typeof(l=n.contextType)&&null!==l?l=ra(l):l=pi(t,l=yi(n)?hi:fi.current),(f="function"===typeof(c=n.getDerivedStateFromProps)||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(u!==r||s!==l)&&ma(t,o,r,l),ia=!1,s=t.memoizedState,o.state=s,ca(t,r,o,i),d=t.memoizedState,u!==r||s!==d||di.current||ia?("function"===typeof c&&(pa(t,n,c,r),d=t.memoizedState),(c=ia||va(t,n,u,r,s,d,l))?(f||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,d,l),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,d,l)),"function"===typeof o.componentDidUpdate&&(t.effectTag|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.effectTag|=256)):("function"!==typeof o.componentDidUpdate||u===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=4),"function"!==typeof o.getSnapshotBeforeUpdate||u===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=256),t.memoizedProps=r,t.memoizedState=d),o.props=r,o.state=d,o.context=l,r=c):("function"!==typeof o.componentDidUpdate||u===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=4),"function"!==typeof o.getSnapshotBeforeUpdate||u===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=256),r=!1);return Uo(e,t,n,r,a,i)}function Uo(e,t,n,r,i,a){Mo(e,t);var o=0!==(64&t.effectTag);if(!r&&!o)return i&&ki(t,n,!1),Qo(e,t,a);r=t.stateNode,Fo.current=t;var u=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.effectTag|=1,null!==e&&o?(t.child=Sa(t,e.child,null,a),t.child=Sa(t,null,u,a)):Bo(e,t,u,a),t.memoizedState=r.state,i&&ki(t,n,!0),t.child}function Ro(e){var t=e.stateNode;t.pendingContext?bi(0,t.pendingContext,t.pendingContext!==t.context):t.context&&bi(0,t.context,!1),Ba(e,t.containerInfo)}var zo,jo,Vo,Wo={dehydrated:null,retryTime:0};function Ho(e,t,n){var r,i=t.mode,a=t.pendingProps,o=Ma.current,u=!1;if((r=0!==(64&t.effectTag))||(r=0!==(2&o)&&(null===e||null!==e.memoizedState)),r?(u=!0,t.effectTag&=-65):null!==e&&null===e.memoizedState||void 0===a.fallback||!0===a.unstable_avoidThisFallback||(o|=1),li(Ma,1&o),null===e){if(void 0!==a.fallback&&To(t),u){if(u=a.fallback,(a=As(null,i,0,null)).return=t,0===(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(n=As(u,i,n,null)).return=t,a.sibling=n,t.memoizedState=Wo,t.child=a,n}return i=a.children,t.memoizedState=null,t.child=Ta(t,null,i,n)}if(null!==e.memoizedState){if(i=(e=e.child).sibling,u){if(a=a.fallback,(n=Is(e,e.pendingProps)).return=t,0===(2&t.mode)&&(u=null!==t.memoizedState?t.child.child:t.child)!==e.child)for(n.child=u;null!==u;)u.return=n,u=u.sibling;return(i=Is(i,a)).return=t,n.sibling=i,n.childExpirationTime=0,t.memoizedState=Wo,t.child=n,i}return n=Sa(t,e.child,a.children,n),t.memoizedState=null,t.child=n}if(e=e.child,u){if(u=a.fallback,(a=As(null,i,0,null)).return=t,a.child=e,null!==e&&(e.return=a),0===(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(n=As(u,i,n,null)).return=t,a.sibling=n,n.effectTag|=2,a.childExpirationTime=0,t.memoizedState=Wo,t.child=a,n}return t.memoizedState=null,t.child=Sa(t,e,a.children,n)}function Yo(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t),ta(e.return,t)}function $o(e,t,n,r,i,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailExpiration:0,tailMode:i,lastEffect:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailExpiration=0,o.tailMode=i,o.lastEffect=a)}function Ko(e,t,n){var r=t.pendingProps,i=r.revealOrder,a=r.tail;if(Bo(e,t,r.children,n),0!==(2&(r=Ma.current)))r=1&r|2,t.effectTag|=64;else{if(null!==e&&0!==(64&e.effectTag))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Yo(e,n);else if(19===e.tag)Yo(e,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(li(Ma,r),0===(2&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===Na(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),$o(t,!1,i,n,a,t.lastEffect);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===Na(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}$o(t,!0,n,null,a,t.lastEffect);break;case"together":$o(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}return t.child}function Qo(e,t,n){null!==e&&(t.dependencies=e.dependencies);var r=t.expirationTime;if(0!==r&&os(r),t.childExpirationTime<n)return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Is(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Is(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Go(e,t){switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qo(e,t,n){var r=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:return yi(t.type)&&vi(),null;case 3:return Da(),si(di),si(fi),(n=t.stateNode).pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||!Eo(t)||(t.effectTag|=4),null;case 5:La(t),n=Oa(Fa.current);var a=t.type;if(null!==e&&null!=t.stateNode)jo(e,t,a,r,n),e.ref!==t.ref&&(t.effectTag|=128);else{if(!r){if(null===t.stateNode)throw Error(o(166));return null}if(e=Oa(Ea.current),Eo(t)){r=t.stateNode,a=t.type;var u=t.memoizedProps;switch(r[Sn]=t,r[Tn]=u,a){case"iframe":case"object":case"embed":Kt("load",r);break;case"video":case"audio":for(e=0;e<qe.length;e++)Kt(qe[e],r);break;case"source":Kt("error",r);break;case"img":case"image":case"link":Kt("error",r),Kt("load",r);break;case"form":Kt("reset",r),Kt("submit",r);break;case"details":Kt("toggle",r);break;case"input":xe(r,u),Kt("invalid",r),sn(n,"onChange");break;case"select":r._wrapperState={wasMultiple:!!u.multiple},Kt("invalid",r),sn(n,"onChange");break;case"textarea":Be(r,u),Kt("invalid",r),sn(n,"onChange")}for(var s in an(a,u),e=null,u)if(u.hasOwnProperty(s)){var l=u[s];"children"===s?"string"===typeof l?r.textContent!==l&&(e=["children",l]):"number"===typeof l&&r.textContent!==""+l&&(e=["children",""+l]):S.hasOwnProperty(s)&&null!=l&&sn(n,s)}switch(a){case"input":ke(r),Ie(r,u,!0);break;case"textarea":ke(r),Ce(r);break;case"select":case"option":break;default:"function"===typeof u.onClick&&(r.onclick=ln)}n=e,t.updateQueue=n,null!==n&&(t.effectTag|=4)}else{switch(s=9===n.nodeType?n:n.ownerDocument,e===un&&(e=Ne(a)),e===un?"script"===a?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(a,{is:r.is}):(e=s.createElement(a),"select"===a&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,a),e[Sn]=t,e[Tn]=r,zo(e,t),t.stateNode=e,s=on(a,r),a){case"iframe":case"object":case"embed":Kt("load",e),l=r;break;case"video":case"audio":for(l=0;l<qe.length;l++)Kt(qe[l],e);l=r;break;case"source":Kt("error",e),l=r;break;case"img":case"image":case"link":Kt("error",e),Kt("load",e),l=r;break;case"form":Kt("reset",e),Kt("submit",e),l=r;break;case"details":Kt("toggle",e),l=r;break;case"input":xe(e,r),l=_e(e,r),Kt("invalid",e),sn(n,"onChange");break;case"option":l=Ae(e,r);break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=i({},r,{value:void 0}),Kt("invalid",e),sn(n,"onChange");break;case"textarea":Be(e,r),l=Oe(e,r),Kt("invalid",e),sn(n,"onChange");break;default:l=r}an(a,l);var c=l;for(u in c)if(c.hasOwnProperty(u)){var f=c[u];"style"===u?nn(e,f):"dangerouslySetInnerHTML"===u?null!=(f=f?f.__html:void 0)&&Re(e,f):"children"===u?"string"===typeof f?("textarea"!==a||""!==f)&&ze(e,f):"number"===typeof f&&ze(e,""+f):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(S.hasOwnProperty(u)?null!=f&&sn(n,u):null!=f&&X(e,u,f,s))}switch(a){case"input":ke(e),Ie(e,r,!1);break;case"textarea":ke(e),Ce(e);break;case"option":null!=r.value&&e.setAttribute("value",""+me(r.value));break;case"select":e.multiple=!!r.multiple,null!=(n=r.value)?Fe(e,!!r.multiple,n,!1):null!=r.defaultValue&&Fe(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof l.onClick&&(e.onclick=ln)}bn(a,r)&&(t.effectTag|=4)}null!==t.ref&&(t.effectTag|=128)}return null;case 6:if(e&&null!=t.stateNode)Vo(0,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));n=Oa(Fa.current),Oa(Ea.current),Eo(t)?(n=t.stateNode,r=t.memoizedProps,n[Sn]=t,n.nodeValue!==r&&(t.effectTag|=4)):((n=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Sn]=t,t.stateNode=n)}return null;case 13:return si(Ma),r=t.memoizedState,0!==(64&t.effectTag)?(t.expirationTime=n,t):(n=null!==r,r=!1,null===e?void 0!==t.memoizedProps.fallback&&Eo(t):(r=null!==(a=e.memoizedState),n||null===a||null!==(a=e.child.sibling)&&(null!==(u=t.firstEffect)?(t.firstEffect=a,a.nextEffect=u):(t.firstEffect=t.lastEffect=a,a.nextEffect=null),a.effectTag=8)),n&&!r&&0!==(2&t.mode)&&(null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||0!==(1&Ma.current)?Eu===ku&&(Eu=wu):(Eu!==ku&&Eu!==wu||(Eu=_u),0!==Du&&null!==Su&&(Cs(Su,Iu),Ls(Su,Du)))),(n||r)&&(t.effectTag|=4),null);case 4:return Da(),null;case 10:return ea(t),null;case 17:return yi(t.type)&&vi(),null;case 19:if(si(Ma),null===(r=t.memoizedState))return null;if(a=0!==(64&t.effectTag),null===(u=r.rendering)){if(a)Go(r,!1);else if(Eu!==ku||null!==e&&0!==(64&e.effectTag))for(u=t.child;null!==u;){if(null!==(e=Na(u))){for(t.effectTag|=64,Go(r,!1),null!==(a=e.updateQueue)&&(t.updateQueue=a,t.effectTag|=4),null===r.lastEffect&&(t.firstEffect=null),t.lastEffect=r.lastEffect,r=t.child;null!==r;)u=n,(a=r).effectTag&=2,a.nextEffect=null,a.firstEffect=null,a.lastEffect=null,null===(e=a.alternate)?(a.childExpirationTime=0,a.expirationTime=u,a.child=null,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null):(a.childExpirationTime=e.childExpirationTime,a.expirationTime=e.expirationTime,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,u=e.dependencies,a.dependencies=null===u?null:{expirationTime:u.expirationTime,firstContext:u.firstContext,responders:u.responders}),r=r.sibling;return li(Ma,1&Ma.current|2),t.child}u=u.sibling}}else{if(!a)if(null!==(e=Na(u))){if(t.effectTag|=64,a=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.effectTag|=4),Go(r,!0),null===r.tail&&"hidden"===r.tailMode&&!u.alternate)return null!==(t=t.lastEffect=r.lastEffect)&&(t.nextEffect=null),null}else 2*Ri()-r.renderingStartTime>r.tailExpiration&&1<n&&(t.effectTag|=64,a=!0,Go(r,!1),t.expirationTime=t.childExpirationTime=n-1);r.isBackwards?(u.sibling=t.child,t.child=u):(null!==(n=r.last)?n.sibling=u:t.child=u,r.last=u)}return null!==r.tail?(0===r.tailExpiration&&(r.tailExpiration=Ri()+500),n=r.tail,r.rendering=n,r.tail=n.sibling,r.lastEffect=t.lastEffect,r.renderingStartTime=Ri(),n.sibling=null,t=Ma.current,li(Ma,a?1&t|2:1&t),n):null}throw Error(o(156,t.tag))}function Xo(e){switch(e.tag){case 1:yi(e.type)&&vi();var t=e.effectTag;return 4096&t?(e.effectTag=-4097&t|64,e):null;case 3:if(Da(),si(di),si(fi),0!==(64&(t=e.effectTag)))throw Error(o(285));return e.effectTag=-4097&t|64,e;case 5:return La(e),null;case 13:return si(Ma),4096&(t=e.effectTag)?(e.effectTag=-4097&t|64,e):null;case 19:return si(Ma),null;case 4:return Da(),null;case 10:return ea(e),null;default:return null}}function Zo(e,t){return{value:e,source:t,stack:be(t)}}zo=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},jo=function(e,t,n,r,a){var o=e.memoizedProps;if(o!==r){var u,s,l=t.stateNode;switch(Oa(Ea.current),e=null,n){case"input":o=_e(l,o),r=_e(l,r),e=[];break;case"option":o=Ae(l,o),r=Ae(l,r),e=[];break;case"select":o=i({},o,{value:void 0}),r=i({},r,{value:void 0}),e=[];break;case"textarea":o=Oe(l,o),r=Oe(l,r),e=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(l.onclick=ln)}for(u in an(n,r),n=null,o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&null!=o[u])if("style"===u)for(s in l=o[u])l.hasOwnProperty(s)&&(n||(n={}),n[s]="");else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(S.hasOwnProperty(u)?e||(e=[]):(e=e||[]).push(u,null));for(u in r){var c=r[u];if(l=null!=o?o[u]:void 0,r.hasOwnProperty(u)&&c!==l&&(null!=c||null!=l))if("style"===u)if(l){for(s in l)!l.hasOwnProperty(s)||c&&c.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in c)c.hasOwnProperty(s)&&l[s]!==c[s]&&(n||(n={}),n[s]=c[s])}else n||(e||(e=[]),e.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,l=l?l.__html:void 0,null!=c&&l!==c&&(e=e||[]).push(u,c)):"children"===u?l===c||"string"!==typeof c&&"number"!==typeof c||(e=e||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(S.hasOwnProperty(u)?(null!=c&&sn(a,u),e||l===c||(e=[])):(e=e||[]).push(u,c))}n&&(e=e||[]).push("style",n),a=e,(t.updateQueue=a)&&(t.effectTag|=4)}},Vo=function(e,t,n,r){n!==r&&(t.effectTag|=4)};var Jo="function"===typeof WeakSet?WeakSet:Set;function eu(e,t){var n=t.source,r=t.stack;null===r&&null!==n&&(r=be(n)),null!==n&&ve(n.type),t=t.value,null!==e&&1===e.tag&&ve(e.type);try{console.error(t)}catch(i){setTimeout((function(){throw i}))}}function tu(e){var t=e.ref;if(null!==t)if("function"===typeof t)try{t(null)}catch(n){ms(e,n)}else t.current=null}function nu(e,t){switch(t.tag){case 0:case 11:case 15:case 22:return;case 1:if(256&t.effectTag&&null!==e){var n=e.memoizedProps,r=e.memoizedState;t=(e=t.stateNode).getSnapshotBeforeUpdate(t.elementType===t.type?n:Qi(t.type,n),r),e.__reactInternalSnapshotBeforeUpdate=t}return;case 3:case 5:case 6:case 4:case 17:return}throw Error(o(163))}function ru(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.destroy;n.destroy=void 0,void 0!==r&&r()}n=n.next}while(n!==t)}}function iu(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function au(e,t,n){switch(n.tag){case 0:case 11:case 15:case 22:return void iu(3,n);case 1:if(e=n.stateNode,4&n.effectTag)if(null===t)e.componentDidMount();else{var r=n.elementType===n.type?t.memoizedProps:Qi(n.type,t.memoizedProps);e.componentDidUpdate(r,t.memoizedState,e.__reactInternalSnapshotBeforeUpdate)}return void(null!==(t=n.updateQueue)&&fa(n,t,e));case 3:if(null!==(t=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}fa(n,t,e)}return;case 5:return e=n.stateNode,void(null===t&&4&n.effectTag&&bn(n.type,n.memoizedProps)&&e.focus());case 6:case 4:case 12:return;case 13:return void(null===n.memoizedState&&(n=n.alternate,null!==n&&(n=n.memoizedState,null!==n&&(n=n.dehydrated,null!==n&&Nt(n)))));case 19:case 17:case 20:case 21:return}throw Error(o(163))}function ou(e,t,n){switch("function"===typeof _s&&_s(t),t.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var r=e.next;Vi(97<n?97:n,(function(){var e=r;do{var n=e.destroy;if(void 0!==n){var i=t;try{n()}catch(a){ms(i,a)}}e=e.next}while(e!==r)}))}break;case 1:tu(t),"function"===typeof(n=t.stateNode).componentWillUnmount&&function(e,t){try{t.props=e.memoizedProps,t.state=e.memoizedState,t.componentWillUnmount()}catch(n){ms(e,n)}}(t,n);break;case 5:tu(t);break;case 4:cu(e,t,n)}}function uu(e){var t=e.alternate;e.return=null,e.child=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.alternate=null,e.firstEffect=null,e.lastEffect=null,e.pendingProps=null,e.memoizedProps=null,e.stateNode=null,null!==t&&uu(t)}function su(e){return 5===e.tag||3===e.tag||4===e.tag}function lu(e){e:{for(var t=e.return;null!==t;){if(su(t)){var n=t;break e}t=t.return}throw Error(o(160))}switch(t=n.stateNode,n.tag){case 5:var r=!1;break;case 3:case 4:t=t.containerInfo,r=!0;break;default:throw Error(o(161))}16&n.effectTag&&(ze(t,""),n.effectTag&=-17);e:t:for(n=e;;){for(;null===n.sibling;){if(null===n.return||su(n.return)){n=null;break e}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.effectTag)continue t;if(null===n.child||4===n.tag)continue t;n.child.return=n,n=n.child}if(!(2&n.effectTag)){n=n.stateNode;break e}}r?function e(t,n,r){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,n?8===r.nodeType?r.parentNode.insertBefore(t,n):r.insertBefore(t,n):(8===r.nodeType?(n=r.parentNode).insertBefore(t,r):(n=r).appendChild(t),null!==(r=r._reactRootContainer)&&void 0!==r||null!==n.onclick||(n.onclick=ln));else if(4!==i&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t):function e(t,n,r){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,n?r.insertBefore(t,n):r.appendChild(t);else if(4!==i&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t)}function cu(e,t,n){for(var r,i,a=t,u=!1;;){if(!u){u=a.return;e:for(;;){if(null===u)throw Error(o(160));switch(r=u.stateNode,u.tag){case 5:i=!1;break e;case 3:case 4:r=r.containerInfo,i=!0;break e}u=u.return}u=!0}if(5===a.tag||6===a.tag){e:for(var s=e,l=a,c=n,f=l;;)if(ou(s,f,c),null!==f.child&&4!==f.tag)f.child.return=f,f=f.child;else{if(f===l)break e;for(;null===f.sibling;){if(null===f.return||f.return===l)break e;f=f.return}f.sibling.return=f.return,f=f.sibling}i?(s=r,l=a.stateNode,8===s.nodeType?s.parentNode.removeChild(l):s.removeChild(l)):r.removeChild(a.stateNode)}else if(4===a.tag){if(null!==a.child){r=a.stateNode.containerInfo,i=!0,a.child.return=a,a=a.child;continue}}else if(ou(e,a,n),null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break;for(;null===a.sibling;){if(null===a.return||a.return===t)return;4===(a=a.return).tag&&(u=!1)}a.sibling.return=a.return,a=a.sibling}}function fu(e,t){switch(t.tag){case 0:case 11:case 14:case 15:case 22:return void ru(3,t);case 1:return;case 5:var n=t.stateNode;if(null!=n){var r=t.memoizedProps,i=null!==e?e.memoizedProps:r;e=t.type;var a=t.updateQueue;if(t.updateQueue=null,null!==a){for(n[Tn]=r,"input"===e&&"radio"===r.type&&null!=r.name&&Se(n,r),on(e,i),t=on(e,r),i=0;i<a.length;i+=2){var u=a[i],s=a[i+1];"style"===u?nn(n,s):"dangerouslySetInnerHTML"===u?Re(n,s):"children"===u?ze(n,s):X(n,u,s,t)}switch(e){case"input":Te(n,r);break;case"textarea":De(n,r);break;case"select":t=n._wrapperState.wasMultiple,n._wrapperState.wasMultiple=!!r.multiple,null!=(e=r.value)?Fe(n,!!r.multiple,e,!1):t!==!!r.multiple&&(null!=r.defaultValue?Fe(n,!!r.multiple,r.defaultValue,!0):Fe(n,!!r.multiple,r.multiple?[]:"",!1))}}}return;case 6:if(null===t.stateNode)throw Error(o(162));return void(t.stateNode.nodeValue=t.memoizedProps);case 3:return void((t=t.stateNode).hydrate&&(t.hydrate=!1,Nt(t.containerInfo)));case 12:return;case 13:if(n=t,null===t.memoizedState?r=!1:(r=!0,n=t.child,Lu=Ri()),null!==n)e:for(e=n;;){if(5===e.tag)a=e.stateNode,r?"function"===typeof(a=a.style).setProperty?a.setProperty("display","none","important"):a.display="none":(a=e.stateNode,i=void 0!==(i=e.memoizedProps.style)&&null!==i&&i.hasOwnProperty("display")?i.display:null,a.style.display=tn("display",i));else if(6===e.tag)e.stateNode.nodeValue=r?"":e.memoizedProps;else{if(13===e.tag&&null!==e.memoizedState&&null===e.memoizedState.dehydrated){(a=e.child.sibling).return=e,e=a;continue}if(null!==e.child){e.child.return=e,e=e.child;continue}}if(e===n)break;for(;null===e.sibling;){if(null===e.return||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}return void du(t);case 19:return void du(t);case 17:return}throw Error(o(163))}function du(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Jo),t.forEach((function(t){var r=ks.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}var hu="function"===typeof WeakMap?WeakMap:Map;function pu(e,t,n){(n=ua(n,null)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Nu||(Nu=!0,Pu=r),eu(e,t)},n}function yu(e,t,n){(n=ua(n,null)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var i=t.value;n.payload=function(){return eu(e,t),r(i)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){"function"!==typeof r&&(null===Uu?Uu=new Set([this]):Uu.add(this),eu(e,t));var n=t.stack;this.componentDidCatch(t.value,{componentStack:null!==n?n:""})}),n}var vu,bu=Math.ceil,mu=q.ReactCurrentDispatcher,gu=q.ReactCurrentOwner,ku=0,wu=3,_u=4,xu=0,Su=null,Tu=null,Iu=0,Eu=ku,Au=null,Fu=1073741823,Ou=1073741823,Bu=null,Du=0,Cu=!1,Lu=0,Mu=null,Nu=!1,Pu=null,Uu=null,Ru=!1,zu=null,ju=90,Vu=null,Wu=0,Hu=null,Yu=0;function $u(){return 0!==(48&xu)?1073741821-(Ri()/10|0):0!==Yu?Yu:Yu=1073741821-(Ri()/10|0)}function Ku(e,t,n){if(0===(2&(t=t.mode)))return 1073741823;var r=zi();if(0===(4&t))return 99===r?1073741823:1073741822;if(0!==(16&xu))return Iu;if(null!==n)e=Ki(e,0|n.timeoutMs||5e3,250);else switch(r){case 99:e=1073741823;break;case 98:e=Ki(e,150,100);break;case 97:case 96:e=Ki(e,5e3,250);break;case 95:e=2;break;default:throw Error(o(326))}return null!==Su&&e===Iu&&--e,e}function Qu(e,t){if(50<Wu)throw Wu=0,Hu=null,Error(o(185));if(null!==(e=Gu(e,t))){var n=zi();1073741823===t?0!==(8&xu)&&0===(48&xu)?Ju(e):(Xu(e),0===xu&&Yi()):Xu(e),0===(4&xu)||98!==n&&99!==n||(null===Vu?Vu=new Map([[e,t]]):(void 0===(n=Vu.get(e))||n>t)&&Vu.set(e,t))}}function Gu(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t);var r=e.return,i=null;if(null===r&&3===e.tag)i=e.stateNode;else for(;null!==r;){if(n=r.alternate,r.childExpirationTime<t&&(r.childExpirationTime=t),null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t),null===r.return&&3===r.tag){i=r.stateNode;break}r=r.return}return null!==i&&(Su===i&&(os(t),Eu===_u&&Cs(i,Iu)),Ls(i,t)),i}function qu(e){var t=e.lastExpiredTime;if(0!==t)return t;if(!Ds(e,t=e.firstPendingTime))return t;var n=e.lastPingedTime;return 2>=(e=n>(e=e.nextKnownPendingLevel)?n:e)&&t!==e?0:e}function Xu(e){if(0!==e.lastExpiredTime)e.callbackExpirationTime=1073741823,e.callbackPriority=99,e.callbackNode=Hi(Ju.bind(null,e));else{var t=qu(e),n=e.callbackNode;if(0===t)null!==n&&(e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90);else{var r=$u();if(1073741823===t?r=99:1===t||2===t?r=95:r=0>=(r=10*(1073741821-t)-10*(1073741821-r))?99:250>=r?98:5250>=r?97:95,null!==n){var i=e.callbackPriority;if(e.callbackExpirationTime===t&&i>=r)return;n!==Di&&xi(n)}e.callbackExpirationTime=t,e.callbackPriority=r,t=1073741823===t?Hi(Ju.bind(null,e)):Wi(r,Zu.bind(null,e),{timeout:10*(1073741821-t)-Ri()}),e.callbackNode=t}}}function Zu(e,t){if(Yu=0,t)return Ms(e,t=$u()),Xu(e),null;var n=qu(e);if(0!==n){if(t=e.callbackNode,0!==(48&xu))throw Error(o(327));if(ys(),e===Su&&n===Iu||ns(e,n),null!==Tu){var r=xu;xu|=16;for(var i=is();;)try{ss();break}catch(s){rs(e,s)}if(Ji(),xu=r,mu.current=i,1===Eu)throw t=Au,ns(e,n),Cs(e,n),Xu(e),t;if(null===Tu)switch(i=e.finishedWork=e.current.alternate,e.finishedExpirationTime=n,r=Eu,Su=null,r){case ku:case 1:throw Error(o(345));case 2:Ms(e,2<n?2:n);break;case wu:if(Cs(e,n),n===(r=e.lastSuspendedTime)&&(e.nextKnownPendingLevel=fs(i)),1073741823===Fu&&10<(i=Lu+500-Ri())){if(Cu){var a=e.lastPingedTime;if(0===a||a>=n){e.lastPingedTime=n,ns(e,n);break}}if(0!==(a=qu(e))&&a!==n)break;if(0!==r&&r!==n){e.lastPingedTime=r;break}e.timeoutHandle=gn(ds.bind(null,e),i);break}ds(e);break;case _u:if(Cs(e,n),n===(r=e.lastSuspendedTime)&&(e.nextKnownPendingLevel=fs(i)),Cu&&(0===(i=e.lastPingedTime)||i>=n)){e.lastPingedTime=n,ns(e,n);break}if(0!==(i=qu(e))&&i!==n)break;if(0!==r&&r!==n){e.lastPingedTime=r;break}if(1073741823!==Ou?r=10*(1073741821-Ou)-Ri():1073741823===Fu?r=0:(r=10*(1073741821-Fu)-5e3,0>(r=(i=Ri())-r)&&(r=0),(n=10*(1073741821-n)-i)<(r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*bu(r/1960))-r)&&(r=n)),10<r){e.timeoutHandle=gn(ds.bind(null,e),r);break}ds(e);break;case 5:if(1073741823!==Fu&&null!==Bu){a=Fu;var u=Bu;if(0>=(r=0|u.busyMinDurationMs)?r=0:(i=0|u.busyDelayMs,r=(a=Ri()-(10*(1073741821-a)-(0|u.timeoutMs||5e3)))<=i?0:i+r-a),10<r){Cs(e,n),e.timeoutHandle=gn(ds.bind(null,e),r);break}}ds(e);break;default:throw Error(o(329))}if(Xu(e),e.callbackNode===t)return Zu.bind(null,e)}}return null}function Ju(e){var t=e.lastExpiredTime;if(t=0!==t?t:1073741823,0!==(48&xu))throw Error(o(327));if(ys(),e===Su&&t===Iu||ns(e,t),null!==Tu){var n=xu;xu|=16;for(var r=is();;)try{us();break}catch(i){rs(e,i)}if(Ji(),xu=n,mu.current=r,1===Eu)throw n=Au,ns(e,t),Cs(e,t),Xu(e),n;if(null!==Tu)throw Error(o(261));e.finishedWork=e.current.alternate,e.finishedExpirationTime=t,Su=null,ds(e),Xu(e)}return null}function es(e,t){var n=xu;xu|=1;try{return e(t)}finally{0===(xu=n)&&Yi()}}function ts(e,t){var n=xu;xu&=-2,xu|=8;try{return e(t)}finally{0===(xu=n)&&Yi()}}function ns(e,t){e.finishedWork=null,e.finishedExpirationTime=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,kn(n)),null!==Tu)for(n=Tu.return;null!==n;){var r=n;switch(r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&vi();break;case 3:Da(),si(di),si(fi);break;case 5:La(r);break;case 4:Da();break;case 13:case 19:si(Ma);break;case 10:ea(r)}n=n.return}Su=e,Tu=Is(e.current,null),Iu=t,Eu=ku,Au=null,Ou=Fu=1073741823,Bu=null,Du=0,Cu=!1}function rs(e,t){for(;;){try{if(Ji(),Ua.current=vo,Ha)for(var n=ja.memoizedState;null!==n;){var r=n.queue;null!==r&&(r.pending=null),n=n.next}if(za=0,Wa=Va=ja=null,Ha=!1,null===Tu||null===Tu.return)return Eu=1,Au=t,Tu=null;e:{var i=e,a=Tu.return,o=Tu,u=t;if(t=Iu,o.effectTag|=2048,o.firstEffect=o.lastEffect=null,null!==u&&"object"===typeof u&&"function"===typeof u.then){var s=u;if(0===(2&o.mode)){var l=o.alternate;l?(o.updateQueue=l.updateQueue,o.memoizedState=l.memoizedState,o.expirationTime=l.expirationTime):(o.updateQueue=null,o.memoizedState=null)}var c=0!==(1&Ma.current),f=a;do{var d;if(d=13===f.tag){var h=f.memoizedState;if(null!==h)d=null!==h.dehydrated;else{var p=f.memoizedProps;d=void 0!==p.fallback&&(!0!==p.unstable_avoidThisFallback||!c)}}if(d){var y=f.updateQueue;if(null===y){var v=new Set;v.add(s),f.updateQueue=v}else y.add(s);if(0===(2&f.mode)){if(f.effectTag|=64,o.effectTag&=-2981,1===o.tag)if(null===o.alternate)o.tag=17;else{var b=ua(1073741823,null);b.tag=2,sa(o,b)}o.expirationTime=1073741823;break e}u=void 0,o=t;var m=i.pingCache;if(null===m?(m=i.pingCache=new hu,u=new Set,m.set(s,u)):void 0===(u=m.get(s))&&(u=new Set,m.set(s,u)),!u.has(o)){u.add(o);var g=gs.bind(null,i,s,o);s.then(g,g)}f.effectTag|=4096,f.expirationTime=t;break e}f=f.return}while(null!==f);u=Error((ve(o.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display."+be(o))}5!==Eu&&(Eu=2),u=Zo(u,o),f=a;do{switch(f.tag){case 3:s=u,f.effectTag|=4096,f.expirationTime=t,la(f,pu(f,s,t));break e;case 1:s=u;var k=f.type,w=f.stateNode;if(0===(64&f.effectTag)&&("function"===typeof k.getDerivedStateFromError||null!==w&&"function"===typeof w.componentDidCatch&&(null===Uu||!Uu.has(w)))){f.effectTag|=4096,f.expirationTime=t,la(f,yu(f,s,t));break e}}f=f.return}while(null!==f)}Tu=cs(Tu)}catch(_){t=_;continue}break}}function is(){var e=mu.current;return mu.current=vo,null===e?vo:e}function as(e,t){e<Fu&&2<e&&(Fu=e),null!==t&&e<Ou&&2<e&&(Ou=e,Bu=t)}function os(e){e>Du&&(Du=e)}function us(){for(;null!==Tu;)Tu=ls(Tu)}function ss(){for(;null!==Tu&&!Ci();)Tu=ls(Tu)}function ls(e){var t=vu(e.alternate,e,Iu);return e.memoizedProps=e.pendingProps,null===t&&(t=cs(e)),gu.current=null,t}function cs(e){Tu=e;do{var t=Tu.alternate;if(e=Tu.return,0===(2048&Tu.effectTag)){if(t=qo(t,Tu,Iu),1===Iu||1!==Tu.childExpirationTime){for(var n=0,r=Tu.child;null!==r;){var i=r.expirationTime,a=r.childExpirationTime;i>n&&(n=i),a>n&&(n=a),r=r.sibling}Tu.childExpirationTime=n}if(null!==t)return t;null!==e&&0===(2048&e.effectTag)&&(null===e.firstEffect&&(e.firstEffect=Tu.firstEffect),null!==Tu.lastEffect&&(null!==e.lastEffect&&(e.lastEffect.nextEffect=Tu.firstEffect),e.lastEffect=Tu.lastEffect),1<Tu.effectTag&&(null!==e.lastEffect?e.lastEffect.nextEffect=Tu:e.firstEffect=Tu,e.lastEffect=Tu))}else{if(null!==(t=Xo(Tu)))return t.effectTag&=2047,t;null!==e&&(e.firstEffect=e.lastEffect=null,e.effectTag|=2048)}if(null!==(t=Tu.sibling))return t;Tu=e}while(null!==Tu);return Eu===ku&&(Eu=5),null}function fs(e){var t=e.expirationTime;return t>(e=e.childExpirationTime)?t:e}function ds(e){var t=zi();return Vi(99,hs.bind(null,e,t)),null}function hs(e,t){do{ys()}while(null!==zu);if(0!==(48&xu))throw Error(o(327));var n=e.finishedWork,r=e.finishedExpirationTime;if(null===n)return null;if(e.finishedWork=null,e.finishedExpirationTime=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90,e.nextKnownPendingLevel=0;var i=fs(n);if(e.firstPendingTime=i,r<=e.lastSuspendedTime?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:r<=e.firstSuspendedTime&&(e.firstSuspendedTime=r-1),r<=e.lastPingedTime&&(e.lastPingedTime=0),r<=e.lastExpiredTime&&(e.lastExpiredTime=0),e===Su&&(Tu=Su=null,Iu=0),1<n.effectTag?null!==n.lastEffect?(n.lastEffect.nextEffect=n,i=n.firstEffect):i=n:i=n.firstEffect,null!==i){var a=xu;xu|=32,gu.current=null,yn=$t;var u=hn();if(pn(u)){if("selectionStart"in u)var s={start:u.selectionStart,end:u.selectionEnd};else e:{var l=(s=(s=u.ownerDocument)&&s.defaultView||window).getSelection&&s.getSelection();if(l&&0!==l.rangeCount){s=l.anchorNode;var c=l.anchorOffset,f=l.focusNode;l=l.focusOffset;try{s.nodeType,f.nodeType}catch(I){s=null;break e}var d=0,h=-1,p=-1,y=0,v=0,b=u,m=null;t:for(;;){for(var g;b!==s||0!==c&&3!==b.nodeType||(h=d+c),b!==f||0!==l&&3!==b.nodeType||(p=d+l),3===b.nodeType&&(d+=b.nodeValue.length),null!==(g=b.firstChild);)m=b,b=g;for(;;){if(b===u)break t;if(m===s&&++y===c&&(h=d),m===f&&++v===l&&(p=d),null!==(g=b.nextSibling))break;m=(b=m).parentNode}b=g}s=-1===h||-1===p?null:{start:h,end:p}}else s=null}s=s||{start:0,end:0}}else s=null;vn={activeElementDetached:null,focusedElem:u,selectionRange:s},$t=!1,Mu=i;do{try{ps()}catch(I){if(null===Mu)throw Error(o(330));ms(Mu,I),Mu=Mu.nextEffect}}while(null!==Mu);Mu=i;do{try{for(u=e,s=t;null!==Mu;){var k=Mu.effectTag;if(16&k&&ze(Mu.stateNode,""),128&k){var w=Mu.alternate;if(null!==w){var _=w.ref;null!==_&&("function"===typeof _?_(null):_.current=null)}}switch(1038&k){case 2:lu(Mu),Mu.effectTag&=-3;break;case 6:lu(Mu),Mu.effectTag&=-3,fu(Mu.alternate,Mu);break;case 1024:Mu.effectTag&=-1025;break;case 1028:Mu.effectTag&=-1025,fu(Mu.alternate,Mu);break;case 4:fu(Mu.alternate,Mu);break;case 8:cu(u,c=Mu,s),uu(c)}Mu=Mu.nextEffect}}catch(I){if(null===Mu)throw Error(o(330));ms(Mu,I),Mu=Mu.nextEffect}}while(null!==Mu);if(_=vn,w=hn(),k=_.focusedElem,s=_.selectionRange,w!==k&&k&&k.ownerDocument&&function e(t,n){return!(!t||!n)&&(t===n||(!t||3!==t.nodeType)&&(n&&3===n.nodeType?e(t,n.parentNode):"contains"in t?t.contains(n):!!t.compareDocumentPosition&&!!(16&t.compareDocumentPosition(n))))}(k.ownerDocument.documentElement,k)){null!==s&&pn(k)&&(w=s.start,void 0===(_=s.end)&&(_=w),"selectionStart"in k?(k.selectionStart=w,k.selectionEnd=Math.min(_,k.value.length)):(_=(w=k.ownerDocument||document)&&w.defaultView||window).getSelection&&(_=_.getSelection(),c=k.textContent.length,u=Math.min(s.start,c),s=void 0===s.end?u:Math.min(s.end,c),!_.extend&&u>s&&(c=s,s=u,u=c),c=dn(k,u),f=dn(k,s),c&&f&&(1!==_.rangeCount||_.anchorNode!==c.node||_.anchorOffset!==c.offset||_.focusNode!==f.node||_.focusOffset!==f.offset)&&((w=w.createRange()).setStart(c.node,c.offset),_.removeAllRanges(),u>s?(_.addRange(w),_.extend(f.node,f.offset)):(w.setEnd(f.node,f.offset),_.addRange(w))))),w=[];for(_=k;_=_.parentNode;)1===_.nodeType&&w.push({element:_,left:_.scrollLeft,top:_.scrollTop});for("function"===typeof k.focus&&k.focus(),k=0;k<w.length;k++)(_=w[k]).element.scrollLeft=_.left,_.element.scrollTop=_.top}$t=!!yn,vn=yn=null,e.current=n,Mu=i;do{try{for(k=e;null!==Mu;){var x=Mu.effectTag;if(36&x&&au(k,Mu.alternate,Mu),128&x){w=void 0;var S=Mu.ref;if(null!==S){var T=Mu.stateNode;switch(Mu.tag){case 5:w=T;break;default:w=T}"function"===typeof S?S(w):S.current=w}}Mu=Mu.nextEffect}}catch(I){if(null===Mu)throw Error(o(330));ms(Mu,I),Mu=Mu.nextEffect}}while(null!==Mu);Mu=null,Li(),xu=a}else e.current=n;if(Ru)Ru=!1,zu=e,ju=t;else for(Mu=i;null!==Mu;)t=Mu.nextEffect,Mu.nextEffect=null,Mu=t;if(0===(t=e.firstPendingTime)&&(Uu=null),1073741823===t?e===Hu?Wu++:(Wu=0,Hu=e):Wu=0,"function"===typeof ws&&ws(n.stateNode,r),Xu(e),Nu)throw Nu=!1,e=Pu,Pu=null,e;return 0!==(8&xu)||Yi(),null}function ps(){for(;null!==Mu;){var e=Mu.effectTag;0!==(256&e)&&nu(Mu.alternate,Mu),0===(512&e)||Ru||(Ru=!0,Wi(97,(function(){return ys(),null}))),Mu=Mu.nextEffect}}function ys(){if(90!==ju){var e=97<ju?97:ju;return ju=90,Vi(e,vs)}}function vs(){if(null===zu)return!1;var e=zu;if(zu=null,0!==(48&xu))throw Error(o(331));var t=xu;for(xu|=32,e=e.current.firstEffect;null!==e;){try{var n=e;if(0!==(512&n.effectTag))switch(n.tag){case 0:case 11:case 15:case 22:ru(5,n),iu(5,n)}}catch(r){if(null===e)throw Error(o(330));ms(e,r)}n=e.nextEffect,e.nextEffect=null,e=n}return xu=t,Yi(),!0}function bs(e,t,n){sa(e,t=pu(e,t=Zo(n,t),1073741823)),null!==(e=Gu(e,1073741823))&&Xu(e)}function ms(e,t){if(3===e.tag)bs(e,e,t);else for(var n=e.return;null!==n;){if(3===n.tag){bs(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"===typeof n.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Uu||!Uu.has(r))){sa(n,e=yu(n,e=Zo(t,e),1073741823)),null!==(n=Gu(n,1073741823))&&Xu(n);break}}n=n.return}}function gs(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),Su===e&&Iu===n?Eu===_u||Eu===wu&&1073741823===Fu&&Ri()-Lu<500?ns(e,Iu):Cu=!0:Ds(e,n)&&(0!==(t=e.lastPingedTime)&&t<n||(e.lastPingedTime=n,Xu(e)))}function ks(e,t){var n=e.stateNode;null!==n&&n.delete(t),0===(t=0)&&(t=Ku(t=$u(),e,null)),null!==(e=Gu(e,t))&&Xu(e)}vu=function(e,t,n){var r=t.expirationTime;if(null!==e){var i=t.pendingProps;if(e.memoizedProps!==i||di.current)Oo=!0;else{if(r<n){switch(Oo=!1,t.tag){case 3:Ro(t),Ao();break;case 5:if(Ca(t),4&t.mode&&1!==n&&i.hidden)return t.expirationTime=t.childExpirationTime=1,null;break;case 1:yi(t.type)&&gi(t);break;case 4:Ba(t,t.stateNode.containerInfo);break;case 10:r=t.memoizedProps.value,i=t.type._context,li(Gi,i._currentValue),i._currentValue=r;break;case 13:if(null!==t.memoizedState)return 0!==(r=t.child.childExpirationTime)&&r>=n?Ho(e,t,n):(li(Ma,1&Ma.current),null!==(t=Qo(e,t,n))?t.sibling:null);li(Ma,1&Ma.current);break;case 19:if(r=t.childExpirationTime>=n,0!==(64&e.effectTag)){if(r)return Ko(e,t,n);t.effectTag|=64}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null),li(Ma,Ma.current),!r)return null}return Qo(e,t,n)}Oo=!1}}else Oo=!1;switch(t.expirationTime=0,t.tag){case 2:if(r=t.type,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,i=pi(t,fi.current),na(t,n),i=Ka(null,t,r,e,i,n),t.effectTag|=1,"object"===typeof i&&null!==i&&"function"===typeof i.render&&void 0===i.$$typeof){if(t.tag=1,t.memoizedState=null,t.updateQueue=null,yi(r)){var a=!0;gi(t)}else a=!1;t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,aa(t);var u=r.getDerivedStateFromProps;"function"===typeof u&&pa(t,r,u,e),i.updater=ya,t.stateNode=i,i._reactInternalFiber=t,ga(t,r,e,n),t=Uo(null,t,r,!0,a,n)}else t.tag=0,Bo(null,t,i,n),t=t.child;return t;case 16:e:{if(i=t.elementType,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,function(e){if(-1===e._status){e._status=0;var t=e._ctor;t=t(),e._result=t,t.then((function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)}),(function(t){0===e._status&&(e._status=2,e._result=t)}))}}(i),1!==i._status)throw i._result;switch(i=i._result,t.type=i,a=t.tag=function(e){if("function"===typeof e)return Ts(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===se)return 11;if(e===fe)return 14}return 2}(i),e=Qi(i,e),a){case 0:t=No(null,t,i,e,n);break e;case 1:t=Po(null,t,i,e,n);break e;case 11:t=Do(null,t,i,e,n);break e;case 14:t=Co(null,t,i,Qi(i.type,e),r,n);break e}throw Error(o(306,i,""))}return t;case 0:return r=t.type,i=t.pendingProps,No(e,t,r,i=t.elementType===r?i:Qi(r,i),n);case 1:return r=t.type,i=t.pendingProps,Po(e,t,r,i=t.elementType===r?i:Qi(r,i),n);case 3:if(Ro(t),r=t.updateQueue,null===e||null===r)throw Error(o(282));if(r=t.pendingProps,i=null!==(i=t.memoizedState)?i.element:null,oa(e,t),ca(t,r,null,n),(r=t.memoizedState.element)===i)Ao(),t=Qo(e,t,n);else{if((i=t.stateNode.hydrate)&&(wo=wn(t.stateNode.containerInfo.firstChild),ko=t,i=_o=!0),i)for(n=Ta(t,null,r,n),t.child=n;n;)n.effectTag=-3&n.effectTag|1024,n=n.sibling;else Bo(e,t,r,n),Ao();t=t.child}return t;case 5:return Ca(t),null===e&&To(t),r=t.type,i=t.pendingProps,a=null!==e?e.memoizedProps:null,u=i.children,mn(r,i)?u=null:null!==a&&mn(r,a)&&(t.effectTag|=16),Mo(e,t),4&t.mode&&1!==n&&i.hidden?(t.expirationTime=t.childExpirationTime=1,t=null):(Bo(e,t,u,n),t=t.child),t;case 6:return null===e&&To(t),null;case 13:return Ho(e,t,n);case 4:return Ba(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Sa(t,null,r,n):Bo(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,Do(e,t,r,i=t.elementType===r?i:Qi(r,i),n);case 7:return Bo(e,t,t.pendingProps,n),t.child;case 8:case 12:return Bo(e,t,t.pendingProps.children,n),t.child;case 10:e:{r=t.type._context,i=t.pendingProps,u=t.memoizedProps,a=i.value;var s=t.type._context;if(li(Gi,s._currentValue),s._currentValue=a,null!==u)if(s=u.value,0===(a=Pr(s,a)?0:0|("function"===typeof r._calculateChangedBits?r._calculateChangedBits(s,a):1073741823))){if(u.children===i.children&&!di.current){t=Qo(e,t,n);break e}}else for(null!==(s=t.child)&&(s.return=t);null!==s;){var l=s.dependencies;if(null!==l){u=s.child;for(var c=l.firstContext;null!==c;){if(c.context===r&&0!==(c.observedBits&a)){1===s.tag&&((c=ua(n,null)).tag=2,sa(s,c)),s.expirationTime<n&&(s.expirationTime=n),null!==(c=s.alternate)&&c.expirationTime<n&&(c.expirationTime=n),ta(s.return,n),l.expirationTime<n&&(l.expirationTime=n);break}c=c.next}}else u=10===s.tag&&s.type===t.type?null:s.child;if(null!==u)u.return=s;else for(u=s;null!==u;){if(u===t){u=null;break}if(null!==(s=u.sibling)){s.return=u.return,u=s;break}u=u.return}s=u}Bo(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=(a=t.pendingProps).children,na(t,n),r=r(i=ra(i,a.unstable_observedBits)),t.effectTag|=1,Bo(e,t,r,n),t.child;case 14:return a=Qi(i=t.type,t.pendingProps),Co(e,t,i,a=Qi(i.type,a),r,n);case 15:return Lo(e,t,t.type,t.pendingProps,r,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Qi(r,i),null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),t.tag=1,yi(r)?(e=!0,gi(t)):e=!1,na(t,n),ba(t,r,i),ga(t,r,i,n),Uo(null,t,r,!0,e,n);case 19:return Ko(e,t,n)}throw Error(o(156,t.tag))};var ws=null,_s=null;function xs(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.effectTag=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childExpirationTime=this.expirationTime=0,this.alternate=null}function Ss(e,t,n,r){return new xs(e,t,n,r)}function Ts(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Is(e,t){var n=e.alternate;return null===n?((n=Ss(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.effectTag=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childExpirationTime=e.childExpirationTime,n.expirationTime=e.expirationTime,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{expirationTime:t.expirationTime,firstContext:t.firstContext,responders:t.responders},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Es(e,t,n,r,i,a){var u=2;if(r=e,"function"===typeof e)Ts(e)&&(u=1);else if("string"===typeof e)u=5;else e:switch(e){case ne:return As(n.children,i,a,t);case ue:u=8,i|=7;break;case re:u=8,i|=1;break;case ie:return(e=Ss(12,n,t,8|i)).elementType=ie,e.type=ie,e.expirationTime=a,e;case le:return(e=Ss(13,n,t,i)).type=le,e.elementType=le,e.expirationTime=a,e;case ce:return(e=Ss(19,n,t,i)).elementType=ce,e.expirationTime=a,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case ae:u=10;break e;case oe:u=9;break e;case se:u=11;break e;case fe:u=14;break e;case de:u=16,r=null;break e;case he:u=22;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Ss(u,n,t,i)).elementType=e,t.type=r,t.expirationTime=a,t}function As(e,t,n,r){return(e=Ss(7,e,r,t)).expirationTime=n,e}function Fs(e,t,n){return(e=Ss(6,e,null,t)).expirationTime=n,e}function Os(e,t,n){return(t=Ss(4,null!==e.children?e.children:[],e.key,t)).expirationTime=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Bs(e,t,n){this.tag=t,this.current=null,this.containerInfo=e,this.pingCache=this.pendingChildren=null,this.finishedExpirationTime=0,this.finishedWork=null,this.timeoutHandle=-1,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=90,this.lastExpiredTime=this.lastPingedTime=this.nextKnownPendingLevel=this.lastSuspendedTime=this.firstSuspendedTime=this.firstPendingTime=0}function Ds(e,t){var n=e.firstSuspendedTime;return e=e.lastSuspendedTime,0!==n&&n>=t&&e<=t}function Cs(e,t){var n=e.firstSuspendedTime,r=e.lastSuspendedTime;n<t&&(e.firstSuspendedTime=t),(r>t||0===n)&&(e.lastSuspendedTime=t),t<=e.lastPingedTime&&(e.lastPingedTime=0),t<=e.lastExpiredTime&&(e.lastExpiredTime=0)}function Ls(e,t){t>e.firstPendingTime&&(e.firstPendingTime=t);var n=e.firstSuspendedTime;0!==n&&(t>=n?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:t>=e.lastSuspendedTime&&(e.lastSuspendedTime=t+1),t>e.nextKnownPendingLevel&&(e.nextKnownPendingLevel=t))}function Ms(e,t){var n=e.lastExpiredTime;(0===n||n>t)&&(e.lastExpiredTime=t)}function Ns(e,t,n,r){var i=t.current,a=$u(),u=da.suspense;a=Ku(a,i,u);e:if(n){t:{if(Je(n=n._reactInternalFiber)!==n||1!==n.tag)throw Error(o(170));var s=n;do{switch(s.tag){case 3:s=s.stateNode.context;break t;case 1:if(yi(s.type)){s=s.stateNode.__reactInternalMemoizedMergedChildContext;break t}}s=s.return}while(null!==s);throw Error(o(171))}if(1===n.tag){var l=n.type;if(yi(l)){n=mi(n,l,s);break e}}n=s}else n=ci;return null===t.context?t.context=n:t.pendingContext=n,(t=ua(a,u)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),sa(i,t),Qu(i,a),a}function Ps(e){if(!(e=e.current).child)return null;switch(e.child.tag){case 5:default:return e.child.stateNode}}function Us(e,t){null!==(e=e.memoizedState)&&null!==e.dehydrated&&e.retryTime<t&&(e.retryTime=t)}function Rs(e,t){Us(e,t),(e=e.alternate)&&Us(e,t)}function zs(e,t,n){var r=new Bs(e,t,n=null!=n&&!0===n.hydrate),i=Ss(3,null,null,2===t?7:1===t?3:0);r.current=i,i.stateNode=r,aa(i),e[In]=r.current,n&&0!==t&&function(e,t){var n=Ze(t);It.forEach((function(e){pt(e,t,n)})),Et.forEach((function(e){pt(e,t,n)}))}(0,9===e.nodeType?e:e.ownerDocument),this._internalRoot=r}function js(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Vs(e,t,n,r,i){var a=n._reactRootContainer;if(a){var o=a._internalRoot;if("function"===typeof i){var u=i;i=function(){var e=Ps(o);u.call(e)}}Ns(t,o,e,i)}else{if(a=n._reactRootContainer=function(e,t){if(t||(t=!(!(t=e?9===e.nodeType?e.documentElement:e.firstChild:null)||1!==t.nodeType||!t.hasAttribute("data-reactroot"))),!t)for(var n;n=e.lastChild;)e.removeChild(n);return new zs(e,0,t?{hydrate:!0}:void 0)}(n,r),o=a._internalRoot,"function"===typeof i){var s=i;i=function(){var e=Ps(o);s.call(e)}}ts((function(){Ns(t,o,e,i)}))}return Ps(o)}function Ws(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:te,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}function Hs(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!js(t))throw Error(o(200));return Ws(e,t,null,n)}zs.prototype.render=function(e){Ns(e,this._internalRoot,null,null)},zs.prototype.unmount=function(){var e=this._internalRoot,t=e.containerInfo;Ns(null,e,null,(function(){t[In]=null}))},yt=function(e){if(13===e.tag){var t=Ki($u(),150,100);Qu(e,t),Rs(e,t)}},vt=function(e){13===e.tag&&(Qu(e,3),Rs(e,3))},bt=function(e){if(13===e.tag){var t=$u();Qu(e,t=Ku(t,e,null)),Rs(e,t)}},A=function(e,t,n){switch(t){case"input":if(Te(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=On(r);if(!i)throw Error(o(90));we(r),Te(r,i)}}}break;case"textarea":De(e,n);break;case"select":null!=(t=n.value)&&Fe(e,!!n.multiple,t,!1)}},L=es,M=function(e,t,n,r,i){var a=xu;xu|=4;try{return Vi(98,e.bind(null,t,n,r,i))}finally{0===(xu=a)&&Yi()}},N=function(){0===(49&xu)&&(function(){if(null!==Vu){var e=Vu;Vu=null,e.forEach((function(e,t){Ms(t,e),Xu(t)})),Yi()}}(),ys())},P=function(e,t){var n=xu;xu|=2;try{return e(t)}finally{0===(xu=n)&&Yi()}};var Ys={Events:[An,Fn,On,I,x,Pn,function(e){it(e,Nn)},D,C,Xt,ut,ys,{current:!1}]};!function(e){var t=e.findFiberByHostInstance;(function(e){if("undefined"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled||!t.supportsFiber)return!0;try{var n=t.inject(e);ws=function(e){try{t.onCommitFiberRoot(n,e,void 0,64===(64&e.current.effectTag))}catch(r){}},_s=function(e){try{t.onCommitFiberUnmount(n,e)}catch(r){}}}catch(r){}})(i({},e,{overrideHookState:null,overrideProps:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:q.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=nt(e))?null:e.stateNode},findFiberByHostInstance:function(e){return t?t(e):null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null}))}({findFiberByHostInstance:En,bundleType:0,version:"16.14.0",rendererPackageName:"react-dom"}),t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ys,t.createPortal=Hs,t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternalFiber;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw Error(o(268,Object.keys(e)))}return e=null===(e=nt(t))?null:e.stateNode},t.flushSync=function(e,t){if(0!==(48&xu))throw Error(o(187));var n=xu;xu|=1;try{return Vi(99,e.bind(null,t))}finally{xu=n,Yi()}},t.hydrate=function(e,t,n){if(!js(t))throw Error(o(200));return Vs(null,e,t,!0,n)},t.render=function(e,t,n){if(!js(t))throw Error(o(200));return Vs(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!js(e))throw Error(o(40));return!!e._reactRootContainer&&(ts((function(){Vs(null,null,e,!1,(function(){e._reactRootContainer=null,e[In]=null}))})),!0)},t.unstable_batchedUpdates=es,t.unstable_createPortal=function(e,t){return Hs(e,t,2<arguments.length&&void 0!==arguments[2]?arguments[2]:null)},t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!js(n))throw Error(o(200));if(null==e||void 0===e._reactInternalFiber)throw Error(o(38));return Vs(e,t,n,!1,r)},t.version="16.14.0"},function(e,t,n){"use strict";e.exports=n(19)},function(e,t,n){"use strict";var r,i,a,o,u;if("undefined"===typeof window||"function"!==typeof MessageChannel){var s=null,l=null,c=function e(){if(null!==s)try{var n=t.unstable_now();s(!0,n),s=null}catch(r){throw setTimeout(e,0),r}},f=Date.now();t.unstable_now=function(){return Date.now()-f},r=function(e){null!==s?setTimeout(r,0,e):(s=e,setTimeout(c,0))},i=function(e,t){l=setTimeout(e,t)},a=function(){clearTimeout(l)},o=function(){return!1},u=t.unstable_forceFrameRate=function(){}}else{var d=window.performance,h=window.Date,p=window.setTimeout,y=window.clearTimeout;if("undefined"!==typeof console){var v=window.cancelAnimationFrame;"function"!==typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),"function"!==typeof v&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills")}if("object"===typeof d&&"function"===typeof d.now)t.unstable_now=function(){return d.now()};else{var b=h.now();t.unstable_now=function(){return h.now()-b}}var m=!1,g=null,k=-1,w=5,_=0;o=function(){return t.unstable_now()>=_},u=function(){},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing framerates higher than 125 fps is not unsupported"):w=0<e?Math.floor(1e3/e):5};var x=new MessageChannel,S=x.port2;x.port1.onmessage=function(){if(null!==g){var e=t.unstable_now();_=e+w;try{g(!0,e)?S.postMessage(null):(m=!1,g=null)}catch(n){throw S.postMessage(null),n}}else m=!1},r=function(e){g=e,m||(m=!0,S.postMessage(null))},i=function(e,n){k=p((function(){e(t.unstable_now())}),n)},a=function(){y(k),k=-1}}function T(e,t){var n=e.length;e.push(t);e:for(;;){var r=n-1>>>1,i=e[r];if(!(void 0!==i&&0<A(i,t)))break e;e[r]=t,e[n]=i,n=r}}function I(e){return void 0===(e=e[0])?null:e}function E(e){var t=e[0];if(void 0!==t){var n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length;r<i;){var a=2*(r+1)-1,o=e[a],u=a+1,s=e[u];if(void 0!==o&&0>A(o,n))void 0!==s&&0>A(s,o)?(e[r]=s,e[u]=n,r=u):(e[r]=o,e[a]=n,r=a);else{if(!(void 0!==s&&0>A(s,n)))break e;e[r]=s,e[u]=n,r=u}}}return t}return null}function A(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}var F=[],O=[],B=1,D=null,C=3,L=!1,M=!1,N=!1;function P(e){for(var t=I(O);null!==t;){if(null===t.callback)E(O);else{if(!(t.startTime<=e))break;E(O),t.sortIndex=t.expirationTime,T(F,t)}t=I(O)}}function U(e){if(N=!1,P(e),!M)if(null!==I(F))M=!0,r(R);else{var t=I(O);null!==t&&i(U,t.startTime-e)}}function R(e,n){M=!1,N&&(N=!1,a()),L=!0;var r=C;try{for(P(n),D=I(F);null!==D&&(!(D.expirationTime>n)||e&&!o());){var u=D.callback;if(null!==u){D.callback=null,C=D.priorityLevel;var s=u(D.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?D.callback=s:D===I(F)&&E(F),P(n)}else E(F);D=I(F)}if(null!==D)var l=!0;else{var c=I(O);null!==c&&i(U,c.startTime-n),l=!1}return l}finally{D=null,C=r,L=!1}}function z(e){switch(e){case 1:return-1;case 2:return 250;case 5:return 1073741823;case 4:return 1e4;default:return 5e3}}var j=u;t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){M||L||(M=!0,r(R))},t.unstable_getCurrentPriorityLevel=function(){return C},t.unstable_getFirstCallbackNode=function(){return I(F)},t.unstable_next=function(e){switch(C){case 1:case 2:case 3:var t=3;break;default:t=C}var n=C;C=t;try{return e()}finally{C=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=j,t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=C;C=e;try{return t()}finally{C=n}},t.unstable_scheduleCallback=function(e,n,o){var u=t.unstable_now();if("object"===typeof o&&null!==o){var s=o.delay;s="number"===typeof s&&0<s?u+s:u,o="number"===typeof o.timeout?o.timeout:z(e)}else o=z(e),s=u;return e={id:B++,callback:n,priorityLevel:e,startTime:s,expirationTime:o=s+o,sortIndex:-1},s>u?(e.sortIndex=s,T(O,e),null===I(F)&&e===I(O)&&(N?a():N=!0,i(U,s-u))):(e.sortIndex=o,T(F,e),M||L||(M=!0,r(R))),e},t.unstable_shouldYield=function(){var e=t.unstable_now();P(e);var n=I(F);return n!==D&&null!==D&&null!==n&&null!==n.callback&&n.startTime<=e&&n.expirationTime<D.expirationTime||o()},t.unstable_wrapCallback=function(e){var t=C;return function(){var n=C;C=t;try{return e.apply(this,arguments)}finally{C=n}}}},function(e,t,n){"use strict";e.exports=n(21)},function(e,t,n){"use strict";var r="function"===typeof Symbol&&Symbol.for,i=r?Symbol.for("react.element"):60103,a=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,u=r?Symbol.for("react.strict_mode"):60108,s=r?Symbol.for("react.profiler"):60114,l=r?Symbol.for("react.provider"):60109,c=r?Symbol.for("react.context"):60110,f=r?Symbol.for("react.async_mode"):60111,d=r?Symbol.for("react.concurrent_mode"):60111,h=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,y=r?Symbol.for("react.suspense_list"):60120,v=r?Symbol.for("react.memo"):60115,b=r?Symbol.for("react.lazy"):60116,m=r?Symbol.for("react.block"):60121,g=r?Symbol.for("react.fundamental"):60117,k=r?Symbol.for("react.responder"):60118,w=r?Symbol.for("react.scope"):60119;function _(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case i:switch(e=e.type){case f:case d:case o:case s:case u:case p:return e;default:switch(e=e&&e.$$typeof){case c:case h:case b:case v:case l:return e;default:return t}}case a:return t}}}function x(e){return _(e)===d}t.AsyncMode=f,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=l,t.Element=i,t.ForwardRef=h,t.Fragment=o,t.Lazy=b,t.Memo=v,t.Portal=a,t.Profiler=s,t.StrictMode=u,t.Suspense=p,t.isAsyncMode=function(e){return x(e)||_(e)===f},t.isConcurrentMode=x,t.isContextConsumer=function(e){return _(e)===c},t.isContextProvider=function(e){return _(e)===l},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===i},t.isForwardRef=function(e){return _(e)===h},t.isFragment=function(e){return _(e)===o},t.isLazy=function(e){return _(e)===b},t.isMemo=function(e){return _(e)===v},t.isPortal=function(e){return _(e)===a},t.isProfiler=function(e){return _(e)===s},t.isStrictMode=function(e){return _(e)===u},t.isSuspense=function(e){return _(e)===p},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===o||e===d||e===s||e===u||e===p||e===y||"object"===typeof e&&null!==e&&(e.$$typeof===b||e.$$typeof===v||e.$$typeof===l||e.$$typeof===c||e.$$typeof===h||e.$$typeof===g||e.$$typeof===k||e.$$typeof===w||e.$$typeof===m)},t.typeOf=_},function(e,t,n){var r=function(e){"use strict";var t=Object.prototype,n=t.hasOwnProperty,r="function"===typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",o=r.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(I){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),o=new x(r||[]);return a._invoke=function(e,t,n){var r="suspendedStart";return function(i,a){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw a;return T()}for(n.method=i,n.arg=a;;){var o=n.delegate;if(o){var u=k(o,n);if(u){if(u===c)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=l(e,t,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===c)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}(e,n,o),a}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(I){return{type:"throw",arg:I}}}e.wrap=s;var c={};function f(){}function d(){}function h(){}var p={};p[i]=function(){return this};var y=Object.getPrototypeOf,v=y&&y(y(S([])));v&&v!==t&&n.call(v,i)&&(p=v);var b=h.prototype=f.prototype=Object.create(p);function m(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function g(e,t){var r;this._invoke=function(i,a){function o(){return new t((function(r,o){!function r(i,a,o,u){var s=l(e[i],e,a);if("throw"!==s.type){var c=s.arg,f=c.value;return f&&"object"===typeof f&&n.call(f,"__await")?t.resolve(f.__await).then((function(e){r("next",e,o,u)}),(function(e){r("throw",e,o,u)})):t.resolve(f).then((function(e){c.value=e,o(c)}),(function(e){return r("throw",e,o,u)}))}u(s.arg)}(i,a,r,o)}))}return r=r?r.then(o,o):o()}}function k(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method))return c;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return c}var r=l(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,c;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,c):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,c)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function S(e){if(e){var t=e[i];if(t)return t.call(e);if("function"===typeof e.next)return e;if(!isNaN(e.length)){var r=-1,a=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return a.next=a}}return{next:T}}function T(){return{value:void 0,done:!0}}return d.prototype=b.constructor=h,h.constructor=d,d.displayName=u(h,o,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"===typeof e&&e.constructor;return!!t&&(t===d||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,u(e,o,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},m(g.prototype),g.prototype[a]=function(){return this},e.AsyncIterator=g,e.async=function(t,n,r,i,a){void 0===a&&(a=Promise);var o=new g(s(t,n,r,i),a);return e.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},m(b),u(b,o,"Generator"),b[i]=function(){return this},b.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=S,x.prototype={constructor:x,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return o.type="throw",o.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],o=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(u&&s){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,c):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),c},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),_(n),c}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;_(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:S(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),c}},e}(e.exports);try{regeneratorRuntime=r}catch(i){Function("r","regeneratorRuntime = r")(r)}},,function(e,t,n){"use strict";n.r(t);var r=/[A-Z]/g,i=/^ms-/,a={};function o(e){return"-"+e.toLowerCase()}t.default=function(e){if(a.hasOwnProperty(e))return a[e];var t=e.replace(r,o);return a[e]=i.test(t)?"-"+t:t}},,function(e,t,n){"use strict";n.d(t,"a",(function(){return T}));var r={white:"#FFFFFF",gray50:"#F6F6F6",gray100:"#EEEEEE",gray200:"#E2E2E2",gray300:"#CBCBCB",gray400:"#AFAFAF",gray500:"#757575",gray600:"#545454",gray700:"#333333",gray800:"#1F1F1F",gray900:"#141414",black:"#000000",platinum50:"#F4FAFB",platinum100:"#EBF5F7",platinum200:"#CCDFE5",platinum300:"#A1BDCA",platinum400:"#8EA3AD",platinum500:"#6C7C83",platinum600:"#556268",platinum700:"#394145",platinum800:"#142328",red50:"#FFEFED",red100:"#FED7D2",red200:"#F1998E",red300:"#E85C4A",red400:"#E11900",red500:"#AB1300",red600:"#870F00",red700:"#5A0A00",orange50:"#FFF3EF",orange100:"#FFE1D6",orange200:"#FABDA5",orange300:"#FA9269",orange400:"#FF6937",orange500:"#C14F29",orange600:"#9A3F21",orange700:"#672A16",yellow50:"#FFFAF0",yellow100:"#FFF2D9",yellow200:"#FFE3AC",yellow300:"#FFCF70",yellow400:"#FFC043",yellow500:"#BC8B2C",yellow600:"#997328",yellow700:"#674D1B",green50:"#E6F2ED",green100:"#ADDEC9",green200:"#66D19E",green300:"#06C167",green400:"#05944F",green500:"#03703C",green600:"#03582F",green700:"#10462D",blue50:"#EFF3FE",blue100:"#D4E2FC",blue200:"#A0BFF8",blue300:"#5B91F5",blue400:"#276EF1",blue500:"#1E54B7",blue600:"#174291",blue700:"#102C60",cobalt50:"#EBEDFA",cobalt100:"#D2D7F0",cobalt200:"#949CE3",cobalt300:"#535FCF",cobalt400:"#0E1FC1",cobalt500:"#0A1899",cobalt600:"#081270",cobalt700:"#050C4D",purple50:"#F3F1F9",purple100:"#E3DDF2",purple200:"#C1B4E2",purple300:"#957FCE",purple400:"#7356BF",purple500:"#574191",purple600:"#453473",purple700:"#2E224C",brown50:"#F6F0EA",brown100:"#EBE0DB",brown200:"#D2BBB0",brown300:"#B18977",brown400:"#99644C",brown500:"#744C3A",brown600:"#5C3C2E",brown700:"#3D281E"},i={primaryA:r.black,primaryB:r.white,primary:r.black,primary50:r.gray50,primary100:r.gray100,primary200:r.gray200,primary300:r.gray300,primary400:r.gray400,primary500:r.gray500,primary600:r.gray600,primary700:r.gray700,accent:r.blue400,accent50:r.blue50,accent100:r.blue100,accent200:r.blue200,accent300:r.blue300,accent400:r.blue400,accent500:r.blue500,accent600:r.blue600,accent700:r.blue700,negative:r.red400,negative50:r.red50,negative100:r.red100,negative200:r.red200,negative300:r.red300,negative400:r.red400,negative500:r.red500,negative600:r.red600,negative700:r.red700,warning:r.yellow400,warning50:r.yellow50,warning100:r.yellow100,warning200:r.yellow200,warning300:r.yellow300,warning400:r.yellow400,warning500:r.yellow500,warning600:r.yellow600,warning700:r.yellow700,positive:r.green400,positive50:r.green50,positive100:r.green100,positive200:r.green200,positive300:r.green300,positive400:r.green400,positive500:r.green500,positive600:r.green600,positive700:r.green700,white:r.white,black:r.black,mono100:r.white,mono200:r.gray50,mono300:r.gray100,mono400:r.gray200,mono500:r.gray300,mono600:r.gray400,mono700:r.gray500,mono800:r.gray600,mono900:r.gray700,mono1000:r.black,rating200:r.yellow200,rating400:r.yellow400};function a(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"1",n=/^#?([a-f\d])([a-f\d])([a-f\d])$/i;e=e.replace(n,(function(e,t,n,r){return t+t+n+n+r+r}));var r=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return r?"rgba(".concat(parseInt(r[1],16),", ").concat(parseInt(r[2],16),", ").concat(parseInt(r[3],16),", ").concat(t,")"):null}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var l={border100:{borderColor:"hsla(0, 0%, 0%, 0.04)",borderStyle:"solid",borderWidth:"1px"},border200:{borderColor:"hsla(0, 0%, 0%, 0.08)",borderStyle:"solid",borderWidth:"1px"},border300:{borderColor:"hsla(0, 0%, 0%, 0.12)",borderStyle:"solid",borderWidth:"1px"},border400:{borderColor:"hsla(0, 0%, 0%, 0.16)",borderStyle:"solid",borderWidth:"1px"},border500:{borderColor:"hsla(0, 0%, 0%, 0.2)",borderStyle:"solid",borderWidth:"1px"},border600:{borderColor:"hsla(0, 0%, 0%, 0.24)",borderStyle:"solid",borderWidth:"1px"},radius100:"2px",radius200:"4px",radius300:"8px",radius400:"12px",useRoundedCorners:!0,buttonBorderRadius:"0px",inputBorderRadius:"0px",popoverBorderRadius:"0px",surfaceBorderRadius:"0px",tagBorderRadius:"24px"},c={shadow400:"0 1px 4px hsla(0, 0%, 0%, 0.16)",shadow500:"0 2px 8px hsla(0, 0%, 0%, 0.16)",shadow600:"0 4px 16px hsla(0, 0%, 0%, 0.16)",shadow700:"0 8px 24px hsla(0, 0%, 0%, 0.16)",overlay0:"inset 0 0 0 1000px hsla(0, 0%, 0%, 0)",overlay100:"inset 0 0 0 1000px hsla(0, 0%, 0%, 0.04)",overlay200:"inset 0 0 0 1000px hsla(0, 0%, 0%, 0.08)",overlay300:"inset 0 0 0 1000px hsla(0, 0%, 0%, 0.12)",overlay400:"inset 0 0 0 1000px hsla(0, 0%, 0%, 0.16)",overlay500:"inset 0 0 0 1000px hsla(0, 0%, 0%, 0.2)",overlay600:"inset 0 0 0 1000px hsla(0, 0%, 0%, 0.24)"};function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var p={primaryFontFamily:'system-ui, "Helvetica Neue", Helvetica, Arial, sans-serif'},y='"Lucida Console", Monaco, monospace',v={timing100:"100ms",timing200:"200ms",timing300:"300ms",timing400:"400ms",timing500:"500ms",timing600:"600ms",timing700:"700ms",timing800:"800ms",timing900:"900ms",timing1000:"1000ms",easeInCurve:"cubic-bezier(.8, .2, .6, 1)",easeOutCurve:"cubic-bezier(.2, .8, .4, 1)",easeInOutCurve:"cubic-bezier(0.4, 0, 0.2, 1)",easeInQuinticCurve:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",easeOutQuinticCurve:"cubic-bezier(0.23, 1, 0.32, 1)",easeInOutQuinticCurve:"cubic-bezier(0.86, 0, 0.07, 1)",linearCurve:"cubic-bezier(0, 0, 1, 1)"},b={small:320,medium:600,large:1136},m={columns:[4,8,12],gutters:[16,36,36],margins:[16,36,64],gaps:0,unit:"px",maxWidth:1280},g=function(e){return"@media screen and (min-width: ".concat(e,"px)")},k={small:g(b.small),medium:g(b.medium),large:g(b.large)},w={scale0:"2px",scale100:"4px",scale200:"6px",scale300:"8px",scale400:"10px",scale500:"12px",scale550:"14px",scale600:"16px",scale650:"18px",scale700:"20px",scale750:"22px",scale800:"24px",scale850:"28px",scale900:"32px",scale950:"36px",scale1000:"40px",scale1200:"48px",scale1400:"56px",scale1600:"64px",scale2400:"96px",scale3200:"128px",scale4800:"192px"};function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){S(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var T={name:"light-theme",colors:x(x(x(x({},i),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i;return{buttonPrimaryFill:e.primary,buttonPrimaryText:e.white,buttonPrimaryHover:e.primary700,buttonPrimaryActive:e.primary600,buttonPrimarySelectedFill:e.primary600,buttonPrimarySelectedText:e.white,buttonPrimarySpinnerForeground:e.primary50,buttonPrimarySpinnerBackground:e.primary500,buttonSecondaryFill:e.primary100,buttonSecondaryText:e.primary,buttonSecondaryHover:e.primary200,buttonSecondaryActive:e.primary300,buttonSecondarySelectedFill:e.primary300,buttonSecondarySelectedText:e.primary,buttonSecondarySpinnerForeground:e.primary700,buttonSecondarySpinnerBackground:e.primary300,buttonTertiaryFill:"transparent",buttonTertiaryText:e.primary,buttonTertiaryHover:e.primary50,buttonTertiaryActive:e.primary100,buttonTertiarySelectedFill:e.primary100,buttonTertiarySelectedText:e.primary,buttonTertiarySpinnerForeground:e.primary700,buttonTertiarySpinnerBackground:e.primary300,buttonMinimalFill:"transparent",buttonMinimalText:e.primary,buttonMinimalHover:e.primary50,buttonMinimalActive:e.primary100,buttonMinimalSelectedFill:e.primary100,buttonMinimalSelectedText:e.primary,buttonMinimalSpinnerForeground:e.primary700,buttonMinimalSpinnerBackground:e.primary300,buttonDisabledFill:e.mono200,buttonDisabledText:e.mono600,buttonDisabledSpinnerForeground:e.mono600,buttonDisabledSpinnerBackground:e.mono400,breadcrumbsText:e.mono900,breadcrumbsSeparatorFill:e.mono700,datepickerBackground:e.mono100,datepickerDayFont:e.mono1000,datepickerDayFontDisabled:e.mono500,datepickerDayPseudoSelected:e.primary100,datepickerDayPseudoHighlighted:e.primary200,calendarBackground:e.mono100,calendarForeground:e.mono1000,calendarForegroundDisabled:e.mono500,calendarHeaderBackground:e.primary,calendarHeaderForeground:e.white,calendarHeaderBackgroundActive:e.primary700,calendarHeaderForegroundDisabled:e.primary500,calendarDayBackgroundPseudoSelected:e.primary100,calendarDayForegroundPseudoSelected:e.mono1000,calendarDayBackgroundPseudoSelectedHighlighted:e.primary200,calendarDayForegroundPseudoSelectedHighlighted:e.mono1000,calendarDayBackgroundSelected:e.white,calendarDayForegroundSelected:e.primary,calendarDayBackgroundSelectedHighlighted:e.primary,calendarDayForegroundSelectedHighlighted:e.white,comboboxListItemFocus:e.mono200,comboboxListItemHover:e.mono300,fileUploaderBackgroundColor:e.mono200,fileUploaderBackgroundColorActive:e.primary50,fileUploaderBorderColorActive:e.primary,fileUploaderBorderColorDefault:e.mono500,fileUploaderMessageColor:e.mono800,linkText:e.primary,linkVisited:e.primary700,linkHover:e.primary600,linkActive:e.primary500,listHeaderFill:e.white,listBodyFill:e.mono200,listIconFill:e.mono500,listBorder:e.mono500,progressStepsCompletedText:e.white,progressStepsCompletedFill:e.primary,progressStepsActiveText:e.white,progressStepsActiveFill:e.primary,progressStepsIconActiveFill:e.primary,toggleFill:e.white,toggleFillChecked:e.primary,toggleFillDisabled:e.mono600,toggleTrackFill:e.mono400,toggleTrackFillDisabled:e.mono300,tickFill:e.mono100,tickFillHover:e.mono200,tickFillActive:e.mono300,tickFillSelected:e.primary,tickFillSelectedHover:e.primary700,tickFillSelectedHoverActive:e.primary600,tickFillError:e.negative50,tickFillErrorHover:e.negative100,tickFillErrorHoverActive:e.negative200,tickFillErrorSelected:e.negative400,tickFillErrorSelectedHover:e.negative500,tickFillErrorSelectedHoverActive:e.negative600,tickFillDisabled:e.mono600,tickBorder:e.mono700,tickBorderError:e.negative400,tickMarkFill:e.white,tickMarkFillError:e.white,tickMarkFillDisabled:e.mono100,sliderTrackFill:e.mono400,sliderTrackFillHover:e.mono500,sliderTrackFillActive:e.mono600,sliderTrackFillSelected:e.primary,sliderTrackFillSelectedHover:e.primary,sliderTrackFillSelectedActive:e.primary500,sliderTrackFillDisabled:e.mono300,sliderHandleFill:e.white,sliderHandleFillHover:e.white,sliderHandleFillActive:e.white,sliderHandleFillSelected:e.white,sliderHandleFillSelectedHover:e.white,sliderHandleFillSelectedActive:e.white,sliderHandleFillDisabled:e.mono500,sliderHandleInnerFill:e.mono400,sliderHandleInnerFillDisabled:e.mono400,sliderHandleInnerFillSelectedHover:e.primary,sliderHandleInnerFillSelectedActive:e.primary500,sliderBorder:e.mono500,sliderBorderHover:e.primary,sliderBorderDisabled:e.mono600,inputBorder:e.mono300,inputFill:e.mono300,inputFillError:e.negative50,inputFillDisabled:e.mono200,inputFillActive:e.mono200,inputFillPositive:e.positive50,inputTextDisabled:e.mono600,inputBorderError:e.negative200,inputBorderPositive:e.positive200,inputEnhancerFill:e.mono300,inputEnhancerFillDisabled:e.mono300,inputEnhancerTextDisabled:e.mono600,inputPlaceholder:e.mono700,inputPlaceholderDisabled:e.mono600,menuFill:e.mono100,menuFillHover:e.mono200,menuFontDefault:e.mono800,menuFontDisabled:e.mono500,menuFontHighlighted:e.mono1000,menuFontSelected:e.mono1000,modalCloseColor:e.mono1000,modalCloseColorHover:e.mono800,modalCloseColorFocus:e.mono800,paginationTriangleDown:e.mono800,headerNavigationFill:"transparent",tabBarFill:e.mono200,tabColor:e.mono800,notificationPrimaryBackground:e.primary50,notificationPrimaryText:e.primary500,notificationInfoBackground:e.accent50,notificationInfoText:e.accent500,notificationPositiveBackground:e.positive50,notificationPositiveText:e.positive500,notificationWarningBackground:e.warning50,notificationWarningText:e.warning500,notificationNegativeBackground:e.negative50,notificationNegativeText:e.negative500,tagFontDisabledRampUnit:"100",tagOutlinedDisabledRampUnit:"200",tagSolidFontRampUnit:"0",tagSolidRampUnit:"400",tagOutlinedFontRampUnit:"400",tagOutlinedRampUnit:"200",tagSolidHoverRampUnit:"50",tagSolidActiveRampUnit:"100",tagSolidDisabledRampUnit:"50",tagSolidFontHoverRampUnit:"500",tagLightRampUnit:"50",tagLightHoverRampUnit:"100",tagLightActiveRampUnit:"100",tagLightDisabledRampUnit:"50",tagLightFontRampUnit:"500",tagLightFontHoverRampUnit:"500",tagOutlinedHoverRampUnit:"50",tagOutlinedActiveRampUnit:"0",tagOutlinedFontHoverRampUnit:"400",tagNeutralFontDisabled:e.mono600,tagNeutralOutlinedDisabled:e.mono400,tagNeutralSolidFont:e.white,tagNeutralSolidBackground:e.black,tagNeutralOutlinedBackground:e.mono600,tagNeutralOutlinedFont:e.black,tagNeutralSolidHover:e.mono300,tagNeutralSolidActive:e.mono400,tagNeutralSolidDisabled:e.mono200,tagNeutralSolidFontHover:e.mono900,tagNeutralLightBackground:e.mono300,tagNeutralLightHover:e.mono300,tagNeutralLightActive:e.mono400,tagNeutralLightDisabled:e.mono200,tagNeutralLightFont:e.mono900,tagNeutralLightFontHover:e.mono900,tagNeutralOutlinedActive:e.mono900,tagNeutralOutlinedFontHover:e.mono800,tagNeutralOutlinedHover:"rgba(0, 0, 0, 0.08)",tagPrimaryFontDisabled:e.primary400,tagPrimaryOutlinedDisabled:e.primary200,tagPrimarySolidFont:e.white,tagPrimarySolidBackground:e.primary,tagPrimaryOutlinedFontHover:e.primary,tagPrimaryOutlinedFont:e.primary,tagPrimarySolidHover:e.primary100,tagPrimarySolidActive:e.primary200,tagPrimarySolidDisabled:e.primary50,tagPrimarySolidFontHover:e.primary700,tagPrimaryLightBackground:e.primary50,tagPrimaryLightHover:e.primary100,tagPrimaryLightActive:e.primary100,tagPrimaryLightDisabled:e.primary50,tagPrimaryLightFont:e.primary500,tagPrimaryLightFontHover:e.primary500,tagPrimaryOutlinedActive:e.primary600,tagPrimaryOutlinedHover:"rgba(0, 0, 0, 0.08)",tagPrimaryOutlinedBackground:e.primary400,tagAccentFontDisabled:e.accent200,tagAccentOutlinedDisabled:e.accent200,tagAccentSolidFont:e.white,tagAccentSolidBackground:e.accent400,tagAccentOutlinedBackground:e.accent200,tagAccentOutlinedFont:e.accent400,tagAccentSolidHover:e.accent50,tagAccentSolidActive:e.accent100,tagAccentSolidDisabled:e.accent50,tagAccentSolidFontHover:e.accent500,tagAccentLightBackground:e.accent50,tagAccentLightHover:e.accent100,tagAccentLightActive:e.accent100,tagAccentLightDisabled:e.accent50,tagAccentLightFont:e.accent500,tagAccentLightFontHover:e.accent500,tagAccentOutlinedActive:e.accent600,tagAccentOutlinedFontHover:e.accent400,tagAccentOutlinedHover:"rgba(0, 0, 0, 0.08)",tagPositiveFontDisabled:e.positive200,tagPositiveOutlinedDisabled:e.positive200,tagPositiveSolidFont:e.white,tagPositiveSolidBackground:e.positive400,tagPositiveOutlinedBackground:e.positive200,tagPositiveOutlinedFont:e.positive400,tagPositiveSolidHover:e.positive50,tagPositiveSolidActive:e.positive100,tagPositiveSolidDisabled:e.positive50,tagPositiveSolidFontHover:e.positive500,tagPositiveLightBackground:e.positive50,tagPositiveLightHover:e.positive100,tagPositiveLightActive:e.positive100,tagPositiveLightDisabled:e.positive50,tagPositiveLightFont:e.positive500,tagPositiveLightFontHover:e.positive500,tagPositiveOutlinedActive:e.positive600,tagPositiveOutlinedFontHover:e.positive400,tagPositiveOutlinedHover:"rgba(0, 0, 0, 0.08)",tagWarningFontDisabled:e.warning300,tagWarningOutlinedDisabled:e.warning300,tagWarningSolidFont:e.warning700,tagWarningSolidBackground:e.warning400,tagWarningOutlinedBackground:e.warning300,tagWarningOutlinedFont:e.warning600,tagWarningSolidHover:e.warning50,tagWarningSolidActive:e.warning100,tagWarningSolidDisabled:e.warning50,tagWarningSolidFontHover:e.warning500,tagWarningLightBackground:e.warning50,tagWarningLightHover:e.warning100,tagWarningLightActive:e.warning100,tagWarningLightDisabled:e.warning50,tagWarningLightFont:e.warning500,tagWarningLightFontHover:e.warning500,tagWarningOutlinedActive:e.warning600,tagWarningOutlinedFontHover:e.warning600,tagWarningOutlinedHover:"rgba(0, 0, 0, 0.08)",tagNegativeFontDisabled:e.negative200,tagNegativeOutlinedDisabled:e.negative200,tagNegativeSolidFont:e.white,tagNegativeSolidBackground:e.negative400,tagNegativeOutlinedBackground:e.negative200,tagNegativeOutlinedFont:e.negative400,tagNegativeSolidHover:e.negative50,tagNegativeSolidActive:e.negative100,tagNegativeSolidDisabled:e.negative50,tagNegativeSolidFontHover:e.negative500,tagNegativeLightBackground:e.negative50,tagNegativeLightHover:e.negative100,tagNegativeLightActive:e.negative100,tagNegativeLightDisabled:e.negative50,tagNegativeLightFont:e.negative500,tagNegativeLightFontHover:e.negative500,tagNegativeOutlinedActive:e.negative600,tagNegativeOutlinedFontHover:e.negative400,tagNegativeOutlinedHover:"rgba(0, 0, 0, 0.08)",tableHeadBackgroundColor:e.mono100,tableBackground:e.mono100,tableStripedBackground:e.mono200,tableFilter:e.mono600,tableFilterHeading:e.mono700,tableFilterBackground:e.mono100,tableFilterFooterBackground:e.mono200,toastText:e.white,toastPrimaryBackground:e.primary500,toastInfoBackground:e.accent500,toastPositiveBackground:e.positive500,toastWarningBackground:e.warning500,toastNegativeBackground:e.negative500,spinnerTrackFill:e.mono900,progressbarTrackFill:e.mono900,tooltipBackground:e.mono900,tooltipText:e.mono100}}()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i;return{colorPrimary:e.black,colorSecondary:e.mono800,background:e.white,backgroundAlt:e.white,backgroundInv:e.primary,foreground:e.black,foregroundAlt:e.mono800,foregroundInv:e.white,border:e.mono500,borderAlt:e.mono600,borderFocus:e.primary,borderError:e.negative,shadowFocus:"rgba(39, 110, 241, 0.32)",shadowError:"rgba(229, 73, 55, 0.32)"}}()),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i,t={backgroundPrimary:e.primaryB,backgroundSecondary:r.gray50,backgroundTertiary:r.gray100,backgroundInversePrimary:e.primaryA,backgroundInverseSecondary:r.gray800,contentPrimary:e.primaryA,contentSecondary:r.gray600,contentTertiary:r.gray500,contentInversePrimary:e.primaryB,contentInverseSecondary:r.gray300,contentInverseTertiary:r.gray400,borderOpaque:r.gray200,borderTransparent:a(e.primaryA,"0.08"),borderSelected:e.primaryA,borderInverseOpaque:r.gray700,borderInverseTransparent:a(e.primaryB,"0.2"),borderInverseSelected:e.primaryB},n={backgroundStateDisabled:r.gray50,backgroundOverlayDark:a(r.black,"0.3"),backgroundOverlayLight:a(r.black,"0.08"),backgroundAccent:e.accent,backgroundNegative:e.negative,backgroundWarning:e.warning,backgroundPositive:e.positive,backgroundLightAccent:r.blue50,backgroundLightNegative:r.red50,backgroundLightWarning:r.yellow50,backgroundLightPositive:r.green50,backgroundAlwaysDark:r.black,backgroundAlwaysLight:r.white,contentStateDisabled:r.gray400,contentAccent:e.accent,contentOnColor:r.white,contentOnColorInverse:r.black,contentNegative:e.negative,contentWarning:r.yellow500,contentPositive:e.positive,borderStateDisabled:r.gray50,borderAccent:r.blue400,borderAccentLight:r.blue200,borderNegative:r.red200,borderWarning:r.yellow200,borderPositive:r.green200};return u(u({},t),n)}()),animation:v,breakpoints:b,borders:l,direction:"auto",grid:m,lighting:c,mediaQuery:k,sizing:w,typography:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p,t={fontFamily:e.primaryFontFamily,fontSize:"12px",fontWeight:"normal",lineHeight:"20px"},n={fontFamily:e.primaryFontFamily,fontSize:"12px",fontWeight:500,lineHeight:"16px"},r={fontFamily:e.primaryFontFamily,fontSize:"14px",fontWeight:"normal",lineHeight:"20px"},i={fontFamily:e.primaryFontFamily,fontSize:"14px",fontWeight:500,lineHeight:"16px"},a={fontFamily:e.primaryFontFamily,fontSize:"16px",fontWeight:"normal",lineHeight:"24px"},o={fontFamily:e.primaryFontFamily,fontSize:"16px",fontWeight:500,lineHeight:"20px"},u={fontFamily:e.primaryFontFamily,fontSize:"18px",fontWeight:"normal",lineHeight:"28px"},s={fontFamily:e.primaryFontFamily,fontSize:"18px",fontWeight:500,lineHeight:"24px"},l={fontFamily:e.primaryFontFamily,fontSize:"20px",fontWeight:500,lineHeight:"28px"},c={fontFamily:e.primaryFontFamily,fontSize:"24px",fontWeight:500,lineHeight:"32px"},f={fontFamily:e.primaryFontFamily,fontSize:"28px",fontWeight:500,lineHeight:"36px"},h={fontFamily:e.primaryFontFamily,fontSize:"32px",fontWeight:500,lineHeight:"40px"},v={fontFamily:e.primaryFontFamily,fontSize:"36px",fontWeight:500,lineHeight:"44px"},b={fontFamily:e.primaryFontFamily,fontSize:"40px",fontWeight:500,lineHeight:"52px"},m={fontFamily:e.primaryFontFamily,fontSize:"36px",fontWeight:500,lineHeight:"44px"},g={fontFamily:e.primaryFontFamily,fontSize:"44px",fontWeight:500,lineHeight:"52px"},k={fontFamily:e.primaryFontFamily,fontSize:"52px",fontWeight:500,lineHeight:"64px"},w={fontFamily:e.primaryFontFamily,fontSize:"96px",fontWeight:500,lineHeight:"112px"};return{font100:t,font150:n,font200:r,font250:i,font300:a,font350:o,font400:u,font450:s,font550:l,font650:c,font750:f,font850:h,font950:v,font1050:b,font1150:m,font1250:g,font1350:k,font1450:w,ParagraphXSmall:t,ParagraphSmall:r,ParagraphMedium:a,ParagraphLarge:u,LabelXSmall:n,LabelSmall:i,LabelMedium:o,LabelLarge:s,HeadingXSmall:l,HeadingSmall:c,HeadingMedium:f,HeadingLarge:h,HeadingXLarge:v,HeadingXXLarge:b,DisplayXSmall:m,DisplaySmall:g,DisplayMedium:k,DisplayLarge:w,MonoParagraphXSmall:d(d({},t),{},{fontFamily:y}),MonoParagraphSmall:d(d({},r),{},{fontFamily:y}),MonoParagraphMedium:d(d({},a),{},{fontFamily:y}),MonoParagraphLarge:d(d({},u),{},{fontFamily:y}),MonoLabelXSmall:d(d({},n),{},{fontFamily:y}),MonoLabelSmall:d(d({},i),{},{fontFamily:y}),MonoLabelMedium:d(d({},o),{},{fontFamily:y}),MonoLabelLarge:d(d({},s),{},{fontFamily:y}),MonoHeadingXSmall:d(d({},l),{},{fontFamily:y}),MonoHeadingSmall:d(d({},c),{},{fontFamily:y}),MonoHeadingMedium:d(d({},f),{},{fontFamily:y}),MonoHeadingLarge:d(d({},h),{},{fontFamily:y}),MonoHeadingXLarge:d(d({},v),{},{fontFamily:y}),MonoHeadingXXLarge:d(d({},b),{},{fontFamily:y}),MonoDisplayXSmall:d(d({},m),{},{fontFamily:y}),MonoDisplaySmall:d(d({},g),{},{fontFamily:y}),MonoDisplayMedium:d(d({},k),{},{fontFamily:y}),MonoDisplayLarge:d(d({},w),{},{fontFamily:y})}}(),zIndex:{modal:2e3}}},,,,function(e,t,n){"use strict";var r=n(1),i=n(26),a=r.createContext(i.a);t.a=function(e){var t=e.theme,n=e.children;return r.createElement(a.Provider,{value:t},n)}}]]);
//# sourceMappingURL=2.6d7de458.chunk.js.map