2025-07-18 14:46:06,522 - [config.py:292] - initialize_config - INFO - -User config not found, will be created.
2025-07-18 14:46:06,529 - [config.py:238] - set_and_save_config - WARNING - db_path not exist in config!
2025-07-18 14:46:06,529 - [config.py:238] - set_and_save_config - WARNING - vdb_img_path not exist in config!
2025-07-18 14:46:08,935 - [file_utils.py:53] - ensure_dir - INFO - files: created folder userdata\db
2025-07-18 14:46:08,935 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 14:46:08,935 - [db_manager.py:60] - db_initialize - INFO - db not existed
2025-07-18 14:46:08,937 - [db_manager.py:72] - db_initialize - INFO - db is empty, writing new table.
2025-07-18 14:46:08,937 - [db_manager.py:137] - db_create_table - INFO - Making table
2025-07-18 14:46:08,949 - [db_manager.py:166] - db_update_data - INFO - Inserting data
2025-07-18 14:46:08,990 - [file_utils.py:38] - empty_directory - ERROR - [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'cache\\logs\\wr.log'
2025-07-18 14:49:46,297 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 14:56:41,024 - [file_utils.py:288] - get_extension - WARNING - [Errno 2] No such file or directory: 'extension\\LLM_summary_mvp\\meta.json'
2025-07-18 15:51:46,874 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 15:51:46,874 - [db_manager.py:60] - db_initialize - INFO - db not existed
2025-07-18 15:51:46,875 - [db_manager.py:72] - db_initialize - INFO - db is empty, writing new table.
2025-07-18 15:51:46,875 - [db_manager.py:137] - db_create_table - INFO - Making table
2025-07-18 15:51:46,888 - [db_manager.py:166] - db_update_data - INFO - Inserting data
2025-07-18 15:51:51,044 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 15:51:51,045 - [db_manager.py:90] - db_initialize - INFO - db existed and not empty
2025-07-18 15:51:51,048 - [db_manager.py:578] - db_num_records - INFO - db_filepath: userdata\db\91445_2025-07_wind_TEMP_READ.db, rows_count: 1
2025-07-18 15:51:51,048 - [db_manager.py:579] - db_num_records - INFO - rows_count_all: 1
2025-07-18 15:51:51,053 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:51,054 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:51:51,055 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:51,056 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:51:51,058 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:51:51,061 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:51,061 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:51:51,063 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:51,063 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:51:51,078 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:51,078 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 1, 0, 0, 1), datetime_end=datetime.datetime(2025, 7, 31, 23, 59, 59)
2025-07-18 15:51:51,079 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:51,080 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1751328001 AND 1754006399)
2025-07-18 15:51:51,467 - [file_utils.py:53] - ensure_dir - INFO - files: created folder userdata\result_date_state
2025-07-18 15:51:52,478 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:52,479 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 1, 1, 0, 0, 1), datetime_end=datetime.datetime(2025, 12, 1, 23, 59, 59)
2025-07-18 15:51:52,480 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:52,480 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1735689601 AND 1764633599)
2025-07-18 15:51:52,711 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:52,711 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 1, 0, 0, 1), datetime_end=datetime.datetime(2025, 7, 31, 23, 59, 59)
2025-07-18 15:51:52,712 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:52,713 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1751328001 AND 1754006399)
2025-07-18 15:51:52,716 - [file_utils.py:53] - ensure_dir - INFO - files: created folder userdata\result_wintitle
2025-07-18 15:51:52,717 - [file_utils.py:262] - save_dict_as_json_to_path - INFO - files: json has been saved at userdata\result_wintitle\2025-07.json
2025-07-18 15:51:52,960 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 15:51:53,435 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:53,435 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:51:53,437 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:53,437 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:51:53,439 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:51:54,977 - [db_manager.py:578] - db_num_records - INFO - db_filepath: userdata\db\91445_2025-07_wind_TEMP_READ.db, rows_count: 1
2025-07-18 15:51:54,977 - [db_manager.py:579] - db_num_records - INFO - rows_count_all: 1
2025-07-18 15:51:54,978 - [file_utils.py:262] - save_dict_as_json_to_path - INFO - files: json has been saved at cache\footer_info_cache.json
2025-07-18 15:51:54,979 - [state.py:242] - make_webui_footer_state_data_cache - INFO - footer info updated.
2025-07-18 15:52:35,194 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:52:35,196 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:52:35,197 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:52:35,198 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:52:35,200 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:52:35,201 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:52:35,202 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:52:35,203 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:52:35,204 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:52:35,498 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 15:52:36,284 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:52:36,284 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:52:36,286 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:52:36,287 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:52:36,288 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:56:08,677 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:56:08,677 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:56:08,678 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:56:08,679 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:56:08,680 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:56:08,683 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:56:08,683 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:56:08,684 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:56:08,685 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:56:08,980 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 15:56:09,520 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:56:09,522 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:56:09,523 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:56:09,524 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:56:09,526 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
