2025-07-18 14:46:06,522 - [config.py:292] - initialize_config - INFO - -User config not found, will be created.
2025-07-18 14:46:06,529 - [config.py:238] - set_and_save_config - WARNING - db_path not exist in config!
2025-07-18 14:46:06,529 - [config.py:238] - set_and_save_config - WARNING - vdb_img_path not exist in config!
2025-07-18 14:46:08,935 - [file_utils.py:53] - ensure_dir - INFO - files: created folder userdata\db
2025-07-18 14:46:08,935 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 14:46:08,935 - [db_manager.py:60] - db_initialize - INFO - db not existed
2025-07-18 14:46:08,937 - [db_manager.py:72] - db_initialize - INFO - db is empty, writing new table.
2025-07-18 14:46:08,937 - [db_manager.py:137] - db_create_table - INFO - Making table
2025-07-18 14:46:08,949 - [db_manager.py:166] - db_update_data - INFO - Inserting data
2025-07-18 14:46:08,990 - [file_utils.py:38] - empty_directory - ERROR - [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'cache\\logs\\wr.log'
2025-07-18 14:49:46,297 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 14:56:41,024 - [file_utils.py:288] - get_extension - WARNING - [Errno 2] No such file or directory: 'extension\\LLM_summary_mvp\\meta.json'
2025-07-18 15:51:46,874 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 15:51:46,874 - [db_manager.py:60] - db_initialize - INFO - db not existed
2025-07-18 15:51:46,875 - [db_manager.py:72] - db_initialize - INFO - db is empty, writing new table.
2025-07-18 15:51:46,875 - [db_manager.py:137] - db_create_table - INFO - Making table
2025-07-18 15:51:46,888 - [db_manager.py:166] - db_update_data - INFO - Inserting data
2025-07-18 15:51:51,044 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 15:51:51,045 - [db_manager.py:90] - db_initialize - INFO - db existed and not empty
2025-07-18 15:51:51,048 - [db_manager.py:578] - db_num_records - INFO - db_filepath: userdata\db\91445_2025-07_wind_TEMP_READ.db, rows_count: 1
2025-07-18 15:51:51,048 - [db_manager.py:579] - db_num_records - INFO - rows_count_all: 1
2025-07-18 15:51:51,053 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:51,054 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:51:51,055 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:51,056 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:51:51,058 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:51:51,061 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:51,061 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:51:51,063 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:51,063 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:51:51,078 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:51,078 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 1, 0, 0, 1), datetime_end=datetime.datetime(2025, 7, 31, 23, 59, 59)
2025-07-18 15:51:51,079 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:51,080 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1751328001 AND 1754006399)
2025-07-18 15:51:51,467 - [file_utils.py:53] - ensure_dir - INFO - files: created folder userdata\result_date_state
2025-07-18 15:51:52,478 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:52,479 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 1, 1, 0, 0, 1), datetime_end=datetime.datetime(2025, 12, 1, 23, 59, 59)
2025-07-18 15:51:52,480 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:52,480 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1735689601 AND 1764633599)
2025-07-18 15:51:52,711 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:52,711 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 1, 0, 0, 1), datetime_end=datetime.datetime(2025, 7, 31, 23, 59, 59)
2025-07-18 15:51:52,712 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:52,713 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1751328001 AND 1754006399)
2025-07-18 15:51:52,716 - [file_utils.py:53] - ensure_dir - INFO - files: created folder userdata\result_wintitle
2025-07-18 15:51:52,717 - [file_utils.py:262] - save_dict_as_json_to_path - INFO - files: json has been saved at userdata\result_wintitle\2025-07.json
2025-07-18 15:51:52,960 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 15:51:53,435 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:51:53,435 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:51:53,437 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:51:53,437 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:51:53,439 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:51:54,977 - [db_manager.py:578] - db_num_records - INFO - db_filepath: userdata\db\91445_2025-07_wind_TEMP_READ.db, rows_count: 1
2025-07-18 15:51:54,977 - [db_manager.py:579] - db_num_records - INFO - rows_count_all: 1
2025-07-18 15:51:54,978 - [file_utils.py:262] - save_dict_as_json_to_path - INFO - files: json has been saved at cache\footer_info_cache.json
2025-07-18 15:51:54,979 - [state.py:242] - make_webui_footer_state_data_cache - INFO - footer info updated.
2025-07-18 15:52:35,194 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:52:35,196 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:52:35,197 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:52:35,198 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:52:35,200 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:52:35,201 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:52:35,202 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:52:35,203 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:52:35,204 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:52:35,498 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 15:52:36,284 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:52:36,284 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:52:36,286 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:52:36,287 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:52:36,288 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:56:08,677 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:56:08,677 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:56:08,678 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:56:08,679 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:56:08,680 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 15:56:08,683 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:56:08,683 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:56:08,684 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:56:08,685 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:56:08,980 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 15:56:09,520 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 15:56:09,522 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 15:56:09,523 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 15:56:09,524 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 15:56:09,526 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:01:58,161 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:01:58,162 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:01:58,163 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:01:58,164 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:01:58,166 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:01:58,168 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:01:58,169 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:01:58,170 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:01:58,171 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:01:58,471 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 16:01:59,127 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:01:59,128 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:01:59,129 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:01:59,130 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:01:59,132 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:08:24,149 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:08:24,149 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:08:24,150 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:08:24,151 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:08:24,152 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:08:24,154 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:08:24,154 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:08:24,155 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:08:24,156 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:08:24,468 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 16:08:25,300 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:08:25,301 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:08:25,303 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:08:25,303 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:08:25,304 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:17:19,700 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 16:17:19,701 - [db_manager.py:90] - db_initialize - INFO - db existed and not empty
2025-07-18 16:17:21,514 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:17:21,515 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:17:21,517 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:17:21,518 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:17:21,520 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:17:21,524 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:17:21,525 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:17:21,527 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:17:21,528 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:17:21,534 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:17:21,535 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:17:21,536 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:17:21,536 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:17:21,539 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:17:21,545 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:17:21,545 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:17:21,546 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:17:21,547 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:17:22,459 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 16:17:22,461 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 16:17:22,945 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:17:22,946 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:17:22,947 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:17:22,948 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:17:22,950 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:17:22,983 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:17:22,984 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:17:22,986 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:17:22,987 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:17:22,990 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:18:42,625 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:18:42,625 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 17, 3, 0), datetime_end=datetime.datetime(2025, 7, 18, 2, 59, 59)
2025-07-18 16:18:42,627 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:18:42,627 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752721200 AND 1752807599)
2025-07-18 16:18:42,629 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:18:42,632 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:18:42,632 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 17, 3, 0), datetime_end=datetime.datetime(2025, 7, 18, 2, 59, 59)
2025-07-18 16:18:42,633 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:18:42,633 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752721200 AND 1752807599)
2025-07-18 16:18:43,032 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:18:43,033 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:18:43,034 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:18:43,035 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:18:43,036 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:18:43,038 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:18:43,039 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:18:43,040 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:18:43,041 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:18:48,054 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:18:48,055 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:18:48,057 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:18:48,057 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:18:48,059 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 16:18:49,298 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 16:18:49,299 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 16:18:49,300 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 16:18:49,301 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 16:18:49,303 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 18:21:51,529 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 18:21:51,530 - [db_manager.py:90] - db_initialize - INFO - db existed and not empty
2025-07-18 18:21:51,536 - [file_utils.py:53] - ensure_dir - INFO - files: created folder cache\locks
2025-07-18 18:21:51,536 - [file_utils.py:53] - ensure_dir - INFO - files: created folder cache\locks\LOCK_MAINTAIN
2025-07-18 18:21:51,537 - [main.py:328] - main - INFO - 
Run in CLI mode.

2025-07-18 18:21:54,725 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 18:21:54,726 - [db_manager.py:90] - db_initialize - INFO - db existed and not empty
2025-07-18 18:21:55,546 - [record_screen.py:269] - main - INFO - Windrecorder: config.OCR_index_strategy: 1
2025-07-18 18:21:55,548 - [record_screen.py:290] - main - INFO - config.screentime_not_change_to_pause_record=5
2025-07-18 18:21:55,548 - [file_utils.py:53] - ensure_dir - INFO - files: created folder cache\i_frames
2025-07-18 18:21:55,607 - [file_utils.py:53] - ensure_dir - INFO - files: created folder userdata\videos
2025-07-18 18:21:55,653 - [ocr_manager.py:984] - ocr_process_videos - INFO - Processing all video files.
2025-07-18 18:21:55,887 - [file_utils.py:53] - ensure_dir - INFO - files: created folder cache\db_backup
2025-07-18 18:21:57,257 - [file_utils.py:53] - ensure_dir - INFO - files: created folder cache\win_title
2025-07-18 18:21:57,271 - [record_screen.py:229] - monitor_compare_screenshot - INFO - monitor_idle_minutes:0, similarity:None
2025-07-18 18:21:58,059 - [record.py:447] - record_screen_via_screenshot_process - INFO - start new screenshot record: config.record_seconds=900, 3
2025-07-18 18:22:01,882 - [file_utils.py:53] - ensure_dir - INFO - files: created folder cache_screenshot\2025-07-18_18-22-01
2025-07-18 18:22:02,030 - [file_utils.py:262] - save_dict_as_json_to_path - INFO - files: json has been saved at cache_screenshot\2025-07-18_18-22-01\tmp_db_json_all_files.json
2025-07-18 18:22:02,030 - [record.py:554] - record_screen_via_screenshot_process - INFO - saved screenshot to cache_screenshot\2025-07-18_18-22-01\2025-07-18_18-22-01.png
2025-07-18 18:22:02,473 - [record.py:583] - record_screen_via_screenshot_process - INFO - ocr res writing
2025-07-18 18:22:02,491 - [file_utils.py:262] - save_dict_as_json_to_path - INFO - files: json has been saved at cache_screenshot\2025-07-18_18-22-01\tmp_db.json
2025-07-18 18:22:05,174 - [file_utils.py:262] - save_dict_as_json_to_path - INFO - files: json has been saved at cache_screenshot\2025-07-18_18-22-01\tmp_db_json_all_files.json
2025-07-18 18:22:05,174 - [record.py:554] - record_screen_via_screenshot_process - INFO - saved screenshot to cache_screenshot\2025-07-18_18-22-01\2025-07-18_18-22-04.png
2025-07-18 18:22:05,392 - [record.py:583] - record_screen_via_screenshot_process - INFO - ocr res writing
2025-07-18 18:22:05,394 - [file_utils.py:262] - save_dict_as_json_to_path - INFO - files: json has been saved at cache_screenshot\2025-07-18_18-22-01\tmp_db.json
2025-07-18 18:22:07,933 - [file_utils.py:262] - save_dict_as_json_to_path - INFO - files: json has been saved at cache_screenshot\2025-07-18_18-22-01\tmp_db_json_all_files.json
2025-07-18 18:22:07,933 - [record.py:554] - record_screen_via_screenshot_process - INFO - saved screenshot to cache_screenshot\2025-07-18_18-22-01\2025-07-18_18-22-07.png
2025-07-18 18:22:10,835 - [record.py:506] - record_screen_via_screenshot_process - ERROR - capture screenshot error: '_thread._local' object has no attribute 'data'
2025-07-18 18:22:13,764 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:22:15,295 - [record_wintitle.py:107] - get_foreground_deep_linking - WARNING - get deep_linking fail: Find Control Timeout(0.5s): {ControlType: DocumentControl}
2025-07-18 18:22:16,818 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:22:19,718 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:22:22,657 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:22:26,296 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:22:27,465 - [record_screen.py:229] - monitor_compare_screenshot - INFO - monitor_idle_minutes:0, similarity:[0.15338645418326693]
2025-07-18 18:22:28,882 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:22:32,163 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:22:35,133 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:22:38,051 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:23:22,060 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 18:23:22,061 - [db_manager.py:90] - db_initialize - INFO - db existed and not empty
2025-07-18 18:23:23,777 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 18:23:23,778 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 18:23:23,780 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 18:23:23,781 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 18:23:23,785 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 18:23:23,788 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 18:23:23,789 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 18:23:23,790 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 18:23:23,791 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 18:23:23,797 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 18:23:23,798 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 18:23:23,800 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 18:23:23,800 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 18:23:23,807 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 18:23:23,810 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 18:23:23,810 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 18:23:23,811 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 18:23:23,814 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 18:23:23,814 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 18:23:23,817 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 1, 0, 0, 1), datetime_end=datetime.datetime(2025, 7, 31, 23, 59, 59)
2025-07-18 18:23:23,818 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 18:23:23,819 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1751328001 AND 1754006399)
2025-07-18 18:23:24,198 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 18:23:24,205 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 1, 0, 0, 1), datetime_end=datetime.datetime(2025, 7, 31, 23, 59, 59)
2025-07-18 18:23:24,221 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 18:23:24,242 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1751328001 AND 1754006399)
2025-07-18 18:23:25,488 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 18:23:25,491 - [utils.py:578] - get_cmd_tool_echo - INFO - command: ['ocr_lib\\Windows.Media.Ocr.Cli.exe', '-s']
2025-07-18 18:23:25,954 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 18:23:25,954 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 18:23:25,955 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 18:23:25,956 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 18:23:25,959 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 18:23:25,978 - [db_manager.py:270] - db_search_data - INFO - Querying keywords
2025-07-18 18:23:25,980 - [db_manager.py:286] - db_search_data - INFO - datetime_start=datetime.datetime(2025, 7, 18, 3, 0), datetime_end=datetime.datetime(2025, 7, 19, 2, 59, 59)
2025-07-18 18:23:25,982 - [db_manager.py:294] - db_search_data - INFO - Querying userdata\db\91445_2025-07_wind_TEMP_READ.db
2025-07-18 18:23:25,983 - [db_manager.py:348] - db_search_data - INFO - SQL query:
 SELECT * FROM video_text WHERE ocr_text LIKE '%%' AND (videofile_time BETWEEN 1752807600 AND 1752893999)
2025-07-18 18:23:25,986 - [oneday.py:115] - render - INFO - day_min_timestamp_dt=None, day_max_timestamp_dt=None
2025-07-18 18:26:43,119 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 18:26:43,121 - [db_manager.py:90] - db_initialize - INFO - db existed and not empty
2025-07-18 18:26:43,126 - [main.py:328] - main - INFO - 
Run in CLI mode.

2025-07-18 18:26:44,933 - [db_manager.py:46] - db_main_initialize - INFO - Initialize the database...
2025-07-18 18:26:44,934 - [db_manager.py:90] - db_initialize - INFO - db existed and not empty
2025-07-18 18:26:45,607 - [record_screen.py:269] - main - INFO - Windrecorder: config.OCR_index_strategy: 1
2025-07-18 18:26:45,609 - [ocr_manager.py:984] - ocr_process_videos - INFO - Processing all video files.
2025-07-18 18:26:45,610 - [record_screen.py:290] - main - INFO - config.screentime_not_change_to_pause_record=5
2025-07-18 18:26:45,610 - [record.py:693] - index_cache_screenshots_dir_process - INFO - cache_screenshot\2025-07-18_18-22-01 not submit to db, submiting...
2025-07-18 18:26:45,611 - [record.py:608] - submit_data_to_sqlite_db_process - INFO - submitting cache_screenshot\2025-07-18_18-22-01 to db
2025-07-18 18:26:45,726 - [record.py:618] - submit_data_to_sqlite_db_process - INFO - tmp_db_json records not enough
2025-07-18 18:26:45,936 - [file_utils.py:383] - delete_files_via_config - INFO - move file to recycle bin: cache_screenshot\2025-07-18_18-22-01
2025-07-18 18:26:46,765 - [record.py:702] - index_cache_screenshots_dir_process - INFO - cache_screenshot\2025-07-18_18-22-01 not enough data, marked as DISCARD.
2025-07-18 18:26:47,407 - [record_screen.py:229] - monitor_compare_screenshot - INFO - monitor_idle_minutes:0, similarity:None
2025-07-18 18:26:48,065 - [record.py:447] - record_screen_via_screenshot_process - INFO - start new screenshot record: config.record_seconds=900, 3
2025-07-18 18:26:51,789 - [record.py:506] - record_screen_via_screenshot_process - ERROR - capture screenshot error: '_thread._local' object has no attribute 'data'
2025-07-18 18:26:53,807 - [record_wintitle.py:107] - get_foreground_deep_linking - WARNING - get deep_linking fail: Find Control Timeout(0.5s): {ControlType: DocumentControl}
2025-07-18 18:26:54,708 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:26:57,751 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:27:00,778 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:27:03,728 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:27:07,019 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:27:10,227 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
2025-07-18 18:27:12,964 - [record.py:484] - record_screen_via_screenshot_process - INFO - wintitle 欢迎 - Windrecorder-main  - Visual Studio Code | Code.exe contains exclude words ['Windrecorder', 'Windrecord - webui', 'KeePass', 'Enpass.exe', '1Password', 'authenticator.exe', 'Payment method', 'Card information', 'forget password', 'Visual Studio Code', 'New Tab -', 'chrome://newtab - Google Chrome', 'Task Switching']
