@echo off
chcp 65001 >nul
echo 正在创建桌面快捷方式...

REM 获取桌面路径
for /f "tokens=3*" %%i in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop') do set DESKTOP=%%i %%j

REM 创建VBS脚本来创建快捷方式
echo Set WshShell = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo Set Shortcut = WshShell.CreateShortcut("%DESKTOP%\Windrecorder启动器.lnk") >> temp_shortcut.vbs
echo Shortcut.TargetPath = "%~dp0启动Windrecorder.bat" >> temp_shortcut.vbs
echo Shortcut.WorkingDirectory = "%~dp0" >> temp_shortcut.vbs
echo Shortcut.IconLocation = "%~dp0__assets__\icon-tray.ico" >> temp_shortcut.vbs
echo Shortcut.Description = "Windrecorder Screen Recorder" >> temp_shortcut.vbs
echo Shortcut.Save >> temp_shortcut.vbs

echo Set Shortcut2 = WshShell.CreateShortcut("%DESKTOP%\Windrecorder-WebUI.lnk") >> temp_shortcut.vbs
echo Shortcut2.TargetPath = "%~dp0start_webui_only.bat" >> temp_shortcut.vbs
echo Shortcut2.WorkingDirectory = "%~dp0" >> temp_shortcut.vbs
echo Shortcut2.IconLocation = "%~dp0__assets__\icon-tray.ico" >> temp_shortcut.vbs
echo Shortcut2.Description = "Windrecorder Web Interface" >> temp_shortcut.vbs
echo Shortcut2.Save >> temp_shortcut.vbs

REM 执行VBS脚本
cscript //nologo temp_shortcut.vbs

REM 删除临时文件
del temp_shortcut.vbs

echo.
echo ✓ 桌面快捷方式创建完成！
echo   - Windrecorder启动器.lnk
echo   - Windrecorder-WebUI.lnk
echo.
pause
