# Set workspace to Windrecorder dir
import os
import sys

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(parent_parent_dir)
os.chdir("..")
os.chdir("..")

from windrecorder.config import config  # noqa: E402

# ------------------------------------------------------------

print("writing config...")
config.set_and_save_config("img_embed_module_install", False)
config.set_and_save_config("enable_img_embed_search", False)
