An extension may contains at least the following files:


# meta.json
```json
{
    "extension_name": "",
    "developer_name": "",
    "developer_url": "",
    "version": "0.0.1",
    "description_markdown": ""
}
```


# bash_for_user.bat
```
@echo off
echo Loading extension, please stand by.
echo.

cd /d %~dp0
for /F "tokens=* USEBACKQ" %%A in (`python -m poetry env info --path`) do call "%%A\Scripts\activate.bat"
chcp 65001
cls
cd ..
cd ..

:: extension code below
python "extension/extension_dir/your_extension.py"
```