[tool.poetry]
name = "windrecorder"
version = "0.0.31"
description = "Windrecorder is a memory search app by records everything on your screen in small size, to let you rewind what you have seen, query through OCR text or image description, and get activity statistics. Developed as MacOS App Rewind.ai / Copilot Recall alternative tool on Windows platform."
authors = ["yuka-friends"]
license = "GPL-2.0"
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10,<3.12"
jieba = "^0.42.1"
matplotlib = "^3.7.1"
numpy = "^1.24.2"
onnxruntime = "~1.15.1"
opencv_python = "~********"
pandas = "^2.0.3"
pyclipper = "~1.3.0.post4"
pywin32 = "~306"
Send2Trash = "^1.8.2"
Shapely = "~2.0.1"
streamlit = "^1.29.0"
pillow = "^10.1.0"
wordcloud = "^1.9.2"
pystray = "^0.19.5"
customtkinter = "^5.2.1"
psutil = "^5.9.5"
scikit-image = "^0.22.0"
faiss-cpu = "^1.7.4"
tqdm = "^4.65.0"
streamlit-tags = "^1.2.8"
mss = "^9.0.1"
pygetwindow = "^0.0.9"
uiautomation = "^2.0.20"
openai = "^1.40.8"

[tool.poetry.group.dev.dependencies]
pre-commit = "^3.5.0"
ipykernel = "^6.29.2"

[[tool.poetry.source]]
name = "pypi-default"
url = "https://pypi.org/simple"
priority = "primary"

[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
priority = "supplemental"

[[tool.poetry.source]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple"
priority = "supplemental"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
