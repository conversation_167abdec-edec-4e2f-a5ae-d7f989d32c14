@echo off
cd /d %~dp0

echo Starting Windrecorder...
echo.

echo [1/2] Starting background recording...
start /min "" "%~dp0start_app.bat"

echo [2/2] Starting Web UI...
timeout /t 3 >nul
call .venv\Scripts\activate.bat
start "" python -m streamlit run webui.py --server.port 8501

echo.
echo Windrecorder is starting!
echo - Background recording: Check system tray
echo - Web UI: http://localhost:8501
echo.
pause
