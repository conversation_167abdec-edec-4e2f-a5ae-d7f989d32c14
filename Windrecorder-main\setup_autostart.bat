@echo off
chcp 65001 >nul
title Windrecorder 开机自启动设置
color 0e

echo.
echo ========================================
echo        Windrecorder 开机自启动设置
echo ========================================
echo.
echo 请选择操作：
echo.
echo [1] 启用开机自启动
echo [2] 禁用开机自启动
echo [3] 查看当前状态
echo [0] 退出
echo.
set /p choice=请输入选项 (0-3): 

if "%choice%"=="1" goto enable_autostart
if "%choice%"=="2" goto disable_autostart
if "%choice%"=="3" goto check_status
if "%choice%"=="0" goto exit
goto invalid

:enable_autostart
echo.
echo 正在设置开机自启动...

REM 创建启动文件夹快捷方式
set STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
copy "%~dp0start_app.bat" "%STARTUP_FOLDER%\Windrecorder.bat" >nul

REM 添加注册表项
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "Windrecorder" /t REG_SZ /d "\"%~dp0start_app.bat\"" /f >nul

echo ✓ 开机自启动已启用
echo   程序将在下次开机时自动启动
goto end

:disable_autostart
echo.
echo 正在禁用开机自启动...

REM 删除启动文件夹中的文件
set STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
if exist "%STARTUP_FOLDER%\Windrecorder.bat" del "%STARTUP_FOLDER%\Windrecorder.bat" >nul

REM 删除注册表项
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "Windrecorder" /f >nul 2>&1

echo ✓ 开机自启动已禁用
goto end

:check_status
echo.
echo 检查开机自启动状态...
echo.

REM 检查注册表
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "Windrecorder" >nul 2>&1
if %errorlevel%==0 (
    echo ✓ 注册表自启动项：已启用
) else (
    echo ✗ 注册表自启动项：未启用
)

REM 检查启动文件夹
set STARTUP_FOLDER=%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup
if exist "%STARTUP_FOLDER%\Windrecorder.bat" (
    echo ✓ 启动文件夹：已设置
) else (
    echo ✗ 启动文件夹：未设置
)
goto end

:invalid
echo.
echo ✗ 无效选项，请重新选择
timeout /t 2 /nobreak >nul
goto start

:end
echo.
pause

:exit
exit
