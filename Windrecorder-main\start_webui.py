#!/usr/bin/env python3
"""
临时脚本：启动 Windrecorder Web UI
这个脚本可以绕过 tkinter 依赖问题，直接启动 Streamlit Web UI
"""

import os
import sys
import subprocess
import time
import webbrowser
from windrecorder import utils

def start_webui():
    """启动 Web UI"""
    print("正在启动 Windrecorder Web UI...")
    
    # 找到可用端口
    port = utils.find_available_port()
    print(f"使用端口: {port}")
    
    # 启动 Streamlit
    try:
        process = subprocess.Popen([
            sys.executable,
            "-m", "streamlit", "run", "webui.py",
            "--server.port", str(port),
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ])
        
        print(f"Web UI 正在启动，请稍候...")
        print(f"启动完成后，请访问: http://localhost:{port}")
        
        # 等待几秒钟让 Streamlit 启动
        time.sleep(3)
        
        # 自动打开浏览器
        webbrowser.open(f"http://localhost:{port}")
        
        print("Web UI 已启动！按 Ctrl+C 停止服务。")
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n正在停止 Web UI...")
        process.terminate()
        print("Web UI 已停止。")
    except Exception as e:
        print(f"启动 Web UI 时出错: {e}")

if __name__ == "__main__":
    start_webui()
