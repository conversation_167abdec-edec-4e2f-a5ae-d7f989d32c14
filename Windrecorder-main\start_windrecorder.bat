@echo off
title Windrecorder Launcher
color 0e

:menu
cls
echo.
echo ========================================
echo           Windrecorder Launcher
echo ========================================
echo.
echo [1] Start Full Program (Recording + Web UI)
echo [2] Start Recording Only
echo [3] Start Web UI Only
echo [4] Check Status
echo [5] Stop All
echo [0] Exit
echo.
set /p choice=Enter your choice (0-5): 

if "%choice%"=="1" goto full_start
if "%choice%"=="2" goto recording_only
if "%choice%"=="3" goto webui_only
if "%choice%"=="4" goto status
if "%choice%"=="5" goto stop_all
if "%choice%"=="0" goto exit
echo Invalid choice. Please try again.
timeout /t 2 >nul
goto menu

:full_start
echo.
echo Starting Windrecorder...
echo 1. Starting background recording...
start /min "" "%~dp0start_app.bat"
echo 2. Waiting 5 seconds...
timeout /t 5 >nul
echo 3. Starting Web UI...
start "" "%~dp0start_webui_only.bat"
echo.
echo Windrecorder started successfully!
echo Check system tray for recording status.
echo Web UI will open in your browser.
pause
goto menu

:recording_only
echo.
echo Starting background recording...
start /min "" "%~dp0start_app.bat"
echo Recording started. Check system tray.
pause
goto menu

:webui_only
echo.
echo Starting Web UI...
start "" "%~dp0start_webui_only.bat"
echo Web UI is starting...
pause
goto menu

:status
echo.
echo Checking status...
echo.
tasklist | findstr python.exe
if %errorlevel%==0 (
    echo Python processes found.
) else (
    echo No Python processes running.
)
echo.
netstat -an | findstr :8501
if %errorlevel%==0 (
    echo Web UI port 8501 is active.
) else (
    echo Web UI is not running.
)
pause
goto menu

:stop_all
echo.
echo Stopping all Windrecorder processes...
taskkill /f /im python.exe 2>nul
taskkill /f /im pythonw.exe 2>nul
echo All processes stopped.
pause
goto menu

:exit
exit
