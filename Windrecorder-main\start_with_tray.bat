@echo off
cd /d %~dp0

echo Starting Windrecorder with system tray...
echo.

REM Clean up any existing lock files
if exist "cache\locks\LOCK_FILE_TRAY.MD" del "cache\locks\LOCK_FILE_TRAY.MD"
if exist "cache\locks\LOCK_FILE_RECORD.MD" del "cache\locks\LOCK_FILE_RECORD.MD"

echo Cleaned lock files.
echo Starting main program...
echo Look for the Windrecorder icon in your system tray!
echo.

REM Start the main program
call .venv\Scripts\activate.bat
python main.py

echo.
echo Main program exited.
pause
