#!/usr/bin/env python3
"""
测试系统托盘功能
"""

import os
import sys
import time
from PIL import Image
import pystray

def get_tray_icon():
    """获取托盘图标"""
    try:
        image = Image.open(os.path.join("__assets__", "icon-tray.png"))
        image = image.convert("RGBA")
        return image
    except Exception as e:
        print(f"Error loading icon: {e}")
        # 创建一个简单的图标
        image = Image.new('RGBA', (64, 64), (255, 0, 0, 255))
        return image

def on_quit(icon, item):
    """退出程序"""
    print("Quitting...")
    icon.stop()

def on_test(icon, item):
    """测试功能"""
    print("Test clicked!")
    icon.notify("Test notification", "Windrecorder Test")

def setup(icon):
    """设置托盘图标"""
    icon.visible = True
    print("Tray icon should be visible now!")
    icon.notify("Windrecorder Test", "System tray is working!")

def main():
    print("Testing system tray functionality...")
    print("Creating tray icon...")
    
    # 创建菜单
    menu = pystray.Menu(
        pystray.MenuItem("Test", on_test),
        pystray.Menu.SEPARATOR,
        pystray.MenuItem("Quit", on_quit)
    )
    
    # 创建托盘图标
    icon = pystray.Icon(
        "WindrecorderTest",
        get_tray_icon(),
        title="Windrecorder Test",
        menu=menu
    )
    
    print("Starting tray icon...")
    print("Look for the icon in your system tray!")
    print("Right-click the icon to see the menu.")
    
    # 运行托盘图标
    icon.run(setup=setup)

if __name__ == "__main__":
    main()
