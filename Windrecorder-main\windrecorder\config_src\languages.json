{"en": {"main_title": "#### 🦝 Windrecorder", "tab_name_oneday": "Daily", "tab_name_search": "Search", "tab_name_stat": "Summary", "tab_name_lab": "Lab", "tab_name_recording": "Recording & Video Storage", "tab_name_setting": "Settings", "text_search_keyword": "Search Keyword", "text_search_daterange": "Date Range", "text_search_not_found": "No results found for \"{search_content}\".", "text_apply_changes": "Save and Apply All Changes", "utils_toast_setting_saved": "Changes applied.", "footer_info": "Earliest record time: **{first_record_time_str}**, latest record time: **{latest_record_time_str}**, index lines: **{latest_db_records}**, video storage: **{videos_file_size} GB** ({videos_files_count} video files)", "footer_info_help": "There may be a delay of up to two days in updating statistics.", "text_welcome_to_windrecorder": "Welcome to Windrecorder!", "text_updating_month_stat": "Updating statistics for this month...", "text_updating_yearly_stat": "Updating statistics for this year...", "oneday_title": "### 🖼️ Daily", "oneday_btn_yesterday": "← Prev Day", "oneday_btn_tomorrow": "Next Day →", "oneday_btn_today": "Today", "oneday_toggle_search": "Search", "oneday_toggle_search_help": "Separate multiple keywords with spaces. Press Enter without entering any content to list all data in day. Supports searching title and content simultaneously.", "oneday_search_md_none": "<p align='center' style='line-height:2.3;'> ⚠ No results found </p>", "oneday_search_md_result": "<p align='center' style='line-height:2.3;'> → {result_num} results: </p>", "oneday_text_generate_timeline_thumbnail": "Generating timeline thumbnails for day...", "oneday_md_no_enough_thunmbnail_for_timeline": "<p align='center' style='color:rgba(0,0,0,.3)'> Not enough thumbnails to generate timeline for the day. </p>", "oneday_md_rewinding_video_name": "`Rewinding {day_video_file_name}`", "oneday_text_not_found_vid_but_has_data": "Video file for this time not found on disk, but text data nearby is available for retrieval.", "oneday_text_data_indexing_wait_and_refresh": "Indexing data, please wait a few minutes and refresh.", "oneday_text_no_found_record_and_vid_on_disk": "No video file or record found on disk for this time.", "oneday_text_has_vid_but_not_index": "No data index in the database for this day. However, there is a video on disk wait to be indexed.", "oneday_text_vid_and_data_not_found": "Data index and video file not found for this day.", "oneday_text_flag_mark_help": "When you want to add a mark to an important meeting, emergency situation, certain live broadcast, game or movie viewing highlight moment, etc. to facilitate future review, you can add a mark to the present moment through the tray menu.", "oneday_text_help_locate_manually": "Limited by streamlit capabilities, currently can only manually locate back to timestamp.", "oneday_text_file_damaged": "{day_screenshot_filepath} appears to be truncated or damaged.", "oneday_toggle_flag_mark": "Time mark list", "oneday_btn_add_flag_mark_from_select_time": "Add a mark on positioned time", "oneday_btn_flag_mark_save_df": "Save changes", "oneday_wt_text": "Screen Time", "oneday_wt_help": "The statistics may have deviations and are for reference only.", "oneday_ls_title_wintitle": "⏱️ Activity Statistics", "oneday_ls_title_flag_note": "🚩 All Flag Note", "oneday_ls_text_no_wintitle_stat": "<p align='left' style='color:rgba(0,0,0,.3)'> No activity data for the day </p>", "oneday_ls_text_no_wintitle_stat_momnth": "<p align='left' style='color:rgba(0,0,0,.3)'> No activity data for the month </p>", "oneday_ls_text_disable_leftside": "<p align='center' style='color:rgba(0,0,0,.3)'> Sidebar closed, enable in Settings. </p>", "gs_md_search_title": "### 🔎 Search", "gs_input_exclude": "Exclude", "gs_input_search_help": "Use spaces to separate multiple keywords and search together to filter out the screens where these keywords appear at the same time. Window titles can also be used as keywords. If you want to search for a continuous sentence, use '-' instead of a space to connect words, such as searching for 'i-love-you' instead of 'i love you'.", "gs_input_exclude_help": "Leave blank for no exclusion. Multiple keywords can be separated by space.", "gs_text_pls_choose_full_date_range": "Please select a complete time range", "gs_input_result_page": "Result pages", "gs_md_search_result_stat": "Found {all_result_counts} results in {max_page_count} pages for \"{search_content}\".", "gs_md_synonyms_recommend": "Also try: {synonyms_recommend}", "gs_md_search_result_below": "`Time cost: {timecost}s`", "gs_text_intro": "This is the global search page where you can search all the recorded content to date. Press Enter to search after entering the keywords.", "gs_slider_to_rewind_result": "Drag to rewind search results", "gs_text_randomwalk": "Random Walk", "gs_text_video_file_not_on_disk": "Video File **{df_videofile_name}** not on disk.", "gs_option_ocr_text_search_month_range": "Text search (month range)", "gs_option_ocr_text_search_exact_date": "Text search (exact date)", "gs_option_img_emb_search": "Image semantic search", "gs_option_similar_img_search": "Search images with image", "gs_input_img_emb_search": "Use natural language to describe images", "gs_text_img_emb_help": "Use natural language to describe the content of the image. The more precise the description, the closer the result will found. Up to 21 languages such as Chinese, English, Japanese, and Korean are supported here (detailed uform document). The video needs to be embedded after indexing Only by searching, seeing the Setting description for details.", "gs_text_searching": "Searching, please stand by...", "gs_text_loading_embed_model": "Loading Embedding model...", "gs_text_upload_img": "Select image", "gs_text_upload_img_help": "The embedding model tends to search based on the semantics of the image content rather than matching the characteristics of the image itself.", "gs_text_not_install_img_emb": "The Image Embedding module is not enabled or installed. Please go to the settings page to enable it. If there are no relevant options in the settings, please install the image semantic module first. The installation script could be found at the Windrecorder directory: extension\\install_img_embedding_module\\install_img_embedding_module.bat", "stat_md_month_title": "### 🌖 Monthly Statistics", "stat_md_year_title": "### 🎏 {stat_year_title} Statistics", "stat_md_memory_title": "### 🧩 Memory Summary", "stat_btn_generate_update_word_cloud": "Generate/Update Monthly Word Cloud", "stat_text_generating_word_cloud": "Generating, it might takes about 30s...", "stat_text_no_month_word_cloud_pic": "No word cloud image for this month.", "stat_btn_generate_lightbox": "Generate/Update Monthly Lightbox", "stat_text_generating_lightbox": "Generating, it might takes about 5s...", "stat_text_no_month_lightbox": "No lightbox image for this month.", "stat_text_counting": "Counting...", "stat_text_wintitle_keyword_filter": "Keyword filter", "stat_text_wintitle_filter_help": "Separate multiple keywords by spaces to search. Case will be ignored when searching for keywords. Leave blank to list all data for the current month.", "stat_btn_generate_update_ai_poem": "Generate Monthly Poem", "stat_text_generating_ai_poem": "Generating, the daily generation time takes about 5~15s (depending on the speed of the language model)...", "stat_text_custom_lightbox": "You can create custom lightbox images using `extension\\create_custom_lightbox_thumbnail_image`", "lab_title": "### ⚗️ Lab Features", "lab_title_image_semantic_index": "#### Image Embedding Index", "lab_tip_image_semantic_index_not_install": "This feature allows you to search for images by text description.\nTo use image semantic search, you need to install the extension module located at extension/install_img_embedding_module.", "lab_title_llm_features": "#### Language Model", "lab_text_config_llm_api": "Configure Language Model Service API", "lab_tip_llm_api_endpoint": "To use features rely on language models, you need to configure API information first. If you don't know how to do this, please refer to [this Getting Started Guide](https://github.com/yuka-friends/Windrecorder/blob/main/__assets__/how_to_set_LLM_api_endpoint_en.md).", "lab_selectbox_api_endpoint": "API endpoint type", "lab_help_api_endpoint": "API endpoint is a type of request format, which does not mean that you can only use the service provider with the literal format. For example, many cloud model providers and local model services support OpenAI format API endpoints.", "lab_btn_test": "Test API connectivity", "lab_text_testing": "Connecting...", "lab_prompt_test": "Hello, I'm a raccoon.", "lab_text_test_res_success": "API connection successful: ", "lab_text_test_res_fail": "API connection failed: ", "lab_checkbox_extract_tag": "Generate daily activity tags", "lab_input_wintitle_num": "Number of activity titles submitted:", "lab_help_extract_tag": "Use AI to summarize daily activity tags. Only the titles within the screen time will be sent to LLM. You can regenerate by deleting the json data in '{ai_extract_tag_result_dir_ud}'. Depending on the policies of different LLM service providers, some content containing pornography, politics, etc. may fail to generate. When the computer is idle, it will automatically generate tags for the previous day.", "lab_checkbox_day_poem": "Write a poem for daily activity", "lab_help_day_poem": "Write a poem based on the summary tag. You can regenerate by deleting the json data in '{ai_day_poem_result_dir_ud}'. When the computer is idle, the poem of the previous day will be automatically generated.", "lab_text_tag_filter_words": "Remove the following sensitive words before submitting to LLM:", "lab_help_tag_filter_words": "For some LLM services and models, some sensitive audit content or politics may cause generation failure. By setting a filter list, relevant words can be automatically removed before submitting to the model.", "lab_btn_generate_tags": "Summary Tags", "lab_text_generating_tags": "Generating tags... \nDepending on the LLM service, it may take 5~20 seconds.", "lab_text_generating_tags_fail": "Failed to generate tags: {plain_text}", "rs_text_need_to_restart_after_save_setting": "After saving the settings on this page, you need to restart 'Windrecorder' to take effect.", "rs_md_title": "### 📼 Recording & Video Storage", "rs_md_record_setting_title": "#### Recording options", "rs_checkbox_start_record_when_startup": "Run on system startup", "rs_checkbox_start_record_when_startup_help": "After applied, a shortcut will be created for 'start_app.bat' and placed in the system's startup directory. This behavior may be misjudged as a virus and cause 'start_webui.bat' to be removed by some security software, if any interception, please remove it from the isolation area and mark it as trusted software. Or manually create a shortcut for 'start_app.bat' and place it in the system's startup folder.", "rs_checkbox_is_start_recording_on_start_app": "Start recording when app started", "rs_input_stop_recording_when_screen_freeze": "<PERSON><PERSON> recording the next video clip when screen has not changed for minutes (0 for never pause)", "rs_text_skip_recording_by_wintitle": "Skip the next video clip recording and index if the current window title or OCR text includes any of these words:", "rs_tag_input_tip": "Enter to add", "rs_text_ocr_manual_update": "Manual index only", "rs_text_ocr_auto_update": "Automatically index when the video clip is recorded (Recommended)", "rs_selectbox_ocr_strategy": "OCR Indexing Strategy", "rs_input_vid_store_time_help": "0 for never delete. Even if video file has been cleaned, the indexed data will still be retained and can be retrieved, it will just be missing the footage.", "rs_input_vid_compress_time": "Compress video after days", "rs_input_vid_compress_time_help": "0 for never compress.", "rs_selectbox_compress_ratio": "Picture compression ratio", "rs_selectbox_compress_ratio_help": "0.75 times scaling can compress the original video size to 1/4, 0.5 times to 1/8, 0.25 times to 1/16. Compression ratio may vary depending on the video's specific content.", "rs_checkbox_enable_half_res_while_hidpi": "For 'high DPI/high resolution screens', scale to quarter resolution when recording", "rs_text_compress_encoder": "Encoding method", "rs_text_compress_accelerator": "Encoding accelerator", "rs_text_compress_CRF": "Quality CRF", "rs_text_compress_CRF_help": "CRF is the abbreviation of Constant Rate Factor, which is used to set the quality and bit rate control of video encoding. Windrecorder is set to 39 by default for a higher compression rate. In ffmpeg, the value range of CRF depends on The encoder used. For x264 encoders, the CRF value range is 0 to 51, where 0 means lossless, 23 is the default value, and 51 means the worst quality. Lower values mean higher quality, but Will result in a larger file size. Typically, the reasonable value range for x264 encoders is 18 to 28. For x265 encoders, the default CRF value is 28. For libvpx encoders, the CRF value range is 0 to 63. In general, the lower the CRF value, the higher the video quality, but the file size will increase accordingly.", "rs_text_compress_cpu_threads": "Number of CPU threads to use for encoding", "rs_text_compress_cpu_threads_help": "Higher values may improve compression speed but increase CPU usage. Default is one fourth of available CPU cores.", "rs_text_estimate_hint": "Estimated video size recorded every 15 minutes: {min}Mb ~ {max}Mb, depending on the specific recording content and the number of displays.", "rs_btn_encode_benchmark": "Test supported encoding methods ♘", "rs_text_encode_benchmark_loading": "Testing, it will take about 1 minute...", "rs_text_support": "Support", "rs_text_compress_ratio": "Compression ratio ❓", "rs_text_compress_ratio_help": "Compression ratio = compressed video file size/original video file size. The provided test video file is short, and there may be a large deviation in the presentation of this indicator. As the video time increases, the compression rate will be relatively higher .You can replace the __assets__\\test_video_compress.mp4 file for testing.", "rs_text_compress_time": "Compression time (s)", "rs_text_record_strategy_option_all": "Record all displays ({num} total)", "rs_text_record_strategy_option_single": "Record one display only", "rs_text_record_range": "Screen recording range", "rs_text_record_single_display_select": "Record display only:", "rs_text_show_encode_option": "Advanced encoding options", "rs_text_record_encoder": "Recording Encoder", "rs_text_record_help": "Choose h265/AV1 to get better image quality in the same file size, but the performance may be affected during recording and playback. (If supported, hardware acceleration will be automatically enabled)", "rs_text_record_bitrate": "Recording Bitrate (kbps)", "rs_text_bitrate_help": "Video bitrate refers to the transmission speed or processing speed of video data, which will affect the video quality and file size. Specifically, the higher the bitrate, the better the video quality, but the corresponding file The size will also be larger. On the contrary, if the bit rate is lower, the video quality may be blurred or distorted even at the same resolution. In order to keep the picture size smaller, the default is 200kbps.", "rs_text_hevc_tips": "To playback HEVC (h265) videos on Microsoft Edge / Firefox, you may need to install the HEVC video extension: https://apps.microsoft.com/detail/9NMZLZ57R3T7", "rs_text_record_mode_option_ffmpeg": "Directly Record Video (using FFmpeg)", "rs_text_record_mode_option_screenshot_array": "Automatic Flexible Screenshot (using MSS)", "rs_text_record_mode": "Recording mode", "rs_input_screenshot_interval_second": "Screenshot analysis interval (sec)", "rs_checkbox_record_screenshot_method_capture_foreground_window_only": "Only capture foreground active window", "rs_text_screenshot_interval_second_help": "In order to ensure real-time analysis performance, the screenshot interval needs to be greater than the time taken for each frame to calculate and analyze, so it is limited to more than 3 seconds.", "rs_text_ffmpeg_help": "- Designed for high-performance users needing comprehensive computer activity recordings;\n- Skip custom content/window during indexing, but related screens may still be captured without retrieval;\n- Memory-intensive recording process, indexing may consume additional system resources;\n- Up to a 15-minute delay in backtracking available;", "rs_text_screenshot_array_help": "- Ideal for most users who store, recall, and search for memory clues;\n- Efficient, real-time, and updates only changed images;\n- Instant screen replay; auto-converts screenshots to video;\n- Skip specific content/window; record only foreground window;", "rs_checkbox_is_record_system_sound": "Record system sound", "rs_checkbox_record_deep_linking": "Record the current page link of web browser", "rs_help_record_deep_linking": "Supports automatic recording of the page address currently browsed by Chrome, Microsoft Edge, and Firefox. If you feel that the browser browsing is slow/lag, please try to turn off this option.", "rs_text_energy_saving": "When to combine screenshots into video", "rs_text_energy_saving_help": "In order to ensure the storage and playback experience, it is recommended to adjust the setting to `Only when the computer is idle and plugged in` when the computer performance is insufficient and the high usage of programs causes computer lagging. If there are many screenshot caches in `cache_screenshot` that have not been synthesized into a video in time, you can use the script in `extension/manually_convert_screenshot_cache_into_video` to manually synthesize them.", "rs_option_energy_saving_instantly": "Immediately when the clip finished", "rs_option_energy_saving_plug": "Immediately, but only when plugged in (for laptop only)", "rs_option_energy_saving_idle": "Only when the computer is idle and plugged in", "set_md_title": "### ⚙️ Settings", "set_md_index_db": "#### Database Index\n", "set_checkbox_shutdown_after_updated": "Shutdown after indexed", "set_selectbox_local_ocr_engine": "Local OCR Engine", "set_selectbox_ocr_lang": "Main OCR language", "set_help_ocr_lang_windows_ocr_engine": "You can add language packs in System Settings-Language to add corresponding OCR language support.", "set_help_ocr_lang_third_party_engine": "Some third-party OCR engines may not support custom primary languages.", "set_selectbox_local_ocr_engine_help": "It is recommended to use Windows built-in Windows.Media.Ocr.Cli for OCR, which is faster and consumes less resources. You can install third-party OCR engines in the extension directory as needed. They may have higher accuracy, but usually occupy more system resources and take longer to recognize.", "set_md_ocr_ignore_area": "**OCR area to ignore on the display edges**", "set_md_ocr_ignore_area_help": "Enter number as percentage, i.e., '6' == 6%. This option allows to ignore elements at the edges of the screen during OCR, such as browser tabs, Windows Start Menu, in-page advertisements, etc. When recording only the foreground window, this option masks each window proportionally.", "set_toggle_use_screenshot_as_refer": "Use current display as reference", "set_text_choose_displays": "Choose displays", "set_text_top_padding": "Top Padding", "set_text_bottom_padding": "Bottom Padding", "set_text_left_padding": "Left Padding", "set_text_right_padding": "Right Padding", "set_checkbox_enable_3_columns_in_oneday": "Daily - Use center three-column layout", "set_help_enable_3_columns_in_oneday": "The three-column layout is suitable for larger webui display devices and can provide more information and center the traceback screen. The default two-column layout is suitable for smaller webui display devices.", "set_checkbox_use_similar_zh_char_to_search": "Use Chinese similar characters for searching", "set_checkbox_use_similar_zh_char_to_search_help": "OCR may misrecognize similar characters, enabling this option will search for similar shapes simultaneously to the keyword, improving the hit rate of the results.", "set_input_wordcloud_filter": "Filter the following words in the word cloud generation:", "set_input_wordcloud_filter_help": "Some UI keywords may persistently appear, resulting in higher frequency in the data. Here, you can exclude them from the word cloud's statistics.", "set_input_oneday_timeline_thumbnail_num": "Daily - timeline's thumbnails amount", "set_input_oneday_timeline_thumbnail_num_help": "If you want the thumbnail density in Daily timelines to be higher, you can increase this value.", "set_text_video_not_index": "There are {nocred_count} videos waiting to be indexed. Expected time: <{timeCostStr}>. (There are {count} files on disk.)", "set_text_no_video_need_index": "No new videos need indexing at the moment.  (There are {count} files on disk.)", "set_text_one_video_to_index": "1 video is being recorded, no new videos need indexing at the moment.  (There are {count} files on disk.)", "set_text_some_video_will_be_index": "There are {nocred_count} videos that will be indexed automatically. (There are {count} files on disk.)", "set_btn_update_db_manual": "✦ Manually update database", "set_text_updating_db": "Updating database, please do not operate or refresh the page. Estimated time required: {estimate_time_str}", "set_text_db_updated_successful": "Database updated successfully! Time: {timeCostStr}", "set_btn_got_it": "Got it!", "set_md_gui": "#### Interface\n", "set_md_auto_maintain": "#### Automatic maintenance", "set_input_video_hold_days": "Video file retention days", "set_input_max_num_search_page": "In global search, maximum number of results per page", "set_update_latest": "Already up to date.", "set_update_checking": "Checking for updates...", "set_update_new": "\n💫 A new version {tool_version} has been detected. Update by opening `install_update.bat` or tray menu.", "set_update_changelog": "\n [What's new](https://github.com/yuka-friends/Windrecorder/blob/main/CHANGELOG.md)", "set_update_fail": "Failed to check for updates. {e}", "set_pwd_text": "webui access password (leave blank to disable)", "set_pwd_help": "If password input filled, you will be asked to provide a password when accessing webui. This setting will not encrypt your data, but only protects the entrance to webui to avoid access by unfamiliar users in the same LAN.", "set_pwd_forget_help": "Forgot your password? Delete the webui_access_password_md5 item in config_user.json to reset password.", "set_checkbox_enable_img_emb": "Enable image semantic retrieval", "set_text_enable_img_emb_help": "Image semantic retrieval is a method of image retrieval based on the semantic content of images through computer vision technology. It can retrieve queries from large-scale image databases based on the semantic description of the content of the image. Related images. Windrecorder uses uform3-image-text-multilingual-base to embed indexes, which may not be as powerful as expected. When this option turned on, the app will embedding image from video in idle maintain, and then it can be searched. You can also embedding manually through the script under app directory 'extension/index_img_embedding_for_all_videofiles'.", "set_text_img_emb_not_suppport_cuda": "Your device seems not support CUDA, may have lower performance when using CPU to embedding images.", "set_input_img_emb_max_recall_count": "Maximum number of results recalled from each database in Image semantic search", "set_text_help_img_emb_max_recall_count": "A specified number of results will be recalled from each month database. Too high or too low may lead to a decrease in query accuracy. If the results are inaccurate, try adjusting this option.", "set_input_day_begin_minutes": "Daily - Separate time of day", "set_help_day_begin_minutes": "You can divide the records for a period of time after midnight into the same natural day, instead of dividing each day by zero.", "set_checkbox_synonyms_recommand": "Recommend synonyms when searching", "set_help_synonyms_recommand": "If the language is not supported, it will not be enabled. Check the supported languages under config_src\\synonyms.", "set_checkbox_reduce_same_content_at_different_time": "When the same content is shown at different time points in a video slice, only the first occurrence is recorded.", "set_checkbox_recycle_deleted_files": "When needing to delete files, put them in the Recycle Bin instead of deleting it directly.", "set_help_recycle_deleted_files": "Windrecorder generates temporary videos or screenshots when running. For data safety, it put the discarded data into the recycle bin by default instead of deleting it directly. The system recycle bin will be automatically cleaned up regularly. If your disk capacity is low or you want to delete the discarded data directly, please turn off this option. All file deletion operations will be logged in cache\\logs.", "qs_header_1": "<PERSON><PERSON><PERSON>me to Windrecorder {__version__}", "qs_header_2": "Thanks for downloading! This Quick Wizard will help you set it up. Setting still can be adjusted in app afterwards.", "qs_la_text_same_as_previous": "The interface language remains the same as before: English", "qs_un_set_your_username": "Set your username as a database identifier.", "qs_un_describe": "Press enter to use the current username: [{sys_username}], or type a custom username (up to 20 characters).", "qs_un_longer_than_expect": "Exceeded the length limit (20 half-width English characters / 10 full-width Chinese characters).", "qs_un_use_current_name": "Use the current username: {sys_username}", "qs_un_use_custom_name": "Use a custom username: {your_username}", "qs_olang_intro": "The system has the following languages installed. Select one as the main language for OCR detection:", "qs_olang_ocrlang_set_to": "The main language for OCR detection has been set to:", "qs_olang_error": "The input should be numeric and within the range of options.", "qs_olang_one_choice_default_set": "The system language is {os_support_lang}, which has been applied as the main language for OCR detection.\n(Not the language you want? Please check whether the input method/language pack of the selected language is installed on the system.\n(https: //learn.microsoft.com/en-us/uwp/api/windows.media.ocr)", "qs_ocr_title": "OCR Engine Test Results:\n", "qs_ocr_result_describe": "Accuracy: {accuracy}, Recognition Time: {timecost}, Time Required to Index a 15-minute Video: {timecost_15}", "qs_ocr_describe": "> It is recommended to use the built-in Windows.Media.Ocr.Cli, which has faster speed and lower performance consumption.\n", "qs_ocr_describe_disable_clo": "> chineseocr_lite_onnx has been disabled due to compatibility issues. \n(Enabled by modifying 'enable_ocr_chineseocr_lite_onnx':true in user_config.json.)", "qs_ocr_tips_low_accuracy": "! Accuracy too low? Please check whether the input method/language pack for the selected language is installed on your system. \n(https://learn.microsoft.com/en-us/uwp/api/windows.media.ocr)\nIn addition, the test case may not be accurate, please refer to the actual situation.", "qs_ocr_cta": "Please select the OCR engine for extracting screen content:", "qs_ocr_option_recommand": "(Recommended & Default)", "qs_ocr_engine_chosen": "OCR Engine Used:", "qs_ocr_not_has_lang_test_support": "No suitable test case was found for the currently selected language and will be tested on en-US image. Don't worry, the OCR can still work correctly to recognize your language, just the wizard lacks appropriate testing, so the recognition rate may not reflect the actual situation.", "qs_record_current_record": "Current recording settings:", "qs_record_record_range_select": "You want the recording range to be: (enter a number and press Enter to confirm)", "qs_record_all_display": "Record all displays", "qs_record_single_display": "Record only a single display", "qs_record_full_display": "Record the entire display {width}x{height}", "qs_record_foreground_window": "Record only the foreground active window", "qs_record_which_display": "Please select the single display you want to record: (enter a number and press Enter to confirm)", "qs_record_mode": "Which mode do you want to use for recording? (enter a number and press Enter to confirm)", "qs_record_ffmpeg_help": "Medium resource usage, with a playback delay of up to 15 minutes. Can record computer activities relatively smoothly and completely, and analyze the screen in detail. Does not support recording only the foreground window.", "qs_record_screenshot_array_help": "Low resource usage, can be played back instantly. Suitable for most users who store, recall, and search for memory clues.", "qs_record_mode_set_to": "Recording mode is set to:", "qs_record_auto_set": "The resolution of the display will be automatically recognized each time the screen is recorded, without additional settings.", "qs_ocr_benchmark_testcase": "Test language: {lang}\n{indentation}Test case: {test_set}", "qs_ocr_benchmark_tabletitle": ["OCR engine", "Single image recognition time (sec)", "Accuracy"], "qs_ocr_benchmark_warning": "* Please note that the time taken for each image recognition by {ocr_engines} may exceed the current screenshot interval {screenshot_interval_second}s, which may cause delays in recording. It is recommended to set the screenshot interval to more than twice the single image recognition time if these OCR engines are used. (When using Flexible Screenshot mode)", "qs_et_describe": "Windrecorder also provides some extension functions, which you can later install/use in the extension directory.", "qs_end_describe": "Congratulations! You have completed all initial settings. Don't worry, you can adjust the settings anytime in the app! \n\nNow, you can open [start_app.bat] in the directory to start using it. \n", "qs_end_slogan": "> Capture and preserve the fleeting moments of the wind, as seen through your eyes.", "qs_end_feedback": "> Encountered a problem or have suggestions? Feel free to submit issues and PRs at\n   https://github.com/yuka-friends/Windrecorder", "tray_add_flag_mark_note_for_now": "🚩 Add mark for now", "tray_webui_start": "🦝 Start Search/Setting webui", "tray_webui_exit": "🦝 Stop Search/Settings webui service", "tray_webui_address": "🦝 Browser {address_port} to access webui", "tray_webui_address_network": "      LAN address: {address_port}", "tray_record_start": "▶️ Start Recording", "tray_record_stop": "⏸️ Pause Recording", "tray_update_cta": "🚀 Update to new version: {version}", "tray_version_info": "🚀 Version {version}", "tray_updatelog": "🚀 See what's new", "tray_exit": "❌ Exit", "tray_notify_title": "Windrecorder is recording", "tray_notify_title_record_pause": "Windrecorder has paused recording", "tray_notify_text": "Use the right-click menu on tray to Pause Recording or Rewind Memories", "tray_notify_text_start_without_record": "Use the right-click menu on tray to Start Recording or Rewind Memories", "tray_tip_record": "Windrecorder - Recording", "tray_tip_record_pause": "Windrecorder - Recording Pause", "tray_text_already_run": "Another Windrecorder is running in system tray.", "flag_text_mark_added": "Time mark added.", "flag_input_note": "Note:", "flag_btn_remove_mark": "Remove mark", "flag_btn_add_note": "Add note", "lb_text_generating": "Generating image, please wait...", "lb_text_month_range": "Approximate month range", "lb_text_exact_date": "Exact date range", "lb_text_data_selector": "Date selector", "lb_text_distributeavg": "Evenly distribute from existing data", "lb_text_timeavg": "Distribute by absolute time range", "lb_text_thumbnail_mode_select": "Thumbnail distribution mode", "lb_text_width_thumbnail_num": "Number of horizontal thumbnails", "lb_text_height_thumbnail_num": "Number of vertical thumbnails", "lb_tip_distributeavg": "Contains the total number of thumbnails: {num}. If there are not enough thumbnails in the database, the generation may fail.", "lb_tip_timeavg": "Contains total number of thumbnail slots: {num}.", "lb_text_custom_lightbox_width": "Generate image width (pixels)", "lb_checkbox_add_watermark": "Add bottom watermark to lightbox images", "lb_btn_create_img": "Create image", "lb_text_create_img_instruction": "Generated results can be found in the folder {cache_dir}.", "lb_text_title": "#### 📔 Custom Lightbox Generator", "bg_text_no_image": "No image selected", "bg_text_disable_bg": "Custom background image is disabled", "bg_title": "### 🖼️ Custom webui background image", "bg_text_opacity": "Opacity", "bg_btn_remove_bg": "Remove background image", "bg_btn_set_bg": "Set as background image", "bg_btn_preview": "Preview effect", "bg_text_not_existed": "Custom background image does not exist {custom_background_filepath}, the background image has been disabled."}, "sc": {"main_title": "#### 🦝 捕风记录仪", "tab_name_oneday": "一天之时", "tab_name_search": "全局搜索", "tab_name_stat": "记忆摘要", "tab_name_lab": "实验室", "tab_name_recording": "录制与视频存储", "tab_name_setting": "设置", "text_search_keyword": "关键词搜索", "text_search_daterange": "日期范围", "text_search_not_found": "没有找到 \"{search_content}\"。", "text_apply_changes": "保存并应用所有更改", "utils_toast_setting_saved": "已应用更改。", "footer_info": "最早的数据时间：**{first_record_time_str}**，最新的数据时间： **{latest_record_time_str}**， 数据库记录了行数： **{latest_db_records}**，视频文件占用空间： **{videos_file_size} GB** ({videos_files_count} 个视频文件)", "footer_info_help": "统计更新可能存在最多两天的延迟。", "text_welcome_to_windrecorder": "欢迎使用 Windrecorder！", "text_updating_month_stat": "更新本月统计中……", "text_updating_yearly_stat": "更新本年统计中……", "oneday_title": "### 🖼️ 一天之时", "oneday_btn_yesterday": "← 前一天", "oneday_btn_tomorrow": "后一天 →", "oneday_btn_today": "今天", "oneday_toggle_search": "搜索", "oneday_toggle_search_help": "通过空格分开多个关键词进行检索。不输入任何内容直接回车搜索，可列出当日所有数据。支持同时搜索标题和内容。", "oneday_search_md_none": "<p align='center' style='line-height:2.3;'> ⚠ 没有找到结果 </p>", "oneday_search_md_result": "<p align='center' style='line-height:2.3;'> → 共 {result_num} 条结果：</p>", "oneday_text_generate_timeline_thumbnail": "生成当日时间轴缩略图中，请稍后……", "oneday_md_no_enough_thunmbnail_for_timeline": "<p align='center' style='color:rgba(0,0,0,.3)'> 当日缩略图数量不足以生成时间轴。 </p>", "oneday_md_rewinding_video_name": "`正在回溯 {day_video_file_name}`", "oneday_text_not_found_vid_but_has_data": "磁盘上没有找到这个时间的视频文件，不过有文本数据可被检索。", "oneday_text_data_indexing_wait_and_refresh": "有数据正在被索引中，请稍等几分钟后刷新查看。", "oneday_text_no_found_record_and_vid_on_disk": "磁盘上没有找到这个时间的视频文件和索引记录。", "oneday_text_has_vid_but_not_index": "数据库中没有这一天的数据索引。不过，磁盘上有这一天的视频还未索引，请前往「设置」进行索引、或等待自动索引。→", "oneday_text_vid_and_data_not_found": "没有找到这一天的数据索引和视频文件。", "oneday_text_flag_mark_help": "当想为正在经历的重要会议、突发情况、某场直播、游戏与观影高光时刻……等添加标记、以方便未来回顾时，可以通过托盘菜单为当下添加一个标记 —— 在时间长河中也可以刻舟求剑。", "oneday_text_help_locate_manually": "受限于 streamlit 能力，目前只能手动定位回到该时间点。", "oneday_text_file_damaged": "{day_screenshot_filepath} 文件似乎不完整或者损坏。", "oneday_toggle_flag_mark": "查看时间标记清单", "oneday_btn_add_flag_mark_from_select_time": "为当前定位的时间添加标记", "oneday_btn_flag_mark_save_df": "保存修改", "oneday_wt_text": "屏幕时间", "oneday_wt_help": "统计可能存在较大偏差，仅供参考。", "oneday_ls_title_wintitle": "⏱️ 活动统计", "oneday_ls_title_flag_note": "🚩 所有旗标", "oneday_ls_text_no_wintitle_stat": "<p align='left' style='color:rgba(0,0,0,.3)'> 没有当日页面浏览数据 </p>", "oneday_ls_text_no_wintitle_stat_momnth": "<p align='left' style='color:rgba(0,0,0,.3)'> 没有当月页面浏览数据 </p>", "oneday_ls_text_disable_leftside": "<p align='center' style='color:rgba(0,0,0,.3)'> 工具栏已关闭，可前往设置页开启。 </p>", "gs_md_search_title": "### 🔎 搜索", "gs_input_exclude": "排除", "gs_input_search_help": "可使用空格分隔多个关键词一起搜索，以筛选出同时出现这些关键词的画面。窗口标题名也可以作为关键词。当想搜索连续的整句内容时，可以使用'-'代替空格连接单词，比如搜索 'i-love-you' 而不是 'i love you'。", "gs_input_exclude_help": "排除哪些关键词的内容，留空为不排除。可使用空格分隔多个关键词。", "gs_text_pls_choose_full_date_range": "请选择完整的时间范围", "gs_input_result_page": "结果页数", "gs_md_search_result_stat": "搜索到 {all_result_counts} 条、共 {max_page_count} 页关于 \"{search_content}\" 的结果。", "gs_md_synonyms_recommend": "试试搜索：{synonyms_recommend}", "gs_md_search_result_below": "`执行耗时：{timecost}s`", "gs_text_intro": "这里是全局搜索页，可以搜索到迄今记录的所有内容。输入关键词后回车即可搜索。", "gs_slider_to_rewind_result": "拖动回溯搜索结果", "gs_text_randomwalk": "随便走走", "gs_text_video_file_not_on_disk": "磁盘上没有找到 **{df_videofile_name}**", "gs_option_ocr_text_search_month_range": "文本搜索（月范围）", "gs_option_ocr_text_search_exact_date": "文本搜索（精确日期）", "gs_option_img_emb_search": "图像语义搜索", "gs_option_similar_img_search": "以图搜图", "gs_input_img_emb_search": "使用自然语言描述图像", "gs_text_img_emb_help": "用自然语言描述画面内容，描述越精确、画面结果将越接近。此处支持中、英、日、韩等多达 21 种语言输入（详细 uform 文档）。视频需要被嵌入索引后才能搜索得到，详见设置页设置项说明。", "gs_text_searching": "搜索中，请稍后……", "gs_text_loading_embed_model": "加载嵌入模型中，请稍后……", "gs_text_upload_img": "选择图片", "gs_text_upload_img_help": "嵌入模型倾向根据图像内容语义进行搜索，而非匹配图像本身特征。", "gs_text_not_install_img_emb": "未启用或未安装图像语义检索模块，请前往设置页启用。若设置中无相关选项，请先安装图像语义模块。安装脚本位于 Windrecorder 目录下：extension\\install_img_embedding_module\\install_img_embedding_module.bat", "stat_md_month_title": "### 🌖 当月数据统计", "stat_md_year_title": "### 🎏 {stat_year_title} 记录", "stat_md_memory_title": "### 🧩 记忆摘要", "stat_btn_generate_update_word_cloud": "生成/更新本月词云", "stat_text_generating_word_cloud": "生成中，大概需要 30s……", "stat_text_no_month_word_cloud_pic": "当月未有词云图片。", "stat_btn_generate_lightbox": "生成/更新本月的光箱", "stat_text_generating_lightbox": "生成中，大概需要 5s……", "stat_text_no_month_lightbox": "当月未有光箱图片。", "stat_text_counting": "统计中……", "stat_text_wintitle_keyword_filter": "关键词过滤", "stat_text_wintitle_filter_help": "通过空格分开多个关键词进行检索，关键词搜索时会忽略大小写。留空列出当月所有数据。", "stat_btn_generate_update_ai_poem": "生成本月诗歌", "stat_text_generating_ai_poem": "生成中，每日生成时间大概需要 5~15s（视语言模型速度而定）……", "stat_text_custom_lightbox": "你可以使用 `extension\\create_custom_lightbox_thumbnail_image` 创建自定义光箱图片", "lab_title": "### ⚗️ 实验室特性", "lab_title_image_semantic_index": "#### 图像嵌入索引", "lab_tip_image_semantic_index_not_install": "此项功能可以让你通过文本描述、以图搜图来搜寻对应的画面。\n为了使用图像语义搜索，需要先安装位于 extension/install_img_embedding_module 的扩展模块。", "lab_title_llm_features": "#### 语言模型", "lab_text_config_llm_api": "配置语言模型服务 API", "lab_tip_llm_api_endpoint": "为了使用依赖语言模型的功能，你需要先配置 API 信息。如果对此不了解，请参见[这篇入门指南](https://github.com/yuka-friends/Windrecorder/blob/main/__assets__/how_to_set_LLM_api_endpoint_sc.md)。", "lab_selectbox_api_endpoint": "API 端点模式", "lab_help_api_endpoint": "API端点是一种请求格式类型，不意味只能用其字面上的服务商。比如有许多云模型提供商、本地模型服务都支持 OpenAI 格式的API端点。", "lab_btn_test": "测试 API 连通性", "lab_text_testing": "连接中……", "lab_prompt_test": "你好，我是一只小浣熊。", "lab_text_test_res_success": "API 连接成功：", "lab_text_test_res_fail": "API 连接失败：", "lab_checkbox_extract_tag": "生成每日活动标签", "lab_input_wintitle_num": "提交的活动标题数量：", "lab_help_extract_tag": "使用 AI 总结每日活动的标签，只有屏幕时间内的标题内容会被发送到LLM。通过删除 '{ai_extract_tag_result_dir_ud}' 中的json内数据，可以重新生成。取决于不同 LLM 服务提供方的政策，部分包含色情、政治等内容可能会生成失败。在电脑空闲时，会自动生成之前天的标签以便回顾。", "lab_checkbox_day_poem": "为每日活动写一句诗", "lab_help_day_poem": "根据总结的标签写一句诗歌。通过删除 '{ai_day_poem_result_dir_ud}' 中的json内数据，可以重新生成。在电脑空闲时，会自动生成之前天的诗歌。", "lab_text_tag_filter_words": "在提交给语言模型前，去除以下敏感词语：", "lab_help_tag_filter_words": "对于有些 LLM 服务与模型，部分包含色情、政治等敏感审计内容可能会导致生成失败。通过设置一个过滤列表，可以在提交到模型前自动移除相关词语。", "lab_btn_generate_tags": "生成标签", "lab_text_generating_tags": "生成中... \n取决于语言模型，这可能需要 5~20 秒。", "lab_text_generating_tags_fail": "生成标签失败: {plain_text}", "rs_text_need_to_restart_after_save_setting": "本页的设置在保存后，需重启 「捕风记录仪」 才能生效。", "rs_md_title": "### 📼 录制与视频存储", "rs_md_record_setting_title": "#### 录制选项", "rs_checkbox_start_record_when_startup": "开机后自动启动应用", "rs_checkbox_start_record_when_startup_help": "此项勾选后会为「start_app.bat」创建快捷方式，并放到系统开机自启动的目录下。此行为可能会被部分安全软件误判为病毒行为，导致「start_webui.bat」被移除，如有拦截，请将其移出隔离区并标记为可信任软件。或手动为「start_app.bat」创建快捷方式、并放到系统的开机启动目录下。", "rs_checkbox_is_start_recording_on_start_app": "启动应用后自动开始录制", "rs_input_stop_recording_when_screen_freeze": "当画面几分钟没有变化时，暂停录制下个视频切片（0为永不暂停）", "rs_text_skip_recording_by_wintitle": "当前台应用标题/ OCR 包含以下词语之一时，跳过录制片段，且不索引、展示：", "rs_tag_input_tip": "回车添加", "rs_text_ocr_manual_update": "不自动索引，我会在需要时手动更新", "rs_text_ocr_auto_update": "视频切片录制完毕时自动索引（推荐）", "rs_selectbox_ocr_strategy": "OCR 索引策略", "rs_input_vid_store_time_help": "0 为永不删除。即使视频文件已被清理，已索引的数据仍会保留且可以被检索，只是缺少画面。", "rs_input_vid_compress_time": "原视频保留几天后进行压缩", "rs_input_vid_compress_time_help": "0 为永不压缩。", "rs_selectbox_compress_ratio": "压缩到原先画面尺寸的", "rs_selectbox_compress_ratio_help": "0.75 倍的边缩放约可压缩到原视频体积的 1/4，0.5 倍为 1/8，0.25 倍为 1/16。根据视频具体内容不同，压缩比可能有所差异。", "rs_checkbox_enable_half_res_while_hidpi": "对于「高 DPI /高分辨率屏幕」，在录制时缩放至四分之一的分辨率", "rs_text_compress_encoder": "编码方式", "rs_text_compress_accelerator": "编码加速器", "rs_text_compress_CRF": "编码质量 CRF", "rs_text_compress_CRF_help": "CRF 是 Constant Rate Factor 的缩写，用于设置视频编码的质量和比特率控制。Windrecorder 为了较高的压缩率，默认设定在 39。在 ffmpeg 中，CRF 的取值范围取决于所使用的编码器。对于 x264 编码器，CRF 的取值范围是 0 到 51，其中 0 表示无损，23 是默认值，51 表示最差的质量。较低的值意味着更高的质量，但会导致更大的文件大小。通常情况下，x264 编码器的合理取值范围是 18 到 28。对于 x265 编码器，默认的 CRF 值是 28。而对于 libvpx 编码器，CRF 的取值范围是 0 到 63。总的来说，CRF 值越低，视频质量越高，但文件大小也会相应增加。", "rs_text_compress_cpu_threads": "用于编码的 CPU 线程数", "rs_text_compress_cpu_threads_help": "更高的值可能会提高压缩速度但增加 CPU 使用率。默认使用可用 CPU 核心数的四分之一。", "rs_text_estimate_hint": "每15分钟录制视频大小估计：{min}Mb ~ {max}Mb，视具体录制内容与显示器数量决定。", "rs_btn_encode_benchmark": "测试支持的编码方式 ♘", "rs_text_encode_benchmark_loading": "测试中，大概需要 1 分钟……", "rs_text_support": "支持", "rs_text_compress_ratio": "压缩率❓", "rs_text_compress_ratio_help": "压缩率 = 压缩后的视频文件体积 / 原视频文件体积。此处测试文件时长较短，该项指标呈现可能存在较大偏差。随着视频时间增长，压缩率相对会更高。你可以替换 __assets__\\test_video_compress.mp4 文件来进行测试。", "rs_text_compress_time": "压缩耗时（s）", "rs_text_record_strategy_option_all": "录制所有显示器（共 {num} 个）", "rs_text_record_strategy_option_single": "仅录制一个显示器", "rs_text_record_range": "画面录制范围", "rs_text_record_single_display_select": "仅录制显示器：", "rs_text_show_encode_option": "高级编码选项", "rs_text_record_encoder": "录制编码器", "rs_text_record_help": "选择 h265/AV1 可以在同等体积下获得更好的画质，但在录制与回放时可能性能较差。（若支持，将会自动启用硬件加速）", "rs_text_record_bitrate": "录制比特率（kbps）", "rs_text_bitrate_help": "视频比特率是指视频数据的传输速度或处理速度，会影响视频的画质和文件的大小。具体来说，比特率越高，视频的画质越好，但相应的文件大小也会越大。相反，如果比特率较低，则即使在同样的分辨率下，视频的画质可能就会出现模糊或者失真。为了将画面保持在较小体积，默认为 200kbps。", "rs_text_hevc_tips": "在 Microsoft Edge / Firefox 等浏览器上回放 HEVC (h265) 视频，可能需要先安装 HEVC 视频扩展：https://apps.microsoft.com/detail/9NMZLZ57R3T7", "rs_text_record_mode_option_ffmpeg": "直接录制视频（使用 FFmpeg）", "rs_text_record_mode_option_screenshot_array": "自动灵活截图（使用 MSS）", "rs_text_record_mode": "录制模式", "rs_input_screenshot_interval_second": "截图分析时间间隔（秒）", "rs_checkbox_record_screenshot_method_capture_foreground_window_only": "只截图前台活动窗口", "rs_text_screenshot_interval_second_help": "为了保证实时分析性能，截图间隔需要大于每帧计算分析占用时间，因此限定在了 3 秒以上。", "rs_text_ffmpeg_help": "- 适用于想完整记录电脑活动视频、性能较高的用户；\n- 索引时可以跳过自定义窗口与内容，但相关画面仍可能被录制、只是无法被检索到；\n- 录制时将占用较高的内存，在录制完成索引时可能占用较多系统资源；\n- 回溯存在最多15分钟的延迟；", "rs_text_screenshot_array_help": "- 适用于存储、回忆、搜索记忆线索的大多数用户；\n- 占用低系统资源，实时分析、只索引存在变化的画面；\n- 可以马上回溯已记录画面；截图完毕会自动转换为视频；\n- 可以精确跳过自定义窗口与内容，同时可以仅录制前台窗口；", "rs_checkbox_is_record_system_sound": "同时录制系统声音", "rs_checkbox_record_deep_linking": "记录浏览器当前页面链接", "rs_help_record_deep_linking": "支持自动记录 Chrome、Microsoft Edge、Firefox 当前浏览的页面地址。如果感到浏览器浏览卡顿，请尝试关闭此选项。", "rs_text_energy_saving": "什么时候将截图合成为视频", "rs_text_energy_saving_help": "为了保证储存与回放体验，建议仅当电脑性能不足、感到程序高占用导致电脑卡顿时，调整至仅在空闲时合成视频。如果积压了位于`cache_screenshot`的许多截图缓存未能来得及合成为视频，可以通过 `extension/manually_convert_screenshot_cache_into_video` 中的脚本手动合成。", "rs_option_energy_saving_instantly": "在片段录制完时立即合成", "rs_option_energy_saving_plug": "立即合成，但仅在插入电源时（仅笔记本电脑适用）", "rs_option_energy_saving_idle": "仅在电脑空闲、且接入电源时", "set_md_title": "### ⚙️ 设置", "set_md_index_db": "#### 数据库索引\n", "set_checkbox_shutdown_after_updated": "更新完毕后关闭计算机", "set_selectbox_local_ocr_engine": "本地 OCR 引擎", "set_selectbox_ocr_lang": "主要的 OCR 语言", "set_help_ocr_lang_windows_ocr_engine": "可以在 系统设置-语言 中添加语言包来添加对应 OCR 语言支持。", "set_help_ocr_lang_third_party_engine": "部分第三方 OCR 引擎可能不支持自定义主要语言。", "set_selectbox_local_ocr_engine_help": "推荐使用 Windows 自带的 Windows.Media.Ocr.Cli 进行 OCR，速度更快、性能占用更低。你可以在 extension 目录下根据需要安装三方的 OCR 引擎，它们可能具备更高的准确率，但通常会占用更高系统资源、使用更长时间识别。", "set_md_ocr_ignore_area": "**OCR 时忽略屏幕四边的区域范围**", "set_md_ocr_ignore_area_help": "填入数字为百分比，比如'6' == 6%。此选项可以在 OCR 时忽略屏幕四边的元素，如浏览器的标签栏、Windows 的开始菜单、网页内的花边广告信息等。当仅录制前台窗口时，此选项会依照比例遮罩每个窗口。", "set_toggle_use_screenshot_as_refer": "用当前屏幕截图参照", "set_text_choose_displays": "选择显示器", "set_text_top_padding": "上边框", "set_text_bottom_padding": "下边框", "set_text_left_padding": "左边框", "set_text_right_padding": "右边框", "set_checkbox_enable_3_columns_in_oneday": "「一天之时」使用三栏居中布局", "set_help_enable_3_columns_in_oneday": "三栏布局适合较大的 webui 显示设备，可以提供更多信息、让回溯画面居中。默认双栏布局适合较小的 webui 显示设备。", "set_checkbox_use_similar_zh_char_to_search": "使用中文形近字进行搜索", "set_checkbox_use_similar_zh_char_to_search_help": "OCR 可能会误认相似字，开启此项会同时搜索和关键词相近的字形，以提高结果的命中率。在某些情况下，会降低搜索性能、让结果噪音增加。", "set_input_wordcloud_filter": "在词云生成中过滤以下词语：", "set_input_wordcloud_filter_help": "有一些 UI 的词语会常驻显示，从而在数据中出现频次更高，这里可以将它们排除在词云的统计范围之外。", "set_input_oneday_timeline_thumbnail_num": "「一天之时」时间轴的横向缩略图数量", "set_input_oneday_timeline_thumbnail_num_help": "如果希望「一天之时」时间轴缩略图密度更高，可以提高该值。", "set_text_video_not_index": "有 {nocred_count} 个视频等待索引，预计用时 <{timeCostStr}。 (磁盘上有 {count} 个文件。)", "set_text_no_video_need_index": "暂时没有新视频需要索引啦。  (磁盘上有 {count} 个文件。)", "set_text_one_video_to_index": "有 1 个视频正在录制中，暂时没有新视频需要索引啦。  (磁盘上有 {count} 个文件。)", "set_text_some_video_will_be_index": "有 {nocred_count} 个视频将会被自动索引。 (磁盘上有 {count} 个文件。)", "set_btn_update_db_manual": "✦ 手动更新数据库", "set_text_updating_db": "更新数据库中，请不要操作或刷新页面。预估所需时间：{estimate_time_str}", "set_text_db_updated_successful": "数据库更新成功！用时： {timeCostStr}", "set_btn_got_it": "我知道了", "set_md_gui": "#### 界面\n", "set_md_auto_maintain": "#### 自动化维护", "set_input_video_hold_days": "视频文件保留天数", "set_input_max_num_search_page": "全局搜索中，每页显示结果的最大数量", "set_update_latest": "当前已是最新版本。", "set_update_checking": "检查更新中……", "set_update_new": "\n💫 检查到有新版本 {tool_version}，请打开目录下的`install_update.bat` 或通过托盘菜单来更新。", "set_update_changelog": "\n [更新日志](https://github.com/yuka-friends/Windrecorder/blob/main/CHANGELOG.md)", "set_update_fail": "无法检查更新。{e}", "set_pwd_text": "<PERSON>ui 访问密码（留空则不启用）", "set_pwd_help": "启用此项设置后，会在访问 webui 时要求提供密码。此项设置不会加密你的数据，仅保护 webui 的使用入口，以避免同局域网内陌生用户访问。", "set_pwd_forget_help": "忘记密码？请将 config_user.json 中的 webui_access_password_md5 项删除重置。", "set_checkbox_enable_img_emb": "启用图像语义检索", "set_text_enable_img_emb_help": "图像语义检索是一种通过计算机视觉技术、基于图像的语义内容进行图像检索的方法。它可以做到根据对图像的内容语义描述，从大规模的图像数据库中检索出查询出相关的图像。Windrecorder 使用 uform3-image-text-multilingual-base 来嵌入索引，能力可能不如预期。开启该选项后，程序将在空闲时间对视频建立图像嵌入索引，之后便能对此进行全局搜索。你也可以通过程序目录下的 extension/index_img_embedding_for_all_videofiles 脚本手动索引已有的视频文件。", "set_text_img_emb_not_suppport_cuda": "你的设备似乎不支持 CUDA，在使用 CPU 对图像语义索引时可能性能较低。", "set_input_img_emb_max_recall_count": "自然语义搜索中，从每个数据库召回的结果数", "set_text_help_img_emb_max_recall_count": "自然语义搜索时，会分别从每月数据库中召回指定数量结果，过高或过低的数量可能导致查询准确率降低。如果结果不准确，请尝试调整该项。", "set_input_day_begin_minutes": "「一天之时」分隔每日的时间点", "set_help_day_begin_minutes": "可以把凌晨后的一段时间记录划分到同个自然天中，而非以零点来划分每天。", "set_checkbox_synonyms_recommand": "搜索时推荐近义词", "set_help_synonyms_recommand": "若语言不支持将不启用，在 config_src\\synonyms 下查看已支持语言。", "set_checkbox_reduce_same_content_at_different_time": "当视频片段内有相同内容显示在不同时间点，只记录第一次出现时间点。", "set_checkbox_recycle_deleted_files": "当需要删除文件时，将其放到回收站、而非直接删除。", "set_help_recycle_deleted_files": "捕风记录仪在运行时产生临时视频或截图。为了数据安全，我们默认将废弃数据放到回收站、而非直接删除，由系统回收站定期自动清理。如果你的磁盘容量较低、或希望直接删除废弃数据，请关闭此选项。所有文件删除操作将记录在 cache\\logs 中。", "qs_header_1": "欢迎使用 捕风记录仪 {__version__}", "qs_header_2": "感谢下载使用！本向导将协助你完成基础配置项。不用担心，所有选项之后都可以再次调整。", "qs_la_text_same_as_previous": "界面语言保持与先前一致的：简体中文", "qs_un_set_your_username": "设定你的用户名，以作为数据库标识。", "qs_un_describe": "回车直接使用当前用户名作为用户标识：「{sys_username}」，也可以输入自定义用户名（20个字符以内）。", "qs_un_longer_than_expect": "超出限定长度（20个半角英文字符/10个全角中文字符）", "qs_un_use_current_name": "使用当前用户名：{sys_username}", "qs_un_use_custom_name": "使用自定义用户名：{your_username}", "qs_olang_intro": "系统已安装以下语言，选择一项作为 OCR 检测的主要语言：", "qs_olang_ocrlang_set_to": "OCR 检测的主要语言已设定为：", "qs_olang_error": "输入项应当为数字且在选项范围内。", "qs_olang_one_choice_default_set": "系统语言为 {os_support_lang}，已应用为 OCR 检测的主要语言。\n（不是所需语言？请检查系统是否安装了选定语言的输入法/语言包。\n（https://learn.microsoft.com/en-us/uwp/api/windows.media.ocr）", "qs_ocr_title": "OCR 引擎测试情况：\n", "qs_ocr_result_describe": "准确率：{accuracy}，识别时间：{timecost} ，索引15分钟视频约用时：{timecost_15}", "qs_ocr_describe": "> 推荐使用系统自带的 Windows.Media.Ocr.Cli，具有更快的速度、更低的性能消耗。\n", "qs_ocr_describe_disable_clo": "> chineseocr_lite_onnx 由于兼容性问题已禁用。\n（通过修改 user_config.json中'enable_ocr_chineseocr_lite_onnx':true 启用。）", "qs_ocr_tips_low_accuracy": "！准确率过低？请检查系统是否安装了选定语言的输入法/语言包。\n（https://learn.microsoft.com/en-us/uwp/api/windows.media.ocr）", "qs_ocr_cta": "请选择提取屏幕内容时的 OCR 引擎：", "qs_ocr_option_recommand": "（推荐 & 默认）", "qs_ocr_engine_chosen": "OCR 引擎使用：", "qs_ocr_not_has_lang_test_support": "目前选择的语言没有找到合适的测试案例，将以 en-US 为基准进行测试。不过别担心，该OCR仍然能正常工作识别你的语言，只是向导缺少了合适测试，可能识别率不能反映实际情况。", "qs_record_current_record": "当前录制设置：", "qs_record_record_range_select": "你希望录制范围为：（输入数字项后回车确认；直接回车沿用当前设置）", "qs_record_all_display": "录制所有显示器", "qs_record_single_display": "仅录制单个显示器", "qs_record_full_display": "录制整个显示器 {width}x{height}", "qs_record_foreground_window": "仅录制前台活动窗口", "qs_record_which_display": "请选择想录制的单个显示器：（输入数字项后回车确认）", "qs_record_mode": "你希望使用哪种方式进行录制？（输入数字项后回车确认）", "qs_record_ffmpeg_help": "占用资源中等，存在最多15分钟的回放延迟。可以比较流畅完整地记录电脑活动、细致分析屏幕画面。不支持仅录制前台窗口。", "qs_record_screenshot_array_help": "占用资源低，可以即时回放。适合大多数存储、回忆、搜索记忆线索的用户。", "qs_record_mode_set_to": "录制模式设置为：", "qs_record_auto_set": "显示器的分辨率将在每次录屏时自动识别，无需额外设定。", "qs_ocr_benchmark_testcase": "测试语言：{lang}\n{indentation}测试用例：{test_set}", "qs_ocr_benchmark_tabletitle": ["OCR 引擎", "单图识别用时（秒）", "准确率"], "qs_ocr_benchmark_warning": "* 请注意，{ocr_engines} 的每单图识别用时可能会超过当前屏幕截图间隔时间 {screenshot_interval_second}s，这可能会导致记录出现延迟。如果使用这些识别引擎，建议将截图间隔时长设定为单图识别时间的两倍以上。（在使用 灵活截图模式 时）", "qs_et_describe": "捕风记录仪 还提供了一些扩展功能，你可以稍后在 extension 目录下安装/使用。", "qs_end_describe": "恭喜！你已完成所有初始设定。别担心，你可以随时在应用内调整设置！\n\n现在，你可以打开目录下的 【start_app.bat】 来开始使用啦。\n", "qs_end_slogan": "> 一起捕捉贮藏风一般掠过的、你的目之所见。", "qs_end_feedback": "> 遇到问题、想反馈建议？欢迎提交 issue 与 PR: \n   https://github.com/yuka-friends/Windrecorder", "tray_add_flag_mark_note_for_now": "🚩 为现在时间添加标记", "tray_webui_start": "🦝 启动搜索/设置界面", "tray_webui_exit": "🦝 关闭搜索/设置界面", "tray_webui_address": "🦝 浏览器访问 {address_port} 进入页面", "tray_webui_address_network": "      局域网访问：{address_port}", "tray_record_start": "▶️ 开始记录", "tray_record_stop": "⏸️ 暂停记录", "tray_update_cta": "🚀 更新到新版本：{version}", "tray_version_info": "🚀 当前已是最新版 {version}", "tray_updatelog": "🚀 查看更新日志", "tray_exit": "❌ 退出", "tray_notify_title": "捕风记录仪 正在记录", "tray_notify_title_record_pause": "捕风记录仪 已暂停录制", "tray_notify_text": "通过托盘右键菜单来「暂停记录」与「搜索回忆」", "tray_notify_text_start_without_record": "通过托盘右键菜单来「开始记录」与「搜索回忆」", "tray_tip_record": "捕风记录仪 - 正在记录", "tray_tip_record_pause": "捕风记录仪 - 暂停记录", "tray_text_already_run": "「捕风记录仪」已在系统托盘中运行。", "flag_text_mark_added": "已添加此时的标记。", "flag_input_note": "备注：", "flag_btn_remove_mark": "移除标记", "flag_btn_add_note": "添加备注", "lb_text_generating": "正在生成图片，请稍等", "lb_text_month_range": "大致月份范围", "lb_text_exact_date": "精确日期范围", "lb_text_data_selector": "日期选择器", "lb_text_distributeavg": "从已有数据中平均分布", "lb_text_timeavg": "按绝对时间范围分布", "lb_text_thumbnail_mode_select": "缩略图分布模式", "lb_text_width_thumbnail_num": "横向缩略图数量", "lb_text_height_thumbnail_num": "纵向缩略图数量", "lb_tip_distributeavg": "包含缩略图总数：{num}。如果数据库内缩略图数量不足，可能会生成失败。", "lb_tip_timeavg": "包含缩略图格子总数：{num}。", "lb_text_custom_lightbox_width": "生成图像宽度（像素）", "lb_checkbox_add_watermark": "为光箱图片添加底部水印", "lb_btn_create_img": "创建图片", "lb_text_create_img_instruction": "生成结果可以在文件夹 {cache_dir} 下找到。", "lb_text_title": "#### 📔 自定义光箱生成器", "bg_text_no_image": "没有选择图片", "bg_text_disable_bg": "已关闭自定义背景图", "bg_title": "### 🖼️ 自定义 webui 背景图片", "bg_text_opacity": "不透明度", "bg_btn_remove_bg": "移除背景图片", "bg_btn_set_bg": "设置为背景图片", "bg_btn_preview": "预览效果", "bg_text_not_existed": "自定义背景图不存在 {custom_background_filepath}，已停用背景图。"}, "ja": {"main_title": "#### 🦝 Windrecorder", "tab_name_oneday": "一日", "tab_name_search": "グローバルキーワード検索", "tab_name_stat": "メモリサマリー", "tab_name_lab": "ラボ", "tab_name_recording": "録画とビデオストレージ", "tab_name_setting": "設定", "text_search_keyword": "キーワード検索", "text_search_daterange": "日付範囲", "text_search_not_found": "\"{search_content}\" が見つかりませんでした。", "text_apply_changes": "すべての変更を保存して適用する", "utils_toast_setting_saved": "変更が適用されました。", "footer_info": "最も早いデータの時間：**{first_record_time_str}** , 最新のデータ時間： **{latest_record_time_str}** ,データベースのレコード数： **{latest_db_records}** ,ビデオファイルの使用スペース： **{videos_file_size} GB** ({videos_files_count} 個のビデオファイル)", "footer_info_help": "統計情報の更新には最大 2 日の遅れが生じる可能性があります。", "text_welcome_to_windrecorder": "Windrecorderへようこそ！", "text_updating_month_stat": "今月の統計を更新しています...", "text_updating_yearly_stat": "今年の統計を更新しています...", "oneday_title": "### 🖼️ 一日", "oneday_btn_yesterday": "← 前の日", "oneday_btn_tomorrow": "次の日 →", "oneday_btn_today": "今日", "oneday_toggle_search": "検索", "oneday_toggle_search_help": "スペースで区切られた複数のキーワードの検索ができます。何も入力しないで Enter キーを押すと、当日のすべてのデータが表示されます。タイトルとコンテンツの同時検索をサポートします。", "oneday_search_md_none": "<p align='center' style='line-height:2.3;'> ⚠ 結果が見つかりません </p>", "oneday_search_md_result": "<p align='center' style='line-height:2.3;'> → 合計 {result_num} 件の結果：</p>", "oneday_text_generate_timeline_thumbnail": "当日のタイムラインサムネイルを生成中です。しばらくお待ちください...", "oneday_md_no_enough_thunmbnail_for_timeline": "<p align='center' style='color:rgba(0,0,0,.3)'> その日のサムネイル数がタイムラインを生成するのに十分ではありません。 </p>", "oneday_md_rewinding_video_name": "`{day_video_file_name} を巻き戻しています`", "oneday_text_not_found_vid_but_has_data": "この時間のビデオファイルがディスク上に見つかりませんでしたが、検索可能なテキストデータがあります。", "oneday_text_data_indexing_wait_and_refresh": "データがインデックス中です。数分待ってから更新してください。", "oneday_text_no_found_record_and_vid_on_disk": "この時間のビデオファイルおよびインデックスレコードがディスク上に見つかりませんでした。", "oneday_text_has_vid_but_not_index": "この日のデータインデックスがデータベースにありません。しかし、インデックスされていないその日のビデオがディスクにあります。設定ページに移動してインデックスを実行してください。→", "oneday_text_vid_and_data_not_found": "データインデックスとビデオファイルがその日見つかりませんでした。", "oneday_text_flag_mark_help": "重要な会議、緊急事態、特定の生放送、ゲームや映画の視聴ハイライトの瞬間などにマークを追加して、将来の見直しを容易にしたい場合、トレイメニューから現在の瞬間にマークを追加できます。時の川、船を彫って剣を求めることもできます。", "oneday_text_help_locate_manually": "ストリームライト機能によって制限されているため、現在、この時点まで遡って手動で検索することしかできません。", "oneday_text_file_damagged": "ファイル {day_screenshot_filepath} は不完全か破損しているようです。", "oneday_toggle_flag_mark": "タイムマークリスト", "oneday_btn_add_flag_mark_from_select_time": "現在位置している時間にマークを追加します", "oneday_btn_flag_mark_save_df": "変更を保存", "oneday_wt_text": "スクリーンタイム", "oneday_wt_help": "統計には大きな偏差がある可能性があり、参照のみを目的としています。", "oneday_ls_title_wintitle": "⏱️ アクティビティ統計", "oneday_ls_title_flag_note": "🚩 すべてのフラグ", "oneday_ls_text_no_wintitle_stat": "<p align='left' style='color:rgba(0,0,0,.3)'> その日のページビュー データはありません </p>", "oneday_ls_text_no_wintitle_stat_momnth": "<p align='left' style='color:rgba(0,0,0,.3)'> その月のページビュー データはありません </p>", "oneday_ls_text_disable_leftside": "<p align='center' style='color:rgba(0,0,0,.3)'> ツールバーは閉じています。ツールバーを開くには、設定ページに移動できます。</p> ", "gs_md_search_title": "### 🔎 検索", "gs_input_exclude": "除外", "gs_input_search_help": "スペースを使用して複数のキーワードを区切り、まとめて検索して、これらのキーワードが同時に表示される画面を除外できます。ウィンドウタイトル名もキーワードとして使用できます。連続した文を検索する場合は、スペースの代わりに「-」を使用して単語を接続できます。たとえば、「i love you」の代わりに「i-love-you」を検索します。", "gs_input_exclude_help": "どのキーワードのコンテンツを除外するか。空白のままにすると除外されません。複数のキーワードをスペースで区切って指定できます。", "gs_text_pls_choose_full_date_range": "完全な日付範囲を選択してください", "gs_input_result_page": "結果ページ数", "gs_md_search_result_stat": "{all_result_counts} 件の \"{search_content}\" の結果が見つかりました。最大 {max_page_count} ページ。", "gs_md_synonyms_recommend": "検索してみてください: {synonyms_recommend}", "gs_md_search_result_below": "`実行時間：{timecost}s`", "gs_text_intro": "これはグローバル検索ページです。これまでに記録されたすべてのコンテンツを検索できます。キーワードを入力して Enter キーを押すと検索が開始されます。", "gs_slider_to_rewind_result": "検索結果を巻き戻す", "gs_text_randomwalk": "散歩する", "gs_text_video_file_not_on_disk": "**{df_videofile_name}** がディスク上に見つかりません", "gs_option_ocr_text_search_month_range": "テキスト検索（月範囲）", "gs_option_ocr_text_search_exact_date": "テキスト検索 (正確な日付)", "gs_option_img_emb_search": "画像セマンティック検索", "gs_option_similar_img_search": "画像を画像で検索", "gs_input_img_emb_search": "自然言語を使用して画像を説明します", "gs_text_img_emb_help": "画面の内容を自然言語で説明します。説明が正確であればあるほど、結果はより正確になります。ここでは、中国語、英語、日本語、韓国語など、最大 21 の言語がサポートされています(uform ドキュメントの詳細)。ビデオはインデックス作成後に埋め込む必要があります。検索のみで、詳細については設定ページの設定項目の説明を参照してください。", "gs_text_searching": "検索中です、お待ちください...", "gs_text_loading_embed_model": "テキスト埋め込みモデルを読み込み中...", "gs_text_upload_img": "画像を選択", "gs_text_upload_img_help": "埋め込みモデルは、画像自体の特性と一致させるのではなく、画像コンテンツのセマンティクスに基づいて検索する傾向があります。", "gs_text_not_install_img_emb": "イメージ セマンティック取得モジュールが有効になっていない、またはインストールされていません。設定ページに移動して有効にしてください。設定に関連するオプションがない場合は、最初にイメージ セマンティック モジュールをインストールしてください。インストール スクリプトは次の場所にあります。 Windrecorder ディレクトリ: extension\\install_img_embedding_module\\install_img_embedding_module.bat", "stat_md_month_title": "### 🌖 今月のデータ統計", "stat_md_year_title": "### 🎏 {stat_year_title} の記録", "stat_md_memory_title": "### 🧩 メモリの要約", "stat_btn_generate_update_word_cloud": "今月のワードクラウドを生成/更新", "stat_text_generating_word_cloud": "生成中です。約 30 秒かかります...", "stat_text_no_month_word_cloud_pic": "今月はワードクラウド画像がありません。", "stat_btn_generate_lightbox": "今月のライトボックスを生成/更新", "stat_text_generating_lightbox": "生成中です。約 5秒かかります...", "stat_text_no_month_lightbox": "今月はライトボックス画像がありません。", "stat_text_counting": "統計...", "stat_text_wintitle_keyword_filter": "キーワード フィルター", "stat_text_wintitle_filter_help": "キーワードを検索する場合、複数のキーワードをスペースで区切って検索します。 当月のすべてのデータをリストするには、空白のままにします。", "stat_btn_generate_update_ai_poem": "今月の詩を生成", "stat_text_generated_ai_poem": "生成中、毎日の生成時間は約 5 ～ 15 秒かかります (言語モデルの速度に応じて)...", "stat_text_custom_lightbox": "`extension\\create_custom_lightbox_thumbnail_image` を使用してカスタム ライトボックス画像を作成できます", "lab_title": "### ⚗️ 研究室の機能", "lab_title_image_semantic_index": "#### 画像埋め込みインデックス", "lab_tip_image_semantic_index_not_install": "この機能を使用すると、テキストの説明と画像検索を通じて、対応する画面を検索できます。\n画像セマンティック検索を使用するには、最初に extension/install_img_embedding_module にある拡張モジュールをインストールする必要があります。", "lab_title_llm_features": "#### 言語モデル", "lab_text_config_llm_api": "構成言語モデル サービス API", "lab_tip_llm_api_endpoint": "言語モデルに依存した関数を使用するには、まず API 情報を設定する必要があります。これについてわからない場合は、[このスタートガイド](https://github.com/yuka-friends/Windrecorder/blob/main/__assets__/how_to_set_LLM_api_endpoint_en.md) を参照してください。", "lab_selectbox_api_endpoint": "API エンドポイント モード", "lab_help_api_endpoint": "API エンドポイントはリクエスト形式のタイプですが、その文字通りのサービス プロバイダーのみを使用できるという意味ではありません。たとえば、多くのクラウド モデル プロバイダーとローカル モデル サービスは OpenAI 形式の API エンドポイントをサポートしています。", "lab_btn_test": "API 接続をテスト", "lab_text_testing": "接続中...", "lab_prompt_test": "こんにちは、私は小さなアライグマです。", "lab_text_test_res_success": "API 接続成功: ", "lab_text_test_res_fail": "API 接続に失敗しました: ", "lab_checkbox_extract_tag": "毎日のアクティビティ タグを生成", "lab_input_wintitle_num": "送信されたアクティブなタイトルの数:", "lab_help_extract_tag": "AI を使用して毎日のアクティビティ タグを要約すると、利用時間内のタイトル コンテンツのみが LLM に送信されます。'{ai_extract_tag_result_dir_ud}' 内の json データを削除することで、再生成できます。LLM サービス プロバイダーによって異なります。党の方針により、ポルノ、政治などを含む一部のコンテンツは生成されない可能性があります。」コンピューターがアイドル状態の場合、確認用に前日のラベルが自動的に生成されます。", "lab_checkbox_day_poem": "毎日のアクティビティの詩を書きます", "lab_help_day_poem": "要約されたタグに基づいて詩を書きます。「{ai_day_poem_result_dir_ud}」内の json データを削除すると、再生成できます。コンピューターがアイドル状態の場合、前日の詩が自動的に生成されます。", "lab_text_tag_filter_words": "言語モデルに送信する前に、次の機密用語を削除してください:", "lab_help_tag_filter_words": "一部の LLM サービスとモデルでは、ポルノや政治などの機密監査コンテンツが含まれていると、生成エラーが発生する可能性があります。フィルター リストを設定することで、モデルに送信する前に関連する単語を自動的に削除できます。", "lab_btn_generate_tags": "概要タグ", "lab_text_generating_tags": "タグを生成しています... \nLLM サービスによっては、5～20 秒かかる場合があります。", "lab_text_generating_tags_fail": "タグを生成できませんでした: {plain_text}", "rs_text_need_to_restart_after_save_setting": "このページの設定を保存した後、Windrecorder を再起動する必要があります。", "rs_md_title": "### 📼 録画とビデオストレージ", "rs_md_record_setting_title": "#### 録画オプション", "rs_checkbox_start_record_when_startup": "起動時に自動的にAppを開始する", "rs_checkbox_start_record_when_startup_help": "このオプションにチェックを入れると、start_app.bat のショートカットが作成されて、システムのスタートアップディレクトリに配置されます。このオプションにより、一部のセキュリティソフトウェアが start_webui.bat をウイルスのように誤検出する可能性がありますが、隔離エリアから取り除いて信頼できるソフトウェアとしてマークすることができます。また、手動で start_app.bat のショートカットを作成し、システムのスタートアップディレクトリに配置することもできます。", "rs_checkbox_is_start_recording_on_start_app": "アプリケーションの起動後に自動的に録画を開始します", "rs_input_stop_recording_when_screen_freeze": "画面が数分間変化しないときは、次のビデオスライスの録画を一時停止する（0 は永続停止しない）", "rs_text_skip_recording_by_wintitle": "フロントエンド アプリケーションのタイトル/OCR に次の単語のいずれかが含まれている場合、記録クリップはスキップされ、インデックス付けも表示もされません。", "rs_tag_input_tip": "追加", "rs_text_ocr_manual_update": "手動更新のみで、自動更新しない", "rs_text_ocr_auto_update": "ビデオスライスの録画が完了すると自動的にインデックス化（おすすめ）", "rs_selectbox_ocr_strategy": "OCR インデックス戦略", "rs_input_vid_store_time_help": "0 は永久に削除されません。ビデオ ファイルがクリーンアップされ、インデックス付きデータが残り、取得できる場合でも、それは単に映像が欠けているだけです。", "rs_input_vid_compress_time": "元ビデオを数日間保持してから圧縮", "rs_input_vid_compress_time_help": "0 は圧縮されません。", "rs_selectbox_compress_ratio": "元の画面サイズの", "rs_selectbox_compress_ratio_help": "0.75 倍の縮尺で元のビデオ容量の 1/4 まで圧縮できます。0.5 倍は 1/8、0.25 倍は 1/16 まで。ビデオの内容によっては、圧縮比率が異なります。", "rs_checkbox_enable_half_res_while_hidpi": "「高 DPI/高解像度画面」の場合、録画時に 4 分の 1 の解像度にスケールします。", "rs_text_compress_encoder": "エンコード方式", "rs_text_compress_accelerator": "エンコード アクセラレータ", "rs_text_compress_CRF": "画面品質 CRF", "rs_text_compress_CRF_help": "CRF は Constant Rate Factor の略で、ビデオ エンコーディングの品質とビット レート制御を設定するために使用されます。Windrecorder は圧縮率を高めるためにデフォルトで 39 に設定されています。ffmpeg では、CRF の値の範囲は使用するエンコーダによって異なります。x264 エンコーダの場合、CRF 値の範囲は 0 ～ 51 です。0 はロスレスを意味し、23 はデフォルト値、51 は最悪の品質を意味します。値が低いほど高品質を意味しますが、結果はファイル サイズが大きくなります。通常、x264 エンコーダの適切な値の範囲は 18 ～ 28 です。x265 エンコーダの場合、デフォルトの CRF 値は 28 です。libvpx エンコーダの場合、CRF 値の範囲は 0 ～ 63 です。一般に、CRF 値が小さいほど、 、ビデオ品質は高くなりますが、それに応じてファイル サイズも大きくなります。", "rs_text_compress_cpu_threads": "エンコードに使用する CPU スレッド数", "rs_text_compress_cpu_threads_help": "値を大きくすると圧縮速度が向上する可能性がありますが、CPU 使用率が高くなります。デフォルトは利用可能な CPU コア数の 1/4 です。", "rs_text_estimate_hint": "15 分ごとに録画される推定ビデオ サイズ: {min}Mb ~ {max}Mb (特定の録画コンテンツとモニターの数によって異なります)。", "rs_btn_encode_benchmark": "サポートされているエンコード方式をテストする ♘", "rs_text_encode_benchmark_loading": "テスト中は約 1 分かかります...", "rs_text_support": "サポート", "rs_text_compress_ratio": "圧縮率 ❓", "rs_text_compress_ratio_help": "圧縮率 = 圧縮ビデオ ファイルの容量 / 元のビデオ ファイルの容量。ここでのテスト ファイルは短いため、このインジケーターの表示には大きな誤差が生じる可能性があります。ビデオ時間が増加するにつれて、圧縮率は比較的高い。テスト用に __assets__\\test_video_compress.mp4 ファイルを置き換えることができます。", "rs_text_compress_time": "圧縮時間 (秒)", "rs_text_record_strategy_option_all": "すべての表示を記録します (合計 {num})", "rs_text_record_strategy_option_single": "1 つのモニターのみを録画します", "rs_text_record_range": "画面録画範囲", "rs_text_record_single_display_select": "レコード表示のみ:", "rs_text_show_encode_option": "高度なエンコード オプションを表示", "rs_text_record_encoder": "録音エンコーダー", "rs_text_record_help": "同じボリュームでより良い画質を得るには h265/AV1 を選択してください。ただし、録音および再生時のパフォーマンスが低下する可能性があります。(サポートされている場合は、ハードウェア アクセラレーションが自動的に有効になります)", "rs_text_record_bitrate": "録音ビットレート(kbps)", "rs_text_bitrate_help": "ビデオのビットレートとは、ビデオ データの送信速度または処理速度を指し、ビデオの品質とファイル サイズに影響します。具体的には、ビットレートが高いほどビデオの品質は向上しますが、対応するファイルのサイズも異なります。逆に、ビットレートが低い場合は、同じ解像度でもビデオ品質がぼやけたり歪んだりする可能性があります。画像サイズを小さくするために、デフォルトは 200kbps です。", "rs_text_hevc_tips": "Microsoft Edge / Firefox などのブラウザーで HEVC (h265) ビデオを再生するには、最初に HEVC ビデオ拡張機能をインストールする必要がある場合があります: https://apps.microsoft.com/detail/9NMZLZ57R3T7", "rs_text_record_mode_option_ffmpeg": "ビデオを直接録画します (FFmpeg を使用)", "rs_text_record_mode_option_screenshot_array": "自動柔軟なスクリーンショット (MSS を使用)", "rs_text_record_mode": "録音モード", "rs_input_screenshot_interval_second": "スクリーンショット解析間隔 (秒)", "rs_checkbox_record_screenshot_method_capture_foreground_window_only": "前景のアクティブ ウィンドウのスクリーンショットのみ", "rs_text_screenshot_interval_second_help": "リアルタイム分析のパフォーマンスを確保するには、スクリーンショットの間隔は各フレームの計算および分析時間よりも長くする必要があるため、3 秒以上に制限されます。", "rs_text_ffmpeg_help": "- コンピュータアクティビティのビデオを完全に録画し、高いパフォーマンスを実現したいユーザーに適しています。\n- インデックス作成中にカスタム ウィンドウとコンテンツをスキップできますが、関連する画像は記録される可能性がありますが、取得することはできません。\n n- 記録はより多くのメモリを占有し、記録が完了してインデックスが作成されると、より多くのシステム リソースを占有する可能性があります。\n- トレースバックには最大 15 分の遅延が発生します。", "rs_text_screenshot_array_help": "- メモリの手がかりを保存、呼び出し、検索するほとんどのユーザーに適しています。\n- 占有するシステム リソースが少なく、リアルタイムで分析し、変更された画像のみにインデックスを作成します。\n- 記録された画像はすぐに呼び出すことができ、完了後に自動的にビデオに変換されます。\n - カスタマイズされたウィンドウとコンテンツは正確にスキップでき、前景ウィンドウのみを記録できます。", "rs_checkbox_is_record_system_sound": "システムサウンドを同時に録音します", "rs_checkbox_record_deep_linking": "ブラウザの現在のページ リンクを記録します", "rs_help_record_deep_linking": "Chrome、Microsoft Edge、Firefox が現在閲覧しているページ アドレスの自動記録をサポートします。ブラウザがスタックしていると感じる場合は、このオプションをオフにしてみてください。", "rs_text_energy_saving": "スクリーンショットをビデオに結合する場合", "rs_text_energy_saving_help": "ストレージと再生のエクスペリエンスを確保するために、コンピューターのパフォーマンスが不十分で、プログラムの使用率が高いためにコンピューターが停止している場合、アイドル時にのみビデオの合成に調整することをお勧めします。 `cache_screenshot` 内のスクリーンショットは、キャッシュできません。時間内にビデオに合成できます。これは、`extension/manually_convert_screenshot_cache_into_video` 内のスクリプトを通じて手動で合成できます。", "rs_option_energy_saving_instantly": "クリップが記録されたらすぐに合成します", "rs_option_energy_saving_plug": "すぐに合成しますが、接続されている場合のみ (ラップトップのみ)", "rs_option_energy_saving_idle": "コンピューターがアイドル状態で電源に接続されている場合のみ", "set_md_title": "### ⚙️ 設定", "set_md_index_db": "#### データベースインデックス\n", "set_checkbox_shutdown_after_updated": "更新が完了したらコンピューターをシャットダウン", "set_selectbox_local_ocr_engine": "ローカルOCRエンジン", "set_selectbox_ocr_lang": "メインの OCR 言語", "set_help_ocr_lang_windows_ocr_engine": "システム設定 - 言語で言語パックを追加して、対応する OCR 言語サポートを追加できます。", "set_help_ocr_lang_third_party_engine": "一部のサードパーティ OCR エンジンはカスタムの主言語をサポートしていない場合があります。", "set_selectbox_local_ocr_engine_help": "Windows に搭載されている Windows.Media.Ocr.Cli を使用することをお勧めします。処理速度が速く、性能の消費が低いです。必要に応じて、サードパーティの OCR エンジンを拡張機能ディレクトリにインストールできます。精度は高くなりますが、通常はより多くのシステム リソースを消費し、識別に時間がかかります。", "set_md_ocr_ignore_area": "**OCR時に画面の四辺の領域を無視する**", "set_md_ocr_ignore_area_help": "数字はパーセンテージです。例えば、「6」は 6% です。このオプションにより、OCR 時に画面の四辺の要素が無視されます。これには、ブラウザのタブバー、Windows のスタートメニュー、Web ページ内のボーダー広告情報などがあります。前景ウィンドウのみを記録する場合、このオプションは各ウィンドウを比例してマスクします。", "set_toggle_use_screenshot_as_refer": "現在のスクリーンショットを参照として使用する", "set_text_choose_displays": "ディスプレイを選択", "set_text_top_padding": "上マージン", "set_text_bottom_padding": "下マージン", "set_text_left_padding": "左マージン", "set_text_right_padding": "右マージン", "set_checkbox_enable_3_columns_in_oneday": "「One Day」では 3 列の中央レイアウトが使用されます", "set_help_enable_3_columns_in_oneday": "3 列レイアウトは、大型の WebUI 表示デバイスに適しており、より多くの情報を提供し、トレースバック画面を中央に配置できます。デフォルトの 2 列レイアウトは、小型の WebUI 表示デバイスに適しています。", "set_checkbox_use_similar_zh_char_to_search": "中国語の類似文字を検索に使用する", "set_checkbox_use_similar_zh_char_to_search_help": "OCRが似た文字を誤認識することがあります。このオプションを有効にすると、キーワードに似た文字の形状も同時に検索され、結果の命中率が向上します。", "set_input_wordcloud_filter": "以下の単語をワードクラウド生成時にフィルタリングする：", "set_input_wordcloud_filter_help": "いくつかのUIの単語は常に表示されるため、データ中でより頻繁に現れます。ここでは、それらをワードクラウドの統計範囲外に除外することができます。", "set_input_oneday_timeline_thumbnail_num": "「一日のタイムライン」の横方向サムネイル数", "set_input_oneday_timeline_thumbnail_num_help": "「一日のタイムライン」のサムネイル密度を高めたい場合は、この値を増やすことができます。", "set_text_video_not_index": "{nocred_count} 個のビデオがインデックス作業待ちです。予想所要時間：<{timeCostStr}>。 （ディスク上には {count} 個のファイルがあります。）", "set_text_no_video_need_index": "新しいビデオは現在インデックスする必要がありません。 （ディスク上には {count} 個のファイルがあります。）", "set_text_one_video_to_index": "1 つのビデオが録画中であり、新しいビデオは現在インデックスする必要がありません。 （ディスク上には {count} 個のファイルがあります。）", "set_text_some_video_will_be_index": "{nocred_count} 個のビデオが自動的にインデックスされます。 （ディスク上には {count} 個のファイルがあります。）", "set_btn_update_db_manual": "✦ データベースを手動で更新する", "set_text_updating_db": "データベースを更新中です。操作やページのリフレッシュはしないでください。更新プロセスはバックグラウンドのコマンドラインインターフェースで確認できます。予想所要時間：{estimate_time_str}", "set_text_db_updated_successful": "データベースの更新が成功しました！所要時間：{timeCostStr}", "set_btn_got_it": "了解しました", "set_md_gui": "#### インタフェース\n", "set_md_auto_maintain": "#### 自動メンテナンス", "set_input_video_hold_days": "ビデオファイルの保持日数", "set_input_max_num_search_page": "グローバル検索で表示される各ページの最大結果数", "set_update_latest": "現在のバージョンは最新です。", "set_update_checking": "更新を確認中...", "set_update_new": "\n💫 新しいバージョン {tool_version} が見つかりました。アップデートするには、ディレクトリ内の `install_update.bat` を開いてください。", "set_update_changelog": "\n [変更ログ](https://github.com/yuka-friends/Windrecorder/blob/main/CHANGELOG.md)", "set_update_fail": "更新を確認できませんでした。{e}", "set_pwd_text": "<PERSON>ui アクセス パスワード (無効にする場合は空白のままにします)", "set_pwd_help": "この設定を有効にすると、webui にアクセスするときにパスワードの入力を求められます。この設定はデータを暗号化しませんが、同じ LAN 内の見慣れないユーザーによるアクセスを避けるために webui への入り口を保護するだけです。", "set_pwd_forget_help": "パスワードをお忘れですか? config_user.json の webui_access_password_md5 項目を削除してリセットしてください。", "set_checkbox_enable_img_emb": "画像のセマンティック検索を有効にする", "set_text_enable_img_emb_help": "画像意味検索は、コンピューター ビジョン テクノロジーによる画像の意味内容に基づく画像検索方法です。画像内容の意味記述に基づいて、大規模画像データベースからクエリを取得できます。Windrecorder はインデックスの埋め込みに uform3-image-text-multilingual-base を使用しますが、その機能は期待どおりではない可能性があります。このオプションをオンにすると、プログラムは空き時間にビデオの画像埋め込みインデックスを構築し、グローバルに検索できるようになります。プログラム ディレクトリを介して、extension/index_img_embedding_for_all_videofilesスクリプトのインデックスが手動で作成されます。", "set_text_img_emb_not_suppport_cuda": "お使いのデバイスは CUDA をサポートしていないようです。CPU を使用してイメージのセマンティック インデックスを作成するとパフォーマンスが低下する可能性があります。", "set_input_img_emb_max_recall_count": "自然セマンティック検索で各データベースから呼び出される結果の最大数", "set_text_help_img_emb_max_recall_count": "ナチュラル セマンティック検索では、指定された数の結果が期間ごとにデータベースから呼び出されます。数値が多すぎたり低すぎたりすると、クエリの精度が低下する可能性があります。", "set_input_day_begin_minutes": "「一日」毎日のタイムラインの最も早い時間", "set_help_day_begin_minutes": "毎日をゼロで割るのではなく、午前 0 時以降の一定期間のレコードを同じ自然な日に分割できます。", "set_checkbox_synonyms_recommand": "検索時に同義語を推奨する", "set_help_synonyms_recommand": "言語がサポートされていない場合、有効になりません。config_src\\synonyms でサポートされている言語を確認してください。", "set_checkbox_reduce_same_content_at_different_time": "ビデオ スライスに同じコンテンツが異なる時点で表示されている場合、最初のコンテンツのみが記録されます。", "set_checkbox_recycle_deleted_files": "ファイルを削除する必要がある場合は、直接削除するのではなく、ごみ箱に入れてください。", "set_help_recycle_deleted_files": "Windrecorder は実行中に一時的なビデオまたはスクリーンショットを生成します。データのセキュリティのため、破棄されたデータは直接削除するのではなく、デフォルトでごみ箱に入れられ、システムのごみ箱は定期的に自動的にクリーンアップされます。ディスク容量が少ない場合や、古いデータを直接削除したい場合は、このオプションをオフにしてください。すべてのファイル削除操作は cache\\logs に記録されます。", "qs_header_1": "Windrecorder {__version__} へようこそ", "qs_header_2": "ダウンロードしていただきありがとうございます。このクイック ウィザードはセットアップに役立ちます。設定は後でアプリで調整できます。", "qs_la_text_same_as_previous": "インターフェイス言語は以前と同じで日本語です。", "qs_un_set_your_username": "データベースの識別子として使用するユーザー名を設定してください。", "qs_un_describe": "Enterキーを押すと現在のユーザー名（{sys_username}）を使用することもできます。または、カスタムのユーザー名（20文字以内）を入力することもできます。", "qs_un_longer_than_expect": "制限の長さを超えています（半角英数字20文字/全角日本語10文字）", "qs_un_use_current_name": "現在のユーザー名を使用する：{sys_username}", "qs_un_use_custom_name": "カスタムのユーザー名を使用する：{your_username}", "qs_olang_intro": "システムには次の言語がインストールされています。OCR 検出のメイン言語として 1 つを選択してください:", "qs_olang_ocrlang_set_to": "OCR 検出のメイン言語は次のように設定されました:", "qs_olang_error": "入力は数値であり、オプションの範囲内である必要があります。", "qs_olang_one_choice_default_set": "システム言語は {os_support_lang} で、OCR 検出のメイン言語として適用されています。\n(必要な言語ではありませんか? 選択した言語の入力方式/言語パックがコンピュータにインストールされているかどうかを確認してください)システム。\n(https://learn.microsoft.com/en-us/uwp/api/windows.media.ocr)", "qs_ocr_title": "OCRエンジンのテスト結果：\n", "qs_ocr_result_describe": "精度：{accuracy}、認識時間：{timecost}、15分のビデオの索引作成に約かかる時間：{timecost_15}", "qs_ocr_describe": "> より高速で性能消費が低い、システム標準のWindows.Media.Ocr.Cliを使用することをお勧めします。\n", "qs_ocr_describe_disable_clo": "> chineseocr_lite_onnx は互換性の問題により無効になっています。\n (user_config.json の 'enable_ocr_chineseocr_lite_onnx':true を変更することで有効になります。)", "qs_ocr_tips_low_accuracy": "！ 精度低すぎませんか？ 選択した言語の入力方式/言語パックがシステムにインストールされているかどうかを確認してください。\n（https://learn.microsoft.com/en-us/uwp/api/windows.media.ocr）", "qs_ocr_cta": "画面のコンテンツを抽出する際のOCRエンジンを選択してください：", "qs_ocr_option_recommand": "（おすすめ＆デフォルト）", "qs_ocr_engine_chosen": "使用されるOCRエンジン：", "qs_ocr_not_has_lang_test_support": "現在選択されている言語に適したテスト ケースが見つからなかったため、en-US に対してテストされます。 ただし、OCR は通常どおり機能して言語を認識できますが、ウィザードには適切なテストが行​​われていないため、認識率が実際の状況を反映していない可能性がありますので、ご安心ください。", "qs_et_describe": "Windrecorder にはいくつかの拡張機能も用意されており、後で拡張機能ディレクトリにインストールして使用できます。", "qs_end_describe": "おめでとう！ すべての初期設定が完了しました。 心配しないでください。設定はアプリ内でいつでも調整できます。 \n\nこれで、ディレクトリ内の [start_app.bat] を開いて使用を開始できます。 \n", "qs_end_slogan": "> 一緒に、あなたの目が見た、風のように過ぎ去るものをキャプチャしましょう。", "qs_end_feedback": "> 問題が発生した場合やフィードバックの提案がある場合は、https://github.com/yuka-friends/Windrecorder でissueやPRを提出してください。", "qs_record_current_record": "現在の録画設定:", "qs_record_record_range_select": "録音範囲を次のようにします: (数値を入力して Enter キーを押して確認します。現在の設定を継承するには Enter キーを直接押します)", "qs_record_all_display": "すべての表示を記録する", "qs_record_single_display": "単一の表示のみを記録します", "qs_record_full_display": "ディスプレイ全体を記録します {幅}x{高さ}", "qs_record_foreground_window": "前景のアクティブなウィンドウのみを記録します", "qs_record_that_display": "記録したいディスプレイを 1 つ選択してください: (番号を入力し、Enter キーを押して確認します)", "qs_record_mode": "録音にどの方法を使用しますか? (番号を入力し、Enter キーを押して確認します)", "qs_record_ffmpeg_help": "中程度のリソースを消費し、再生遅延は最大 15 分です。コンピューターのアクティビティをスムーズかつ完全に記録し、画面を詳細に分析できます。前景ウィンドウのみの記録はサポートされていません。", "qs_record_screenshot_array_help": "リソース使用量が少なく、即時再生が可能です。記憶の手がかりを保存、呼び出し、検索するほとんどのユーザーに適しています。", "qs_record_mode_set_to": "記録モードは次のように設定されています:", "qs_record_auto_set": "モニターの解像度は画面が録画されるたびに自動的に認識されるため、追加の設定は必要ありません。", "qs_ocr_benchmark_testcase": "テスト言語: {lang}\n{indentation}テスト ケース: {test_set}", "qs_ocr_benchmark_tabletitle": ["OCR エンジン", "単一画像認識時間 (秒)", "精度率"], "qs_ocr_benchmark_warning": "* {ocr_engines} の各単一画像認識時間が現在のスクリーンショット間隔 {screenshot_interval_second}s を超える可能性があり、記録に遅延が生じる可能性があることに注意してください。スクリーンショット間隔の長さを単一画像認識に設定することをお勧めします。 2 倍以上の時間 (フレキシブル スクリーンショット モード使用時)", "tray_add_flag_mark_note_for_now": "🚩 とりあえずマークを追加", "tray_webui_start": "🦝 検索/設定インターフェイスを開始します", "tray_webui_exit": "🦝 検索/設定インターフェイスを閉じます", "tray_webui_address": "🦝 ページに入るにはブラウザから {address_port} にアクセスしてください", "tray_webui_address_network": "      Network access: {address_port}", "tray_record_start": "▶️ 録音開始", "tray_record_stop": "⏸️ 録音を一時停止します", "tray_update_cta": "🚀 新しいバージョンに更新します: {version}", "tray_version_info": "🚀 現在は最新バージョン {version} です", "tray_updatelog": "🚀 更新ログを表示", "tray_exit": "❌ 終了", "tray_notify_title": "Windrecorder が録音中です", "tray_notify_title_record_pause": "Windrecorder は録音を一時停止しました", "tray_notify_text": "トレイの右クリック メニューを使用して、録画を一時停止し、メモリを巻き戻します", "tray_notify_text_start_without_record": "トレイの右クリック メニューを使用して記録を開始し、メモリを巻き戻します", "tray_tip_record": "Windrecorder - Recording", "tray_tip_record_pause": "Windrecorder - Recording Pause", "tray_text_already_run": "Windrecorder はシステムトレイですでに実行されています。", "flag_text_mark_added": "今回のマークが追加されました。", "flag_input_note": "メモ:", "flag_btn_remove_mark": "マークを削除", "flag_btn_add_note": "メモを追加", "lb_text_generated": "画像を生成中です。お待​​ちください", "lb_text_month_range": "おおよその月の範囲", "lb_text_exact_date": "正確な日付範囲", "lb_text_data_selector": "日付セレクター", "lb_text_distributeavg": "既存のデータから均等に分散します", "lb_text_timeavg": "絶対時間範囲で分散", "lb_text_thumbnail_mode_select": "サムネイル配信モード", "lb_text_width_thumbnail_num": "水平方向のサムネイルの数", "lb_text_height_thumbnail_num": "縦方向のサムネイルの数", "lb_tip_distributeavg": "サムネイルの合計数が含まれます: {num}。データベースに十分なサムネイルがない場合、生成は失敗する可能性があります。", "lb_tip_timeavg": "サムネイル グリッドの合計数が含まれます: {num}。", "lb_text_custom_lightbox_width": "画像の幅 (ピクセル) を生成", "lb_checkbox_add_watermark": "ライトボックス画像に下透かしを追加する", "lb_btn_create_img": "イメージを作成", "lb_text_create_img_instruction": "生成された結果はフォルダー {cache_dir} にあります。", "lb_text_title": "#### 📔 カスタム ライト ボックス ジェネレーター", "bg_text_no_image": "画像が選択されていません", "bg_text_disable_bg": "カスタム背景画像がオフになりました", "bg_title": "### 🖼️ カスタム Webui 背景画像", "bg_text_opacity": "不透明度", "bg_btn_remove_bg": "背景画像を削除", "bg_btn_set_bg": "背景画像として設定", "bg_btn_preview": "プレビュー効果", "bg_text_not_existed": "カスタム背景画像 {custom_background_filepath} が存在しません。背景画像は無効になっています。"}, "lang_map": {"en": "English", "sc": "简体中文", "ja": "日本語"}}