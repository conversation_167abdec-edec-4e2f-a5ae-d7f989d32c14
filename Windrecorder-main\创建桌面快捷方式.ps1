# 创建 Windrecorder 桌面快捷方式
Write-Host "正在创建 Windrecorder 桌面快捷方式..." -ForegroundColor Green

# 获取当前脚本目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# 获取桌面路径
$Desktop = [Environment]::GetFolderPath("Desktop")

# 创建 WScript.Shell 对象
$WshShell = New-Object -comObject WScript.Shell

# 创建主程序快捷方式
$Shortcut1 = $WshShell.CreateShortcut("$Desktop\Windrecorder 启动器.lnk")
$Shortcut1.TargetPath = "$ScriptDir\启动Windrecorder.bat"
$Shortcut1.WorkingDirectory = $ScriptDir
$Shortcut1.IconLocation = "$ScriptDir\__assets__\icon-tray.ico"
$Shortcut1.Description = "Windrecorder 屏幕记录和搜索工具"
$Shortcut1.Save()

# 创建Web UI快捷方式
$Shortcut2 = $WshShell.CreateShortcut("$Desktop\Windrecorder WebUI.lnk")
$Shortcut2.TargetPath = "$ScriptDir\start_webui_only.bat"
$Shortcut2.WorkingDirectory = $ScriptDir
$Shortcut2.IconLocation = "$ScriptDir\__assets__\icon-tray.ico"
$Shortcut2.Description = "Windrecorder Web Search Interface"
$Shortcut2.Save()

# 创建直接访问Web界面的URL快捷方式
$UrlShortcut = $WshShell.CreateShortcut("$Desktop\Windrecorder Web.url")
$UrlShortcut.TargetPath = "http://localhost:8501"
$UrlShortcut.Save()

Write-Host "✓ 桌面快捷方式创建完成！" -ForegroundColor Green
Write-Host ""
Write-Host "Created shortcuts:" -ForegroundColor Yellow
Write-Host "1. Windrecorder Launcher - Complete startup menu"
Write-Host "2. Windrecorder WebUI - Direct Web interface"
Write-Host "3. Windrecorder Web - Direct web access (requires running program)"
Write-Host ""
Write-Host "Press any key to continue..."
Read-Host
