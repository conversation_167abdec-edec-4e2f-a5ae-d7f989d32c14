@echo off
title Windrecorder Launcher
color 0e

:start
echo.
echo ========================================
echo           Windrecorder Launcher
echo ========================================
echo.
echo Please select startup mode:
echo.
echo [1] Start full program (background recording + Web UI)
echo [2] Start background recording only
echo [3] Start Web UI only
echo [4] Check running status
echo [5] Stop all programs
echo [0] Exit
echo.
set /p choice=Please enter option (0-5):

if "%choice%"=="1" goto start_all
if "%choice%"=="2" goto start_main
if "%choice%"=="3" goto start_webui
if "%choice%"=="4" goto check_status
if "%choice%"=="5" goto stop_all
if "%choice%"=="0" goto exit
goto invalid

:start_all
echo.
echo Starting full program...
echo 1. Starting background recording...
start /min "" "%~dp0start_app.bat"
timeout /t 3 /nobreak >nul
echo 2. Starting Web UI...
start /min "" "%~dp0start_webui_only.bat"
echo.
echo Program started successfully!
echo   - Background recording is running in system tray
echo   - Web UI will open in browser automatically
echo   - To access Web UI manually, visit: http://localhost:8501
goto end

:start_main
echo.
echo Starting background recording...
start /min "" "%~dp0start_app.bat"
echo Background recording started, please check system tray
goto end

:start_webui
echo.
echo Starting Web UI...
start "" "%~dp0start_webui_only.bat"
goto end

:check_status
echo.
echo Checking program status...
echo.
tasklist | findstr python.exe >nul
if %errorlevel%==0 (
    echo Python processes are running:
    tasklist | findstr python.exe
) else (
    echo No Python processes found
)
echo.
netstat -an | findstr :8501 >nul
if %errorlevel%==0 (
    echo Web UI port 8501 is listening
) else (
    echo Web UI is not started
)
goto end

:stop_all
echo.
echo Stopping all programs...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1
echo All programs stopped
goto end

:invalid
echo.
echo Invalid option, please try again
timeout /t 2 /nobreak >nul
goto start

:end
echo.
pause

:exit
exit
