@echo off
chcp 65001 >nul
title Windrecorder 启动器
color 0e

echo.
echo ========================================
echo           Windrecorder 启动器
echo ========================================
echo.
echo 请选择启动方式：
echo.
echo [1] 启动完整程序（后台录制 + Web界面）
echo [2] 仅启动后台录制程序
echo [3] 仅启动Web界面
echo [4] 查看运行状态
echo [5] 停止所有程序
echo [0] 退出
echo.
set /p choice=请输入选项 (0-5): 

if "%choice%"=="1" goto start_all
if "%choice%"=="2" goto start_main
if "%choice%"=="3" goto start_webui
if "%choice%"=="4" goto check_status
if "%choice%"=="5" goto stop_all
if "%choice%"=="0" goto exit
goto invalid

:start_all
echo.
echo 正在启动完整程序...
echo 1. 启动后台录制程序...
start /min "" "%~dp0start_app.bat"
timeout /t 3 /nobreak >nul
echo 2. 启动Web界面...
start /min "" "%~dp0start_webui_only.bat"
echo.
echo ✓ 程序启动完成！
echo   - 后台录制程序已在系统托盘运行
echo   - Web界面将在浏览器中自动打开
echo   - 如需手动访问Web界面，请访问: http://localhost:8501
goto end

:start_main
echo.
echo 正在启动后台录制程序...
start /min "" "%~dp0start_app.bat"
echo ✓ 后台录制程序已启动，请查看系统托盘
goto end

:start_webui
echo.
echo 正在启动Web界面...
start "" "%~dp0start_webui_only.bat"
goto end

:check_status
echo.
echo 检查程序运行状态...
echo.
tasklist | findstr python.exe >nul
if %errorlevel%==0 (
    echo ✓ Python进程正在运行
    tasklist | findstr python.exe
) else (
    echo ✗ 未发现Python进程
)
echo.
netstat -an | findstr :8501 >nul
if %errorlevel%==0 (
    echo ✓ Web界面端口8501正在监听
) else (
    echo ✗ Web界面未启动
)
goto end

:stop_all
echo.
echo 正在停止所有程序...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im pythonw.exe >nul 2>&1
echo ✓ 所有程序已停止
goto end

:invalid
echo.
echo ✗ 无效选项，请重新选择
timeout /t 2 /nobreak >nul
goto start

:end
echo.
pause

:exit
exit
